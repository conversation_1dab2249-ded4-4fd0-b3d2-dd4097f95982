<?php
header('Content-Type: application/json');
include 'loadenv.php';

try {
    // Connect to the database
    $db = new PDO('mysql:host=' . $_ENV["DB_SERVER"] . ';dbname=db_nagiosql_v3', $_ENV["DB_USER"], $_ENV["DB_PASSWORD"]);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get POST data
    $hostgroup_name = $_POST['hostgroup_name'] ?? '';
    $alias = $_POST['alias'] ?? $hostgroup_name;
    $config_id = $_POST['config_id'] ?? 1;
    $notes = $_POST['notes'] ?? ''; // Add notes field with default empty string
    $notes_url = $_POST['notes_url'] ?? ''; // Add notes_url field with default empty string
    $action_url = $_POST['action_url'] ?? ''; // Add action_url field with default empty string

    // Validate input
    if (empty($hostgroup_name)) {
        throw new Exception('Hostgroup name cannot be empty');
    }
    
    // Validate hostgroup_name characters - only allow letters, numbers, spaces, and hyphens
    if (!preg_match('/^[a-zA-Z0-9\s-]+$/', $hostgroup_name)) {
        throw new Exception('Hostgroup name contains invalid characters. Only letters, numbers, spaces, and hyphens (-) are allowed.');
    }

    // Prepare and execute SQL - now including all required fields
    $stmt = $db->prepare('INSERT INTO tbl_hostgroup (hostgroup_name, alias, config_id, notes, notes_url, action_url, last_modified) VALUES (:hostgroup_name, :alias, :config_id, :notes, :notes_url, :action_url, NOW())');
    $stmt->bindParam(':hostgroup_name', $hostgroup_name);
    $stmt->bindParam(':alias', $alias);
    $stmt->bindParam(':config_id', $config_id);
    $stmt->bindParam(':notes', $notes);
    $stmt->bindParam(':notes_url', $notes_url);
    $stmt->bindParam(':action_url', $action_url);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Hostgroup added successfully']);
    } else {
        throw new Exception('Failed to add hostgroup');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>