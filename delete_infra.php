<?php
require_once 'loadenv.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Check if infra parameter is provided
if (!isset($_POST['infra']) || empty($_POST['infra'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Infrastructure name is required']);
    exit;
}

$infraName = trim($_POST['infra']);

try {
    // Create database connection
    $db = new PDO('mysql:host=' . $_ENV["DB_SERVER"] . ';dbname=bubblemaps', $_ENV["DB_USER"], $_ENV["DB_PASSWORD"]);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Start transaction
    $db->beginTransaction();
    
    // First, check if there are any hosts in this infrastructure
    $checkHostsQuery = "SELECT COUNT(*) as host_count FROM hosts WHERE infra = ?";
    $checkHostsStmt = $db->prepare($checkHostsQuery);
    $checkHostsStmt->execute([$infraName]);
    $hostCount = $checkHostsStmt->fetch(PDO::FETCH_ASSOC)['host_count'];
    
    if ($hostCount > 0) {
        $db->rollBack();
        http_response_code(400);
        echo json_encode([
            'success' => false, 
            'message' => "Cannot delete infrastructure '$infraName' because it contains $hostCount host(s) (including blacklisted hosts). Please remove all hosts first."
        ]);
        exit;
    }
    
    // Check if infrastructure exists
    $checkInfraQuery = "SELECT id FROM infra WHERE name = ?";
    $checkInfraStmt = $db->prepare($checkInfraQuery);
    $checkInfraStmt->execute([$infraName]);
    $infraExists = $checkInfraStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$infraExists) {
        $db->rollBack();
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => "Infrastructure '$infraName' not found"]);
        exit;
    }
    
    // Delete all subnets associated with this infrastructure
    $deleteSubnetsQuery = "DELETE FROM subnets WHERE infra = ?";
    $deleteSubnetsStmt = $db->prepare($deleteSubnetsQuery);
    $deleteSubnetsStmt->execute([$infraName]);
    $deletedSubnets = $deleteSubnetsStmt->rowCount();
    
    // Delete the infrastructure
    $deleteInfraQuery = "DELETE FROM infra WHERE name = ?";
    $deleteInfraStmt = $db->prepare($deleteInfraQuery);
    $deleteInfraStmt->execute([$infraName]);
    $deletedInfra = $deleteInfraStmt->rowCount();
    
    // Commit transaction
    $db->commit();
    
    if ($deletedInfra > 0) {
        echo json_encode([
            'success' => true, 
            'message' => "Infrastructure '$infraName' deleted successfully. Removed $deletedSubnets subnet(s)."
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => "Failed to delete infrastructure '$infraName'"]);
    }
    
} catch (PDOException $e) {
    // Rollback transaction on error
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    
    error_log("Database error in delete_infra.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    
    error_log("Error in delete_infra.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'An error occurred']);
}
?>
