<?php
include "loadenv.php";

// Check if SPM module is available
$spm_check = shell_exec("php " . __DIR__ . "/checkmods.php spm 2>&1");
if (trim($spm_check) !== "true") {
    echo "SPM module is not available. Exiting script.\n";
    exit(1);
}

// Database configuration
define('DB_HOST', $_ENV["DB_SERVER"]);
define('DB_USER', $_ENV["DB_USER"]);
define('DB_PASS', $_ENV["DB_PASSWORD"]);
define('BUBBLEMAPS_DB', 'bubblemaps');
define('NAGIOSQL_DB', 'db_nagiosql_v3');
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);
define('RELATIONSHIPS_JSON_FILE', __DIR__ . '/conf/auto_relationships.json');

/**
 * Load automatically discovered relationships from JSON file
 */
function loadAutoRelationships() {
    if (!file_exists(RELATIONSHIPS_JSON_FILE)) {
        return [];
    }
    
    $content = file_get_contents(RELATIONSHIPS_JSON_FILE);
    if ($content === false) {
        error_log("Failed to read relationships JSON file");
        return [];
    }
    
    $data = json_decode($content, true);
    if ($data === null) {
        error_log("Failed to parse relationships JSON file");
        return [];
    }
    
    return $data;
}

/**
 * Save automatically discovered relationships to JSON file
 */
function saveAutoRelationships($relationships) {
    // Ensure the conf directory exists
    $confDir = dirname(RELATIONSHIPS_JSON_FILE);
    if (!is_dir($confDir)) {
        if (!mkdir($confDir, 0755, true)) {
            throw new Exception("Failed to create conf directory: $confDir");
        }
    }
    
    $jsonData = json_encode($relationships, JSON_PRETTY_PRINT);
    if ($jsonData === false) {
        throw new Exception("Failed to encode relationships to JSON");
    }
    
    if (file_put_contents(RELATIONSHIPS_JSON_FILE, $jsonData) === false) {
        throw new Exception("Failed to write relationships JSON file");
    }
    
    echo "Saved " . count($relationships) . " relationships to JSON file\n";
}

/**
 * Get relationships that were automatically discovered (from JSON) that exist in nagiosql
 */
function getAutoRelationshipsFromNagios($nagiosqlConn) {
    $autoRelationships = loadAutoRelationships();
    if (empty($autoRelationships)) {
        return [];
    }
    
    $hostMappings = getHostIdMappings($nagiosqlConn);
    $existingRelationships = [];
    
    foreach ($autoRelationships as $rel) {
        $parentIp = $rel['parent_ip'];
        $childIp = $rel['child_ip'];
        
        // Check if both hosts still exist in nagiosql
        if (isset($hostMappings[$parentIp]) && isset($hostMappings[$childIp])) {
            $parentId = $hostMappings[$parentIp];
            $childId = $hostMappings[$childIp];
            
            // Check if relationship exists in nagiosql
            $stmt = $nagiosqlConn->prepare("SELECT 1 FROM tbl_lnkHostToHost WHERE idMaster = ? AND idSlave = ?");
            $stmt->bind_param("ii", $childId, $parentId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $existingRelationships[] = [
                    'parent_id' => $parentId,
                    'child_id' => $childId,
                    'parent_ip' => $parentIp,
                    'child_ip' => $childIp
                ];
            }
            $stmt->close();
        }
    }
    
    return $existingRelationships;
}

/**
 * Get self IP address
 */
function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

/**
 * Simulate verify actions in NagiosQL
 */
function simulateVerifyActions($hostname) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        // Initialize cURL session with cookies
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
        ]);

        // Login to Nagios
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Login failed: ' . curl_error($ch));

        // Handle redirect
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        // Perform verification actions
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if(curl_errno($ch)) throw new Exception("Action $action failed");
        }
        
        curl_close($ch);
        echo "NagiosQL verification actions completed successfully.\n";
    } catch (Exception $e) {
        error_log("Error in simulateVerifyActions: " . $e->getMessage());
    }
}

/**
 * Get database connection for bubblemaps
 */
function getBubbleMapsConnection() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, BUBBLEMAPS_DB);
    if ($conn->connect_error) {
        throw new Exception("BubbleMaps DB connection failed: " . $conn->connect_error);
    }
    return $conn;
}

/**
 * Get database connection for nagiosql
 */
function getNagiosQLConnection() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, NAGIOSQL_DB);
    if ($conn->connect_error) {
        throw new Exception("NagiosQL DB connection failed: " . $conn->connect_error);
    }
    return $conn;
}

/**
 * Get all valid host IPs from bubblemaps in one query
 */
function getValidHostIps($bubbleMapsConn) {
    $result = $bubbleMapsConn->query("SELECT DISTINCT ip FROM hosts WHERE blacklist = 0 AND apmStatus != 'not-added' AND apmStatus != 'ask'");
    if (!$result) {
        throw new Exception("Failed to fetch hosts from bubblemaps: " . $bubbleMapsConn->error);
    }
    
    $ips = [];
    while ($row = $result->fetch_assoc()) {
        $ips[] = $row['ip'];
    }
    return $ips;
}

/**
 * Execute netdisco query for a specific switch IP - ORIGINAL WORKING VERSION
 */
function queryNetdiscoForSwitch($switchIp) {
    // Build the comprehensive Netdisco query including uplinks (same as original)
    $sql = <<<SQL
SELECT
    dp.ip AS parent_ip,
    dp.port AS port,
    COALESCE(
        (SELECT string_agg(ip::text, ', ') 
         FROM node_ip 
         WHERE mac = n.mac AND active = true
         GROUP BY mac),
        dp.remote_ip::text
    ) AS child_ip,
    CASE
        WHEN dp.remote_ip IS NOT NULL THEN 'Uplink'
        WHEN n.mac IS NOT NULL THEN 'Device'
        ELSE 'Unknown'
    END AS connection_type
FROM 
    device_port dp
LEFT JOIN 
    node n ON dp.ip = n.switch AND dp.port = n.port AND n.time_last > NOW() - INTERVAL '2 hours'
WHERE
    dp.up = 'up'
    AND dp.ip = '{$switchIp}'
    AND (
        -- Include recent device connections (nodes with MAC addresses)
        (n.mac IS NOT NULL AND EXISTS (
            SELECT 1 FROM node_ip 
            WHERE mac = n.mac AND active = true
        ))
        -- Include uplinks (remote_ip connections, no time restriction needed)
        OR dp.remote_ip IS NOT NULL
    )
ORDER BY 
    dp.ip, dp.port;
SQL;

    // Execute the query via psql, producing pipe-separated, header-less output for easy parsing
    $command = "sudo -u netdisco psql -d netdisco -t -A -F'|' -c " . escapeshellarg($sql);
    
    $output = shell_exec($command . ' 2>&1');
    
    if ($output === null) {
        throw new Exception("Failed to execute netdisco query for switch: $switchIp");
    }
    
    // Parse the output into structured data
    $lines = array_filter(explode("\n", trim($output)));
    $results = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '') {
            continue;
        }

        // Expected order: parent_ip|port|child_ip|connection_type
        $parts = explode('|', $line);

        if (count($parts) >= 4) {
            $switchIp = trim($parts[0]);
            $port = trim($parts[1]);
            $childIpRaw = trim($parts[2]);
            $connectionType = trim($parts[3]);
            
            // Handle multiple IPs separated by commas (from string_agg)
            $childIps = array_map('trim', explode(',', $childIpRaw));
            
            foreach ($childIps as $childIp) {
                // Remove CIDR notation (e.g., /32, /24) from IP addresses
                $childIp = preg_replace('/\/\d+$/', '', $childIp);
                
                // Validate IP addresses
                if (filter_var($switchIp, FILTER_VALIDATE_IP) && 
                    filter_var($childIp, FILTER_VALIDATE_IP) &&
                    $switchIp !== $childIp) {
                    
                    $results[] = [
                        'switch_ip'             => $switchIp, // parent_ip
                        'connected_device_ip'   => $childIp, // child_ip
                        'connection_type'       => $connectionType, // Uplink, Device, or Unknown
                    ];
                } else {
                    error_log("Skipping invalid IP pair: switch=$switchIp, device=$childIp");
                }
            }
        }
    }
    
    return $results;
}

/**
 * Check if a device IP exists in bubblemaps and meets criteria
 */
function isValidDevice($deviceIp, $bubbleMapsConn) {
    if (empty($deviceIp)) {
        return false;
    }
    
    $stmt = $bubbleMapsConn->prepare("SELECT id FROM hosts WHERE ip = ? AND blacklist = 0 AND apmStatus != 'not-added' AND apmStatus != 'ask'");
    if (!$stmt) {
        error_log("Failed to prepare statement: " . $bubbleMapsConn->error);
        return false;
    }
    
    $stmt->bind_param("s", $deviceIp);
    $stmt->execute();
    $result = $stmt->get_result();
    $exists = $result->num_rows > 0;
    $stmt->close();
    
    return $exists;
}

/**
 * Get all host mappings from nagiosql in one query
 */
function getHostIdMappings($nagiosqlConn) {
    $result = $nagiosqlConn->query("SELECT id, address FROM tbl_host");
    if (!$result) {
        throw new Exception("Failed to fetch host mappings: " . $nagiosqlConn->error);
    }
    
    $mappings = [];
    while ($row = $result->fetch_assoc()) {
        $mappings[$row['address']] = (int)$row['id'];
    }
    return $mappings;
}

/**
 * Remove only automatically discovered relationships from nagiosql
 */
function removeAutoRelationships($nagiosqlConn) {
    $autoRelationships = getAutoRelationshipsFromNagios($nagiosqlConn);
    if (empty($autoRelationships)) {
        echo "No automatically discovered relationships to remove\n";
        return 0;
    }
    
    echo "Removing " . count($autoRelationships) . " automatically discovered relationships...\n";
    
    $removedCount = 0;
    foreach ($autoRelationships as $rel) {
        $parentId = $rel['parent_id'];
        $childId = $rel['child_id'];
        
        // Remove the relationship
        $stmt = $nagiosqlConn->prepare("DELETE FROM tbl_lnkHostToHost WHERE idMaster = ? AND idSlave = ?");
        $stmt->bind_param("ii", $childId, $parentId);
        if ($stmt->execute()) {
            $removedCount++;
        }
        $stmt->close();
        
        // Check if child has any other parents, if not, set parents flag to 0
        $stmt = $nagiosqlConn->prepare("SELECT COUNT(*) as count FROM tbl_lnkHostToHost WHERE idMaster = ?");
        $stmt->bind_param("i", $childId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stmt->close();
        
        if ($row['count'] == 0) {
            $stmt = $nagiosqlConn->prepare("UPDATE tbl_host SET parents = 0 WHERE id = ?");
            $stmt->bind_param("i", $childId);
            $stmt->execute();
            $stmt->close();
        }
    }
    
    echo "Removed $removedCount automatically discovered relationships\n";
    return $removedCount;
}

/**
 * Bulk create parent-child relationships with optimized cycle detection
 */
function bulkCreateRelationships($relationships, $hostMappings, $nagiosqlConn) {
    if (empty($relationships)) {
        return 0;
    }
    
    // Build adjacency list for cycle detection
    $adjacencyList = [];
    $validRelationships = [];
    
    // First pass: validate relationships and build adjacency list
    foreach ($relationships as $rel) {
        $parentIp = $rel['switch_ip'];
        $childIp = $rel['connected_device_ip'];
        
        // Check if both hosts exist in nagiosql
        if (!isset($hostMappings[$parentIp]) || !isset($hostMappings[$childIp])) {
            continue;
        }
        
        $parentId = $hostMappings[$parentIp];
        $childId = $hostMappings[$childIp];
        
        // Initialize adjacency list if needed
        if (!isset($adjacencyList[$parentId])) {
            $adjacencyList[$parentId] = [];
        }
        if (!isset($adjacencyList[$childId])) {
            $adjacencyList[$childId] = [];
        }
        
        // Add relationship to adjacency list
        $adjacencyList[$parentId][] = $childId;
        
        $validRelationships[] = [
            'parent_id' => $parentId,
            'child_id' => $childId,
            'parent_ip' => $parentIp,
            'child_ip' => $childIp
        ];
    }
    
    // Detect cycles using topological sort
    $cycleFreeRelationships = detectAndRemoveCycles($validRelationships, $adjacencyList);
    
    if (empty($cycleFreeRelationships)) {
        return 0;
    }
    
    // Bulk insert all relationships
    $values = [];
    $childIds = [];
    
    foreach ($cycleFreeRelationships as $rel) {
        $values[] = "({$rel['child_id']}, {$rel['parent_id']})"; // child is master, parent is slave
        $childIds[] = $rel['child_id'];
    }
    
    // Start transaction for bulk operations
    $nagiosqlConn->begin_transaction();
    
    try {
        // Bulk insert relationships
        $insertSql = "INSERT INTO tbl_lnkHostToHost (idMaster, idSlave) VALUES " . implode(', ', $values);
        $nagiosqlConn->query($insertSql);
        
        // Bulk update parents flag for all child hosts
        if (!empty($childIds)) {
            $childIdsStr = implode(',', $childIds);
            $nagiosqlConn->query("UPDATE tbl_host SET parents = 1 WHERE id IN ($childIdsStr)");
        }
        
        $nagiosqlConn->commit();
        return count($cycleFreeRelationships);
        
    } catch (Exception $e) {
        $nagiosqlConn->rollback();
        throw $e;
    }
}

/**
 * Detect and remove cycles using topological sort
 */
function detectAndRemoveCycles($relationships, $adjacencyList) {
    // Calculate in-degrees
    $inDegree = [];
    foreach ($adjacencyList as $node => $neighbors) {
        if (!isset($inDegree[$node])) {
            $inDegree[$node] = 0;
        }
        foreach ($neighbors as $neighbor) {
            if (!isset($inDegree[$neighbor])) {
                $inDegree[$neighbor] = 0;
            }
            $inDegree[$neighbor]++;
        }
    }
    
    // Topological sort to detect cycles
    $queue = [];
    foreach ($inDegree as $node => $degree) {
        if ($degree == 0) {
            $queue[] = $node;
        }
    }
    
    $topologicalOrder = [];
    $processed = [];
    
    while (!empty($queue)) {
        $node = array_shift($queue);
        $topologicalOrder[] = $node;
        $processed[$node] = true;
        
        if (isset($adjacencyList[$node])) {
            foreach ($adjacencyList[$node] as $neighbor) {
                $inDegree[$neighbor]--;
                if ($inDegree[$neighbor] == 0) {
                    $queue[] = $neighbor;
                }
            }
        }
    }
    
    // If we can't process all nodes, there are cycles
    if (count($topologicalOrder) != count($adjacencyList)) {
        // Remove relationships that would create cycles
        $validRelationships = [];
        $processedNodes = array_flip($topologicalOrder);
        
        foreach ($relationships as $rel) {
            $parentId = $rel['parent_id'];
            $childId = $rel['child_id'];
            
            // Only keep relationships where both nodes are in the topological order
            if (isset($processedNodes[$parentId]) && isset($processedNodes[$childId])) {
                $validRelationships[] = $rel;
            }
        }
        
        return $validRelationships;
    }
    
    return $relationships;
}

/**
 * Main function to sync netdisco relationships - OPTIMIZED VERSION
 */
function syncNetdiscoParentChildRelationships() {
    echo "Starting optimized netdisco parent-child relationship sync...\n";
    $startTime = microtime(true);
    
    $bubbleMapsConn = getBubbleMapsConnection();
    $nagiosqlConn = getNagiosQLConnection();
    
    try {
        // Check if JSON file exists - if not, clear all relationships (first run behavior)
        if (!file_exists(RELATIONSHIPS_JSON_FILE)) {
            echo "JSON tracking file not found - clearing all existing parent-child relationships (first run)...\n";
            $nagiosqlConn->query("DELETE FROM tbl_lnkHostToHost");
            $nagiosqlConn->query("UPDATE tbl_host SET parents = 0");
        } else {
            // Remove only automatically discovered relationships (preserve manual ones)
            echo "Removing automatically discovered parent-child relationships...\n";
            removeAutoRelationships($nagiosqlConn);
        }
        
        // Get all valid host IPs from bubblemaps
        echo "Fetching valid host IPs from bubblemaps...\n";
        $validIps = getValidHostIps($bubbleMapsConn);
        echo "Found " . count($validIps) . " valid hosts\n";
        
        if (empty($validIps)) {
            echo "No valid hosts found. Exiting.\n";
            return;
        }
        
        // Debug: Show first few IPs
        echo "Sample IPs: " . implode(', ', array_slice($validIps, 0, 5)) . "\n";
        
        // Get all relationships from netdisco using original working approach
        echo "Querying netdisco for all relationships...\n";
        $allRelationships = [];
        $processedPairs = [];
        
        foreach ($validIps as $hostIp) {
            // Skip invalid IPs
            if (!filter_var($hostIp, FILTER_VALIDATE_IP)) {
                echo "Skipping invalid IP: $hostIp\n";
                continue;
            }
            
            try {
                echo "Querying netdisco for switch: $hostIp\n";
                $netdiscoResults = queryNetdiscoForSwitch($hostIp);
                
                if (empty($netdiscoResults)) {
                    continue; // No results for this switch
                }
                
                foreach ($netdiscoResults as $netdiscoRow) {
                    $switchIp = $netdiscoRow['switch_ip']; // parent_ip from Netdisco
                    $connectedDeviceIp = $netdiscoRow['connected_device_ip']; // child_ip from Netdisco
                    
                    if (empty($connectedDeviceIp)) {
                        continue; // No connected device IP
                    }

                    // Check if connected device exists in bubblemaps and meets criteria
                    if (isValidDevice($connectedDeviceIp, $bubbleMapsConn)) {
                        $pairKey = $switchIp . '|' . $connectedDeviceIp;
                        if (!isset($processedPairs[$pairKey])) {
                            $allRelationships[] = [
                                'parent' => $switchIp,
                                'child' => $connectedDeviceIp,
                                'type' => $netdiscoRow['connection_type']
                            ];
                            $processedPairs[$pairKey] = true;
                        }
                    }
                }
                
            } catch (Exception $e) {
                error_log("Error processing switch $hostIp: " . $e->getMessage());
            }
        }
        
        echo "Found " . count($allRelationships) . " relationships from netdisco\n";
        
        if (empty($allRelationships)) {
            echo "No relationships found in netdisco. Exiting.\n";
            return;
        }
        
        // Get host ID mappings from nagiosql
        echo "Fetching host ID mappings from nagiosql...\n";
        $hostMappings = getHostIdMappings($nagiosqlConn);
        echo "Found " . count($hostMappings) . " hosts in nagiosql\n";
        
        // Filter relationships to only include valid devices
        $validRelationships = [];
        foreach ($allRelationships as $result) {
            $switchIp = $result['parent'];
            $connectedDeviceIp = $result['child'];
            
            // Check if both IPs exist in nagiosql
            if (isset($hostMappings[$switchIp]) && isset($hostMappings[$connectedDeviceIp])) {
                $validRelationships[] = [
                    'switch_ip' => $switchIp,
                    'connected_device_ip' => $connectedDeviceIp,
                    'connection_type' => $result['type']
                ];
            }
        }
        
        echo "Found " . count($validRelationships) . " valid relationships\n";
        
        // Bulk create relationships
        echo "Creating relationships with cycle detection...\n";
        $createdCount = bulkCreateRelationships($validRelationships, $hostMappings, $nagiosqlConn);
        
        // Save the new relationships to JSON file for tracking
        if (!empty($validRelationships)) {
            $jsonRelationships = [];
            foreach ($validRelationships as $rel) {
                $jsonRelationships[] = [
                    'parent_ip' => $rel['switch_ip'],
                    'child_ip' => $rel['connected_device_ip'],
                    'connection_type' => $rel['connection_type'],
                    'discovered_at' => date('Y-m-d H:i:s')
                ];
            }
            saveAutoRelationships($jsonRelationships);
        }
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        echo "Netdisco sync completed in {$duration} seconds.\n";
        echo "Created: $createdCount relationships\n";
        
        // Run verify actions if any relationships were created
        if ($createdCount > 0) {
            echo "Running NagiosQL verification actions...\n";
            simulateVerifyActions(getSelfIp());
        }
        
    } finally {
        $bubbleMapsConn->close();
        $nagiosqlConn->close();
    }
}

// Function to read the lock status
function isLocked($lockFile) {
    if (!file_exists($lockFile)) {
        return false;
    }
    
    $fp = @fopen($lockFile, 'r');
    if (!$fp) {
        return false;
    }
    
    // Try to get a shared lock (non-blocking)
    $locked = !flock($fp, LOCK_SH | LOCK_NB);
    fclose($fp);
    
    return $locked;
}

// Function to set the lock status
function setLock($lockFile, $status) {
    global $lockHandle;
    
    if ($status) {
        // Create or open the lock file
        $lockHandle = fopen($lockFile, 'w');
        if (!$lockHandle) {
            throw new Exception("Unable to create lock file: $lockFile");
        }
        
        // Try to acquire an exclusive lock (non-blocking)
        if (!flock($lockHandle, LOCK_EX | LOCK_NB)) {
            fclose($lockHandle);
            throw new Exception("Unable to acquire lock: $lockFile");
        }
        
        // Write PID to the lock file
        fwrite($lockHandle, getmypid());
        fflush($lockHandle);
    } else if (isset($lockHandle) && is_resource($lockHandle)) {
        // Release the lock
        flock($lockHandle, LOCK_UN);
        fclose($lockHandle);
        
        // Remove the lock file
        if (file_exists($lockFile)) {
            @unlink($lockFile);
        }
    }
}

// Script start

// Disallow the script to run multiple instances if already running
$lockFile = dirname(__FILE__) . '/locks/netdisco_sync.lock';
$lockHandle = null;

// Check if the script is already running
if (isLocked($lockFile)) {
    echo "Netdisco sync script is already running. Exiting.\n";
    exit;
}

// Set the lock
try {
    setLock($lockFile, true);
    
    // Start the sync process
    syncNetdiscoParentChildRelationships();
    
    echo "Netdisco sync script completed successfully at " . date('Y-m-d H:i:s') . ".\n";
} catch (Exception $e) {
    // Handle any errors that occur during execution
    echo "Error at " . date('Y-m-d H:i:s') . ": " . $e->getMessage() . "\n";
    error_log("Netdisco sync error: " . $e->getMessage());
} finally {
    // Always release the lock when the script finishes
    setLock($lockFile, false);
}
?> 