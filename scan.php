<?php
session_start(); // Start session to store scan results
include "loadenv.php";

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function extractIps($input) {
    preg_match_all('/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/', $input, $matches);
    return $matches[0];
}

function hasComma($ipList) {
    return strpos($ipList, ',') !== false;
}

function isPrivateIP($input) {
    // Define our private IP ranges.
    $privateRanges = [
        ['10.0.0.0', '**************'],       // 10.0.0.0/8
        ['**********', '**************'],     // **********/12
        ['***********', '***************'],   // ***********/16
    ];

    // Helper function to normalize an IP address by removing leading zeros from each octet.
    $normalizeIP = function($ip) {
        return implode('.', array_map('intval', explode('.', $ip)));
    };

    // Helper function to check if a numeric IP range (start/end) is entirely private.
    $isRangePrivate = function($startLong, $endLong) use ($privateRanges) {
        foreach ($privateRanges as $range) {
            $rangeStart = ip2long($range[0]);
            $rangeEnd   = ip2long($range[1]);
            if ($startLong >= $rangeStart && $endLong <= $rangeEnd) {
                return true;
            }
        }
        return false;
    };

    // If the input is a comma-separated list, split it and check each element.
    if (strpos($input, ',') !== false) {
        $parts = explode(',', $input);
        // For the list to be considered private, all parts must be private.
        foreach ($parts as $part) {
            if (!isPrivateIP(trim($part))) {
                return false;
            }
        }
        return true;
    }

    // If the input is in CIDR notation (e.g. "10.0.0.0/24")
    if (strpos($input, '/') !== false) {
        list($baseIp, $mask) = explode('/', $input, 2);
        $baseIp = trim($baseIp);
        $mask = trim($mask);
        if (!filter_var($baseIp, FILTER_VALIDATE_IP) || !is_numeric($mask) || $mask < 0 || $mask > 32) {
            return false;
        }
        $baseIp = $normalizeIP($baseIp); // Normalize the base IP
        $baseLong = ip2long($baseIp);
        // Calculate the network and broadcast addresses.
        $network   = $baseLong & ~((1 << (32 - $mask)) - 1);
        $broadcast = $network | ((1 << (32 - $mask)) - 1);
        return $isRangePrivate($network, $broadcast);
    }

    // If the input is an IP range (e.g. "********-254" or "********-**********")
    if (strpos($input, '-') !== false) {
        list($start, $end) = explode('-', $input, 2);
        $start = trim($start);
        $end   = trim($end);

        // If the end does not look like a full IP address, assume it's a shorthand for the last octet.
        if (strpos($end, '.') === false) {
            $parts = explode('.', $start);
            if (count($parts) !== 4) {
                return false; // Invalid start IP format.
            }
            $parts[3] = $end;
            $end = implode('.', $parts);
        }
        $start = $normalizeIP($start); // Normalize the start IP
        $end = $normalizeIP($end);     // Normalize the end IP
        if (!filter_var($start, FILTER_VALIDATE_IP) || !filter_var($end, FILTER_VALIDATE_IP)) {
            return false;
        }
        $startLong = ip2long($start);
        $endLong   = ip2long($end);
        // Swap if the range is given in reverse order.
        if ($startLong > $endLong) {
            list($startLong, $endLong) = [$endLong, $startLong];
        }
        return $isRangePrivate($startLong, $endLong);
    }

    // Otherwise, assume it's a single IP address.
    $input = $normalizeIP($input); // Normalize the input IP
    if (!filter_var($input, FILTER_VALIDATE_IP)) {
        return false;
    }
    $ipLong = ip2long($input);
    foreach ($privateRanges as $range) {
        $start = ip2long($range[0]);
        $end   = ip2long($range[1]);
        if ($ipLong >= $start && $ipLong <= $end) {
            return true;
        }
    }
    return false;
}

function infraExists($infra) {
    $conn = getDatabaseConnection();

    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM infra WHERE name = ?");
    $stmt->bind_param("s", $infra);

    $stmt->execute();
    $stmt->bind_result($count);
    $stmt->fetch();

    // Check if the count is greater than 0
    $exists = ($count > 0);

    $stmt->close();
    $conn->close();

    return $exists;
}

function multipleInfras() {
    $conn = getDatabaseConnection();

    // No parameters needed in the query, removed bind_param
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM infra");
    
    $stmt->execute();
    $stmt->bind_result($count);
    $stmt->fetch();

    // Check if the count is greater than 1 (returns boolean)
    $hasMultiple = ($count > 1);

    $stmt->close();
    $conn->close();

    return $hasMultiple;
}

function getInfraName(){
    if(isset($_GET['infra']) && !empty($_GET['infra'])){
        return $_GET['infra'];
    }
}

function getApprovalStatus() {
    if (isset($_GET['approval']) && $_GET['approval'] === 'ask') {
        return 'ask';
    }
    return null;
}

function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "blesk";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();

    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "username" => $row["username"],
            "password" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }

    $conn->close();

    return $userCredentials;
}

function checkForceScan() { 
    if (isset($_GET['forceScan']) && $_GET['forceScan'] === 'yes') { 
        return true; 
    } else { 
        return false; 
    } 
}

function getSubnetBase($ip) {
    // Helper function to normalize an IP address by removing leading zeros from each octet
    $normalizeIP = function($ip) {
        return implode('.', array_map('intval', explode('.', $ip)));
    };

    // Check if the input contains CIDR notation (e.g., ***********/24)
    if (strpos($ip, '/') !== false) {
        // Split into IP and subnet mask
        list($baseIp, $subnetMask) = explode('/', $ip);
        // Normalize the base IP address
        $baseIp = $normalizeIP($baseIp);
        $parts = explode('.', $baseIp);
        if (count($parts) == 4) {
            return [
                'subnet_base' => $parts[0] . '.' . $parts[1] . '.' . $parts[2] . '.',
                'range' => $parts[3] . '/' . $subnetMask
            ];
        }
    }
    // Check if the input is an IP range (e.g., ***********-254)
    elseif (strpos($ip, '-') !== false) {
        // Split the range into the base part and the end part
        list($baseIp, $endRange) = explode('-', $ip);
        // Normalize the base IP address
        $baseIp = $normalizeIP($baseIp);
        $parts = explode('.', $baseIp);
        if (count($parts) == 4) {
            return [
                'subnet_base' => $parts[0] . '.' . $parts[1] . '.' . $parts[2] . '.',
                'range' => $parts[3] . '-' . $endRange
            ];
        }
    }
    // Single IP address case (e.g., ***********)
    else {
        // Normalize the IP address
        $ip = $normalizeIP($ip);
        $parts = explode('.', $ip);
        if (count($parts) == 4) {
            return [
                'subnet_base' => $parts[0] . '.' . $parts[1] . '.' . $parts[2] . '.',
                'range' => $parts[3]
            ];
        }
    }
    // Return an empty array if the IP address is not valid
    return [];
}

function executeNmapWithTimeout($command, $timeout = 60) {
    $descriptorspec = array(
        0 => array("pipe", "r"),  // stdin
        1 => array("pipe", "w"),  // stdout
        2 => array("pipe", "w")   // stderr
    );
    
    $process = proc_open($command, $descriptorspec, $pipes);
    
    if (!is_resource($process)) {
        return null;
    }
    
    // Set pipes to non-blocking mode
    stream_set_blocking($pipes[1], 0);
    stream_set_blocking($pipes[2], 0);
    
    $startTime = time();
    $output = '';
    $errorOutput = '';
    $status = null;
    
    do {
        $status = proc_get_status($process);
        
        // Read from stdout
        $stdout = stream_get_contents($pipes[1]);
        if ($stdout !== false) {
            $output .= $stdout;
        }
        
        // Read from stderr
        $stderr = stream_get_contents($pipes[2]);
        if ($stderr !== false) {
            $errorOutput .= $stderr;
        }
        
        // Check if process is still running and timeout exceeded
        if ($status['running'] && (time() - $startTime) > $timeout) {
            // Kill the process
            proc_terminate($process);
            proc_close($process);
            return null; // Return null to indicate timeout
        }
        
        // Small delay to prevent busy waiting
        usleep(100000); // 0.1 second
        
    } while ($status['running']);
    
    // Close pipes
    fclose($pipes[0]);
    fclose($pipes[1]);
    fclose($pipes[2]);
    
    // Close process
    proc_close($process);
    
    return $output . $errorOutput;
}

// Default scan
function scanNetwork($subnetBase, $range) {
    $activeHosts = [];
    $subnetRange = "{$subnetBase}{$range}";

    $infra = getInfraName(); 
    $approvalStatus = getApprovalStatus(); // Get the approval status

    $nmapOutput = executeNmapWithTimeout("sudo nmap -sn -PE --disable-arp-ping $subnetRange 2>&1");
    if ($nmapOutput === null) {
        // Return empty array instead of dying, so scan summary can be properly collected
        return $activeHosts;
    }

    // Extract IP addresses from lines that have "Nmap scan report for"
    preg_match_all('/Nmap scan report for (.*?)(?:\s+\(([\d\.]+)\))?$/im', $nmapOutput, $scanMatches);

    // Find hosts that are up
    preg_match_all('/Host is up/i', $nmapOutput, $upMatches, PREG_OFFSET_CAPTURE);
    
    $hostCount = count($upMatches[0]);
    
    // For each "Host is up" match, find the corresponding IP address from scanMatches
    for ($i = 0; $i < $hostCount; $i++) {
        $hostname = null;
        $ip = null;
        
        if (isset($scanMatches[1][$i])) {
            // Check if this is an IP address or hostname
            if (filter_var($scanMatches[1][$i], FILTER_VALIDATE_IP)) {
                $ip = $scanMatches[1][$i];
            } else {
                $hostname = strtoupper($scanMatches[1][$i]);
                // If we have a hostname, check if we also have the IP in parentheses
                if (!empty($scanMatches[2][$i])) {
                    $ip = $scanMatches[2][$i];
                }
            }
            
            if ($ip) {
                $host = [
                    'hostname' => $hostname,
                    'ip' => $ip,
                    'subnet' => $subnetBase,
                    'infra' => $infra
                ];
                
                // Set apmStatus if approval parameter was provided
                if ($approvalStatus !== null) {
                    $host['apmStatus'] = $approvalStatus;
                }
                
                $activeHosts[] = $host;
            }
        }
    }

    return $activeHosts; 
}

// Scan if multiple IPs given by comma
function scanIPs($ipList) {
    $activeHosts = [];
    $ips = extractIps($ipList);
    $infra = getInfraName();
    $approvalStatus = getApprovalStatus(); // Get approval status
    
    foreach ($ips as $ip) {
        $nmapOutput = executeNmapWithTimeout("sudo nmap -sn -PE --disable-arp-ping $ip 2>&1");
        if ($nmapOutput === null) {
            // Skip this IP if scan times out, but continue with other IPs
            continue;
        }

        // Extract IP addresses from lines that have "Nmap scan report for"
        preg_match_all('/Nmap scan report for (.*?)(?:\s+\(([\d\.]+)\))?$/im', $nmapOutput, $scanMatches);

        // Find hosts that are up
        preg_match_all('/Host is up/i', $nmapOutput, $upMatches, PREG_OFFSET_CAPTURE);
        
        if (!empty($upMatches[0])) {
            $hostname = null;
            $foundIp = $ip; // Default to the IP we're scanning if we can't extract it from output
            
            // If we have a scan report, try to extract hostname and IP
            if (!empty($scanMatches[1][0])) {
                // Check if this is an IP address or hostname
                if (filter_var($scanMatches[1][0], FILTER_VALIDATE_IP)) {
                    $foundIp = $scanMatches[1][0];
                } else {
                    $hostname = strtoupper($scanMatches[1][0]);
                    // If we have a hostname, check if we also have the IP in parentheses
                    if (!empty($scanMatches[2][0])) {
                        $foundIp = $scanMatches[2][0];
                    }
                }
            }
            
            $subnet = substr($foundIp, 0, strrpos($foundIp, '.') + 1);
            $host = [
                'hostname' => $hostname,
                'ip' => $foundIp,
                'infra' => $infra,
                'subnet' => $subnet
            ];
            
            // Set apmStatus if approval parameter was provided
            if ($approvalStatus !== null) {
                $host['apmStatus'] = $approvalStatus;
            }
            
            saveToDatabase([$host], $subnet);
            $activeHosts[] = $host;
        }
    }
    return $activeHosts;
}

function addSubnetToDatabase($subnetBase) {
    $conn = getDatabaseConnection();
    $currentInfra = getInfraName();

    // Generate a default subnetNickname
    if($subnetBase != "External"){
        $subnetNickname = "Subnet " . $subnetBase . "x";
    }else{
        $subnetNickname = $subnetBase;
    }

    // Check if the subnet already exists IN THE CURRENT INFRASTRUCTURE
    $checkSql = "SELECT * FROM subnets WHERE subnet = '$subnetBase' AND infra = '$currentInfra'";
    $result = $conn->query($checkSql);

    if ($result && $result->num_rows > 0) {
        // Subnet exists in this infrastructure, do nothing and return
        return;
    } else {
        // Subnet doesn't exist in this infrastructure, proceed to insert
        $insertSql = "INSERT INTO subnets (subnet, subnetNickname, infra) VALUES ('$subnetBase', '$subnetNickname', '$currentInfra')";
        if (!$conn->query($insertSql)) {
            die("Error inserting subnet: " . $conn->error);
        }
    }

    $conn->close();
}

function saveToDatabase($activeHosts, $subnetBase) {
    if (empty($activeHosts)) {
        return; // Exit the function early if there are no active hosts
    }

    $conn = getDatabaseConnection();
    $infra = getInfraName();
    addSubnetToDatabase($subnetBase);

    foreach ($activeHosts as $host) {
        $ip = $host['ip'];
        $hostname = $host['hostname'];
        $apmStatus = isset($host['apmStatus']) ? $host['apmStatus'] : 'ask';

        // Check if the entry already exists
        $checkSql = "SELECT 1 FROM hosts WHERE ip = '$ip' AND infra = '$infra'";
        $result = $conn->query($checkSql);
        
        if ($result->num_rows > 0) {
            // If record exists, skip insertion
            continue;
        }

        // Insert new entry
        $insertSql = "INSERT INTO hosts (ip, subnet, hostname, infra, apmStatus) VALUES ('$ip', '$subnetBase', '$hostname', '$infra', '$apmStatus')";
        if (!$conn->query($insertSql)) {
            die("Error: " . $insertSql . "<br>" . $conn->error);
        }
    }

    $conn->close();
}

function loadFromDatabase($subnetBase) {
    $conn = getDatabaseConnection();
    $infra = getInfraName();

    $sql = "SELECT * FROM hosts WHERE subnet='$subnetBase' AND infra='$infra'";
    $result = $conn->query($sql);

    $activeHosts = [];
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $activeHosts[] = [
                'ip' => $row['ip'],
                'hostname' => $row['hostname'],
                'subnet' => $row['subnet'],
                'infra' => $row['infra']
            ];
        }
    }

    $conn->close();

    return $activeHosts;
}

function formatOutput($data, $currentInfra) {
    $formattedData = [];
    foreach ($data as $host) {
        if ($host['infra'] !== $currentInfra) {
            continue; // Skip hosts that do not match the current infra
        }
        $subnet = $host['subnet'];
        unset($host['subnet']);
        if (!isset($formattedData[$subnet])) {
            $formattedData[$subnet] = [];
        }
        $formattedData[$subnet][] = $host;
    }
    return $formattedData;
}

function getAllSubnetsData() {
    $conn = getDatabaseConnection();
    $currentInfra = getInfraName(); // Get the current infra

    $sql = "SELECT * FROM hosts";
    $result = $conn->query($sql);

    $allHosts = [];
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $allHosts[] = [
                'id' => $row['id'],
                'ip' => $row['ip'],
                'hostname' => $row['hostname'],
                'subnet' => $row['subnet'],
                'infra' => $row['infra'],
                'apmStatus' => $row['apmStatus'],
                'hostgroup' => $row['hostgroup'],
                'blacklist' => $row['blacklist']
            ];
        }
    }

    $conn->close();

    // Filter and format output based on current infra
    return formatOutput($allHosts, $currentInfra);
}

function getAllSubnetsDataNoInfra() {
    $conn = getDatabaseConnection();
    $currentInfra = getInfraName();
    
    $stmt = $conn->prepare("SELECT * FROM hosts WHERE infra = ?");
    $stmt->bind_param("s", $currentInfra);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $allHosts = [];
    
    
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $subnetKey = "all"; // Needed for fetchData() to parse the json properly
            
            // Add the host to the $allHosts array
            $allHosts[$subnetKey][] = [
                'id' => $row['id'],
                'ip' => $row['ip'],
                'hostname' => $row['hostname'],
                'subnet' => $row['subnet'],
                'infra' => $row['infra'],
                'apmStatus' => $row['apmStatus'],
                'hostgroup' => $row['hostgroup'],
                'blacklist' => $row['blacklist']
            ];
        }
    }

    $stmt->close();
    $conn->close();
    return $allHosts; // Return all hosts without filtering by infra for hostgroups view
}
// For public host IPs
function pingHost($host) {
    $activeHosts = [];
    $infra = getInfraName();

    // Remove protocol if present and extract only the hostname.
    if (preg_match('/^https?:\/\//', $host)) {
        $host = parse_url($host, PHP_URL_HOST);
    }
    $host = trim($host);
    // Execute the ping command.
    // Use escapeshellarg() for safety.
    $command = "ping -c 1 " . escapeshellarg($host) . " 2>&1";
    $pingOutput = shell_exec($command);

    if ($pingOutput === null) {
        // Unable to execute the ping command.
        return $activeHosts;
    }

    // Check for success by looking for common indicators in the output.
    // This example checks for '1 received', '0% packet loss', or 'bytes from'.
    if (strpos($pingOutput, '1 received') !== false ||
        strpos($pingOutput, '0% packet loss') !== false ||
        strpos($pingOutput, 'bytes from') !== false) {

        // Split the output into lines and use the first line to extract the IP.
        $lines = explode("\n", $pingOutput);
        $firstLine = $lines[0];

        // Look for an IP address within parentheses, e.g. "(**************)".
        if (preg_match('/\(([\d\.]+)\)/', $firstLine, $matches)) {
            $resolvedIp = $matches[1];
        } else {
            // Fallback: try to resolve using DNS.
            $resolvedIp = gethostbyname($host);
        }

        $activeHosts[] = [
            'hostname'    => strtoupper($host), // Convert hostname to uppercase.
            'ip'          => $resolvedIp,         // The extracted IP.
            'subnet'      => 'External',          // Marked as External.
            'infra'       => $infra,
            'apmStatus'   => 'not-added'
        ];
    }

    return $activeHosts;
}

// For URLs
function processURL($host) {
    $activeHosts = [];
    $infra = getInfraName();
    $selfIP = getSelfIp();
    $adminUser = getUserCredentials();
    $usr = $adminUser["username"];
    $pwd = $adminUser["password"];
    $baseUrl = "https://$selfIP/ndd/";

    // Extract hostname from URL
    $url = $host;
    $hostname = parse_url($host, PHP_URL_HOST) ?: $host;
    $hostname = strtoupper(trim($hostname));

    // Temporary cookie storage
    $cookieFile = tempnam(sys_get_temp_dir(), 'nagios');

    try {
        // STEP 1: Initiate scan via index-proc.php
        echo "Initializing scan...\n";
        $ch = curl_init($baseUrl . 'index-proc.php');
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => ['iprange' => $url],
            CURLOPT_COOKIEJAR => $cookieFile, // Store cookies for reuse
            CURLOPT_COOKIEFILE => $cookieFile, // Load cookies for the session
            CURLOPT_FOLLOWLOCATION => false, // Don't follow redirects
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => true,
            CURLOPT_VERBOSE => true,  // Enable cURL debug
            CURLOPT_SSL_VERIFYPEER => false,  // Disable SSL verification for debugging
            CURLOPT_SSL_VERIFYHOST => false,  // Disable SSL verification for debugging
            CURLOPT_USERPWD => "$usr:$pwd"  // Basic authentication
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        echo $response;

        // STEP 2: Execute import via import-url-proc.php, using the retained cookie
        if ($httpCode === 302) {
            echo "Proceeding with import...\n";
            $ch = curl_init($baseUrl . 'import-url-proc.php');
            curl_setopt_array($ch, [
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => ['iprange' => $url, 'hostgroup' => 'Internet'],
                CURLOPT_COOKIEFILE => $cookieFile, // Use cookies from the first request
                CURLOPT_COOKIEJAR => $cookieFile, // Store any new cookies
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HEADER => true,
                CURLOPT_VERBOSE => true,  // Enable cURL debug
                CURLOPT_SSL_VERIFYPEER => false,  // Disable SSL verification for debugging
                CURLOPT_SSL_VERIFYHOST => false,  // Disable SSL verification for debugging
                CURLOPT_USERPWD => "$usr:$pwd"  // Basic authentication
            ]);

            $response = curl_exec($ch);
            echo $response;
            curl_close($ch);

            // Verify successful completion
            if (str_contains($response, 'status.cgi') || str_contains($response, 'there is no NddDeviceUrl object in the session')) {

                echo "Added: $hostname\n";
                
                // Function to check if a host exists in Nagios
                function checkHostInNagios($hostname) {
                    // Build the URL for the Nagios API call
                    $nagiosUrl = "https://" . $_SERVER['HTTP_HOST'] . "/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true";
                    
                    try {
                        $response = file_get_contents($nagiosUrl);
                        if ($response === false) {
                            return false;
                        }
                        
                        $data = json_decode($response, true);
                        if (!isset($data['data']['hostlist'])) {
                            return false;
                        }
                        
                        // Check if the hostname exists in the hostlist
                        foreach ($data['data']['hostlist'] as $nagiosHost) {
                            if (isset($nagiosHost['address']) && $nagiosHost['address'] === $hostname) {
                                return true;
                            }
                        }
                        
                        return false;
                    } catch (Exception $e) {
                        return false;
                    }
                }
                
                //$nagiosStatus = checkHostInNagios($hostname) ? 'pending' : 'not-added';
                
                $activeHosts[] = [
                    'hostname' => $hostname,
                    'ip' => strtolower($hostname),
                    'subnet' => 'External',
                    'infra' => $infra,
                    'apmStatus' => 'pending'
                ];
            }
        }
    } finally {
        @unlink($cookieFile); // Clean up the temporary cookie file
    }

    return $activeHosts;
}

// Function to collect scan summary data
function collectScanSummary($targetInput, $activeHosts, $scanType = 'Network', $totalScanned = 0, $downIPs = []) {
    $infra = getInfraName();
    $upIPs = array_map(function($host) { return $host['ip']; }, $activeHosts);
    $hostsUp = count($activeHosts);
    $hostsDown = count($downIPs);
    
    // Determine if this is a single IP scan
    $isSingleIP = !hasComma($targetInput) && !strpos($targetInput, '-') && !strpos($targetInput, '/');
    
    // If total scanned wasn't provided, calculate it
    if ($totalScanned === 0) {
        $totalScanned = $hostsUp + $hostsDown;
        if ($totalScanned === 0) {
            $totalScanned = 1; // At least one IP was attempted
        }
    }
    
    $summaryData = [
        'scanType' => $scanType,
        'targetInput' => $targetInput,
        'hostsUp' => $hostsUp,
        'hostsDown' => $hostsDown,
        'totalScanned' => $totalScanned,
        'infrastructure' => $infra,
        'upIPs' => $upIPs,
        'downIPs' => $downIPs,
        'scanTime' => date('Y-m-d H:i:s'),
        'isSingleIP' => $isSingleIP
    ];
    
    // Store in session for retrieval after redirect
    $_SESSION['scanSummary'] = $summaryData;
    
    return $summaryData;
}

// Function to get scan summary from session
function getScanSummary() {
    if (isset($_SESSION['scanSummary'])) {
        $summary = $_SESSION['scanSummary'];
        unset($_SESSION['scanSummary']); // Clear after retrieval
        return $summary;
    }
    return null;
}

// Begin

// If hostgroup view return all hosts
if(isset($_GET['hostgroup']) && !empty($_GET['hostgroup'])){
    header('Content-Type: application/json');
    echo json_encode(getAllSubnetsDataNoInfra(), JSON_PRETTY_PRINT);
    exit;
}
// Otherwise die if infra doesn't exist
if(!infraExists(getInfraName())){
    die("Error: Infra '$infra' does not exist in the table.");
}

// Check if the GET parameter "ip" is set and not empty.
if (isset($_GET['ip']) && !empty($_GET['ip'])) {
    $ip = $_GET['ip'];
    // Allow users to force an IP/range to be treated as internal even if it is outside RFC1918
    $forceInternal = isset($_GET['internal']) && $_GET['internal'] === 'yes';

    // If starts with "https://", we assume it is a URL or a domain name.
    if (preg_match('/^(http:\/\/|https:\/\/)/', $ip)) {
        // Call the ping function
        $activeHosts = processURL($ip);
        saveToDatabase($activeHosts, 'External');
        collectScanSummary($ip, $activeHosts, 'URL', 1, count($activeHosts) === 0 ? [$ip] : []);

        header('Content-Type: application/json');
        echo json_encode(getAllSubnetsData(), JSON_PRETTY_PRINT);
        exit;
    }

    // Check if host is public ip
    // Unless user explicitly asked to treat it as internal, consider non-RFC1918 addresses as external
    if (!$forceInternal && !isPrivateIP($ip)) {
        // Call the ping function
        $activeHosts = pingHost($ip);
        saveToDatabase($activeHosts, 'External');
        collectScanSummary($ip, $activeHosts, 'External Host', 1, count($activeHosts) === 0 ? [$ip] : []);

        header('Content-Type: application/json');
        echo json_encode(getAllSubnetsData(), JSON_PRETTY_PRINT);
        exit;
    }

    // If we get here, $ip is assumed to be a valid IP (or a comma-separated list).
    if (!hasComma($ip)) {
        $subnetInfo = getSubnetBase($ip);
        $subnetBase = $subnetInfo['subnet_base'];
        $range = $subnetInfo['range'];
    }
} else {
    // If no IP is provided, use the server's own IP. Unless we have multiple infras
    if(multipleInfras()){
        header('Content-Type: application/json');
        echo json_encode(getAllSubnetsData(), JSON_PRETTY_PRINT);
        exit;
    }
    $selfIp = getSelfIp();
    $ip = $selfIp; // Define $ip for the case when no IP parameter is provided
    $subnetInfo = getSubnetBase($selfIp);
    $subnetBase = $subnetInfo['subnet_base'];
    $range = "1-254";
}

// Continue with scanning logic if the input was an IP address or list.
if (!checkForceScan() && !empty(loadFromDatabase($subnetBase))) {
    $activeHosts = loadFromDatabase($subnetBase);
    // For loaded data, create a basic summary
    collectScanSummary($ip, $activeHosts, 'Network (Cached)');
    } else {
        if (hasComma($ip)) {
            $activeHosts = scanIPs($ip);
            // Calculate total IPs for comma-separated list
            $totalIPs = count(extractIps($ip));
            $downIPs = array_diff(extractIps($ip), array_map(function($h) { return $h['ip']; }, $activeHosts));
            $scanType = 'Network';
            // Check if any scans timed out (if we have fewer results than expected)
            if (count($activeHosts) < $totalIPs) {
                $scanType = 'Network (Partial - Some scans timed out)';
            }
            collectScanSummary($ip, $activeHosts, $scanType, $totalIPs, $downIPs);
        } else {
            $activeHosts = scanNetwork($subnetBase, $range);
            saveToDatabase($activeHosts, $subnetBase);
            // For range scans, calculate total based on range
            $totalScanned = 254; // Default range 1-254
            if (preg_match('/(\d+)-(\d+)/', $range, $matches)) {
                $start = intval($matches[1]);
                $end = intval($matches[2]);
                $totalScanned = $end - $start + 1;
            }
            $downCount = $totalScanned - count($activeHosts);
            $scanType = 'Network';
            // Check if scan timed out (if we have no results from a range scan)
            if (count($activeHosts) === 0 && $totalScanned > 1) {
                $scanType = 'Network (Timed out)';
            }
            collectScanSummary($ip, $activeHosts, $scanType, $totalScanned, array_fill(0, $downCount, 'unknown'));
        }
    }

// Finally, return all subnet data as JSON.
header('Content-Type: application/json');
echo json_encode(getAllSubnetsData(), JSON_PRETTY_PRINT);
?>
