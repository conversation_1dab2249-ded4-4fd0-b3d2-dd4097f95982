<?php
include "loadenv.php";
include 'theme_loader.php'; // Include the theme loader
// Database connection
$db_host = $_ENV["DB_SERVER"];
$db_user = $_ENV["DB_USER"];
$db_pass = $_ENV["DB_PASSWORD"];
$db_name = 'db_nagiosql_v3';
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Prevent caching
header("Cache-Control: no-cache, must-revalidate");

// Fetch parameters
$servicename = $_GET['servicename'] ?? ''; // This is service_description in tbl_service
$hostname = $_GET['hostname'] ?? '';
$data = [];

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function simulateVerifyActions($ip) {
    // [Keeping your existing simulateVerifyActions function unchanged]
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
        ]);

        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception('Login failed: ' . curl_error($ch));
        }
        error_log("Login response code: " . curl_getinfo($ch, CURLINFO_HTTP_CODE));

        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            error_log("Redirecting to: $redirect");
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                throw new Exception("Action $action failed: " . curl_error($ch));
            }
            error_log("Action $action completed with response code: " . curl_getinfo($ch, CURLINFO_HTTP_CODE));
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log("Error in simulateVerifyActions: " . $e->getMessage());
    }
}

// Fetch all time periods
$timeperiods = [];
$sql = "SELECT id, timeperiod_name, alias FROM tbl_timeperiod ORDER BY timeperiod_name";
$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) {
    $timeperiods[] = $row;
}

if (!empty($servicename) && !empty($hostname)) {
    // Step 1: Get host ID
    $sql = "SELECT id FROM tbl_host WHERE host_name = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $hostname);
    $stmt->execute();
    $result = $stmt->get_result();
    $host = $result->fetch_assoc();
    $stmt->close();

    if ($host) {
        $host_id = $host['id'];
        
        // Step 2: Get all service IDs linked to this host
        $sql = "SELECT idMaster FROM tbl_lnkServiceToHost WHERE idSlave = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $host_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $service_ids = [];
        while ($row = $result->fetch_assoc()) {
            $service_ids[] = $row['idMaster'];
        }
        $stmt->close();

        // Step 3: Find the matching service
        foreach ($service_ids as $service_id) {
            $sql = "SELECT * FROM tbl_service WHERE id = ? AND service_description = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("is", $service_id, $servicename);
            $stmt->execute();
            $result = $stmt->get_result();
            $service_data = $result->fetch_assoc();
            $stmt->close();

            if ($service_data) {
                $data = $service_data;
                break; // Found our match, exit the loop
            }
        }
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($servicename) && !empty($hostname)) {
    $fields = [
        'initial_state', 'max_check_attempts', 'check_interval', 'retry_interval',
        'active_checks_enabled', 'passive_checks_enabled', 'check_period',
        'freshness_threshold', 'check_freshness', 'obsess_over_service', 'event_handler',
        'event_handler_enabled', 'low_flap_threshold', 'high_flap_threshold',
        'flap_detection_enabled', 'retain_status_information', 'retain_nonstatus_information',
        'process_perf_data', 'is_volatile'
    ];

    $updates = [];
    $params = [];
    $types = '';

    $flap_options = !empty($_POST['flap_detection_options']) ? implode(',', $_POST['flap_detection_options']) : '';
    if ($flap_options !== ($data['flap_detection_options'] ?? '')) {
        $updates[] = "flap_detection_options = ?";
        $params[] = $flap_options;
        $types .= 's';
    }

    foreach ($fields as $field) {
        $value = $_POST[$field] ?? '';
        $current_value = $data[$field] ?? '';
        
        if ($value !== $current_value) {
            if ($value === '') {
                $updates[] = "$field = NULL";
            } else {
                $updates[] = "$field = ?";
                $params[] = $value;
                $types .= 's';
            }
        }
    }

    if (!empty($updates) && !empty($data['id'])) {
        $sql = "UPDATE tbl_service SET " . implode(', ', $updates) . " WHERE id = ?";
        $params[] = $data['id'];
        $types .= 'i';

        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param($types, ...$params);
            $stmt->execute();
            $stmt->close();
            simulateVerifyActions(getSelfIp());
            
            // Refresh data after update
            $sql = "SELECT * FROM tbl_service WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $data['id']);
            $stmt->execute();
            $result = $stmt->get_result();
            $data = $result->fetch_assoc() ?? [];
            $stmt->close();

            // Output JavaScript to close the window
            echo '<script>';
            echo 'if (window.self !== window.top) {';
            echo '  window.parent.location.reload();';
            echo '} else {';
            echo '  window.location.reload();';
            echo '}';
            echo '</script>';
            exit;
        }
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Settings - <?php echo htmlspecialchars($servicename ?: 'No Service'); ?></title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/advancedCommands.css">
</head>
<body>
    <div class="container">
        <h1><?php echo htmlspecialchars($servicename ?: 'No Service'); ?> on <?php echo htmlspecialchars($hostname ?: 'No Host'); ?></h1>

        <?php if (empty($data) && !empty($servicename)): ?>
            <p class="no-data">No data found for service: <?php echo htmlspecialchars($servicename); ?></p>
        <?php endif; ?>

        <form method="POST">
            <!-- Basic Settings -->
            <div class="form-section">
                <h2>Basic Settings</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Initial State</label>
                        <div class="radio-group">
                            <label><input type="radio" name="initial_state" value="o" <?php echo (string)($data['initial_state'] ?? '') === 'o' ? 'checked' : ''; ?>> Okay</label>
                            <label><input type="radio" name="initial_state" value="w" <?php echo (string)($data['initial_state'] ?? '') === 'w' ? 'checked' : ''; ?>> Warning</label>
                            <label><input type="radio" name="initial_state" value="c" <?php echo (string)($data['initial_state'] ?? '') === 'c' ? 'checked' : ''; ?>> Critical</label>
                            <label><input type="radio" name="initial_state" value="u" <?php echo (string)($data['initial_state'] ?? '') === 'u' ? 'checked' : ''; ?>> Unknown</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Max Check Attempts</label>
                        <input type="number" name="max_check_attempts" min="1" value="<?php echo htmlspecialchars($data['max_check_attempts'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label>Check Interval (min)</label>
                        <input type="number" name="check_interval" min="1" value="<?php echo htmlspecialchars($data['check_interval'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label>Retry Interval (min)</label>
                        <input type="number" name="retry_interval" min="1" value="<?php echo htmlspecialchars($data['retry_interval'] ?? ''); ?>">
                    </div>
                </div>
            </div>

            <!-- Check Settings -->
            <div class="form-section">
                <h2>Check Settings</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Active Checks</label>
                        <div class="radio-group">
                            <label><input type="radio" name="active_checks_enabled" value="1" <?php echo (string)($data['active_checks_enabled'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="active_checks_enabled" value="0" <?php echo (string)($data['active_checks_enabled'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="active_checks_enabled" value="2" <?php echo (string)($data['active_checks_enabled'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="active_checks_enabled" value="3" <?php echo (string)($data['active_checks_enabled'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Passive Checks</label>
                        <div class="radio-group">
                            <label><input type="radio" name="passive_checks_enabled" value="1" <?php echo (string)($data['passive_checks_enabled'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="passive_checks_enabled" value="0" <?php echo (string)($data['passive_checks_enabled'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="passive_checks_enabled" value="2" <?php echo (string)($data['passive_checks_enabled'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="passive_checks_enabled" value="3" <?php echo (string)($data['passive_checks_enabled'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Check Period</label>
                        <select name="check_period">
                            <option value="0" <?php echo (string)($data['check_period'] ?? '') === '0' ? 'selected' : ''; ?>>Null</option>
                            <?php foreach ($timeperiods as $tp): ?>
                                <option value="<?php echo $tp['id']; ?>" <?php echo (string)($data['check_period'] ?? '') === (string)$tp['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($tp['timeperiod_name']); ?> (<?php echo htmlspecialchars($tp['alias']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Freshness Threshold (s)</label>
                        <input type="number" name="freshness_threshold" min="0" value="<?php echo htmlspecialchars($data['freshness_threshold'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label>Check Freshness</label>
                        <div class="radio-group">
                            <label><input type="radio" name="check_freshness" value="1" <?php echo (string)($data['check_freshness'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="check_freshness" value="0" <?php echo (string)($data['check_freshness'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="check_freshness" value="2" <?php echo (string)($data['check_freshness'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="check_freshness" value="3" <?php echo (string)($data['check_freshness'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Handling -->
            <div class="form-section">
                <h2>Event Handling</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Obsess Over Service</label>
                        <div class="radio-group">
                            <label><input type="radio" name="obsess_over_service" value="1" <?php echo (string)($data['obsess_over_service'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="obsess_over_service" value="0" <?php echo (string)($data['obsess_over_service'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="obsess_over_service" value="2" <?php echo (string)($data['obsess_over_service'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="obsess_over_service" value="3" <?php echo (string)($data['obsess_over_service'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Event Handler</label>
                        <select name="event_handler">
                            <option value="0" <?php echo (string)($data['event_handler'] ?? '') === '0' ? 'selected' : ''; ?>>[None]</option>
                            <option value="1" <?php echo (string)($data['event_handler'] ?? '') === '1' ? 'selected' : ''; ?>>notify-host-by-email</option>
                            <option value="271" <?php echo (string)($data['event_handler'] ?? '') === '271' ? 'selected' : ''; ?>>notify-host-by-email-html</option>
                            <option value="411" <?php echo (string)($data['event_handler'] ?? '') === '411' ? 'selected' : ''; ?>>notify-host-by-page</option>
                            <option value="409" <?php echo (string)($data['event_handler'] ?? '') === '409' ? 'selected' : ''; ?>>notify-host-by-sms</option>
                            <option value="2" <?php echo (string)($data['event_handler'] ?? '') === '2' ? 'selected' : ''; ?>>notify-service-by-email</option>
                            <option value="272" <?php echo (string)($data['event_handler'] ?? '') === '272' ? 'selected' : ''; ?>>notify-service-by-email-html</option>
                            <option value="412" <?php echo (string)($data['event_handler'] ?? '') === '412' ? 'selected' : ''; ?>>notify-service-by-page</option>
                            <option value="410" <?php echo (string)($data['event_handler'] ?? '') === '410' ? 'selected' : ''; ?>>notify-service-by-sms</option>
                            <option value="3" <?php echo (string)($data['event_handler'] ?? '') === '3' ? 'selected' : ''; ?>>process-host-perfdata-file</option>
                            <option value="4" <?php echo (string)($data['event_handler'] ?? '') === '4' ? 'selected' : ''; ?>>process-service-perfdata-file</option>
                            <option value="316" <?php echo (string)($data['event_handler'] ?? '') === '316' ? 'selected' : ''; ?>>reload-nagios-service</option>
                            <option value="277" <?php echo (string)($data['event_handler'] ?? '') === '277' ? 'selected' : ''; ?>>restart-arpwatch-service</option>
                            <option value="245" <?php echo (string)($data['event_handler'] ?? '') === '245' ? 'selected' : ''; ?>>restart-barnyard2-service</option>
                            <option value="447" <?php echo (string)($data['event_handler'] ?? '') === '447' ? 'selected' : ''; ?>>restart-bprobe-barnyard2-service</option>
                            <option value="448" <?php echo (string)($data['event_handler'] ?? '') === '448' ? 'selected' : ''; ?>>restart-bprobe-httpd-service</option>
                            <option value="449" <?php echo (string)($data['event_handler'] ?? '') === '449' ? 'selected' : ''; ?>>restart-bprobe-nprobe-service</option>
                            <option value="450" <?php echo (string)($data['event_handler'] ?? '') === '450' ? 'selected' : ''; ?>>restart-bprobe-snort-service</option>
                            <option value="307" <?php echo (string)($data['event_handler'] ?? '') === '307' ? 'selected' : ''; ?>>restart-dns-service</option>
                            <option value="296" <?php echo (string)($data['event_handler'] ?? '') === '296' ? 'selected' : ''; ?>>restart-elasticsearch-service</option>
                            <option value="308" <?php echo (string)($data['event_handler'] ?? '') === '308' ? 'selected' : ''; ?>>restart-ftp-service</option>
                            <option value="458" <?php echo (string)($data['event_handler'] ?? '') === '458' ? 'selected' : ''; ?>>restart-grafana-service</option>
                            <option value="242" <?php echo (string)($data['event_handler'] ?? '') === '242' ? 'selected' : ''; ?>>restart-http-service</option>
                            <option value="295" <?php echo (string)($data['event_handler'] ?? '') === '295' ? 'selected' : ''; ?>>restart-kibana-service</option>
                            <option value="297" <?php echo (string)($data['event_handler'] ?? '') === '297' ? 'selected' : ''; ?>>restart-logstash-service</option>
                            <option value="243" <?php echo (string)($data['event_handler'] ?? '') === '243' ? 'selected' : ''; ?>>restart-mysql-service</option>
                            <option value="260" <?php echo (string)($data['event_handler'] ?? '') === '260' ? 'selected' : ''; ?>>restart-netdisco-daemon</option>
                            <option value="385" <?php echo (string)($data['event_handler'] ?? '') === '385' ? 'selected' : ''; ?>>restart-netdisco-web</option>
                            <option value="301" <?php echo (string)($data['event_handler'] ?? '') === '301' ? 'selected' : ''; ?>>restart-nprobe-service</option>
                            <option value="302" <?php echo (string)($data['event_handler'] ?? '') === '302' ? 'selected' : ''; ?>>restart-nrpe-service</option>
                            <option value="225" <?php echo (string)($data['event_handler'] ?? '') === '225' ? 'selected' : ''; ?>>restart-ntopng-service</option>
                            <option value="300" <?php echo (string)($data['event_handler'] ?? '') === '300' ? 'selected' : ''; ?>>restart-ntpd-service</option>
                            <option value="456" <?php echo (string)($data['event_handler'] ?? '') === '456' ? 'selected' : ''; ?>>restart-openvas-gsa-service</option>
                            <option value="454" <?php echo (string)($data['event_handler'] ?? '') === '454' ? 'selected' : ''; ?>>restart-openvas-manager-service</option>
                            <option value="455" <?php echo (string)($data['event_handler'] ?? '') === '455' ? 'selected' : ''; ?>>restart-openvas-scanner-service</option>
                            <option value="294" <?php echo (string)($data['event_handler'] ?? '') === '294' ? 'selected' : ''; ?>>restart-pnp4nagios-service</option>
                            <option value="240" <?php echo (string)($data['event_handler'] ?? '') === '240' ? 'selected' : ''; ?>>restart-postgresql-service</option>
                            <option value="337" <?php echo (string)($data['event_handler'] ?? '') === '337' ? 'selected' : ''; ?>>restart-redis-service</option>
                            <option value="244" <?php echo (string)($data['event_handler'] ?? '') === '244' ? 'selected' : ''; ?>>restart-rsyslog-service</option>
                            <option value="274" <?php echo (string)($data['event_handler'] ?? '') === '274' ? 'selected' : ''; ?>>restart-sguild-service</option>
                            <option value="306" <?php echo (string)($data['event_handler'] ?? '') === '306' ? 'selected' : ''; ?>>restart-smtpd-service</option>
                            <option value="241" <?php echo (string)($data['event_handler'] ?? '') === '241' ? 'selected' : ''; ?>>restart-snmptrapd-service</option>
                            <option value="226" <?php echo (string)($data['event_handler'] ?? '') === '226' ? 'selected' : ''; ?>>restart-snort-service</option>
                            <option value="305" <?php echo (string)($data['event_handler'] ?? '') === '305' ? 'selected' : ''; ?>>restart-sshd-service</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Event Handler Enabled</label>
                        <div class="radio-group">
                            <label><input type="radio" name="event_handler_enabled" value="1" <?php echo (string)($data['event_handler_enabled'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="event_handler_enabled" value="0" <?php echo (string)($data['event_handler_enabled'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="event_handler_enabled" value="2" <?php echo (string)($data['event_handler_enabled'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="event_handler_enabled" value="3" <?php echo (string)($data['event_handler_enabled'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Flapping Settings -->
            <div class="form-section">
                <h2>Flapping Settings</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Low Flap Threshold (%)</label>
                        <input type="number" name="low_flap_threshold" min="0" max="100" value="<?php echo htmlspecialchars($data['low_flap_threshold'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label>High Flap Threshold (%)</label>
                        <input type="number" name="high_flap_threshold" min="0" max="100" value="<?php echo htmlspecialchars($data['high_flap_threshold'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label>Flap Detection Enabled</label>
                        <div class="radio-group">
                            <label><input type="radio" name="flap_detection_enabled" value="1" <?php echo (string)($data['flap_detection_enabled'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="flap_detection_enabled" value="0" <?php echo (string)($data['flap_detection_enabled'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="flap_detection_enabled" value="2" <?php echo (string)($data['flap_detection_enabled'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="flap_detection_enabled" value="3" <?php echo (string)($data['flap_detection_enabled'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Flap Detection Options</label>
                        <div class="checkbox-group">
                            <?php $flap_options = explode(',', $data['flap_detection_options'] ?? ''); ?>
                            <label><input type="checkbox" name="flap_detection_options[]" value="o" <?php echo in_array('o', $flap_options) ? 'checked' : ''; ?>> Okay</label>
                            <label><input type="checkbox" name="flap_detection_options[]" value="w" <?php echo in_array('w', $flap_options) ? 'checked' : ''; ?>> Warning</label>
                            <label><input type="checkbox" name="flap_detection_options[]" value="c" <?php echo in_array('c', $flap_options) ? 'checked' : ''; ?>> Critical</label>
                            <label><input type="checkbox" name="flap_detection_options[]" value="u" <?php echo in_array('u', $flap_options) ? 'checked' : ''; ?>> Unknown</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Retention Settings -->
            <div class="form-section">
                <h2>Retention Settings</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Retain Status Info</label>
                        <div class="radio-group">
                            <label><input type="radio" name="retain_status_information" value="1" <?php echo (string)($data['retain_status_information'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="retain_status_information" value="0" <?php echo (string)($data['retain_status_information'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="retain_status_information" value="2" <?php echo (string)($data['retain_status_information'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="retain_status_information" value="3" <?php echo (string)($data['retain_status_information'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Retain Non-Status Info</label>
                        <div class="radio-group">
                            <label><input type="radio" name="retain_nonstatus_information" value="1" <?php echo (string)($data['retain_nonstatus_information'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="retain_nonstatus_information" value="0" <?php echo (string)($data['retain_nonstatus_information'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="retain_nonstatus_information" value="2" <?php echo (string)($data['retain_nonstatus_information'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="retain_nonstatus_information" value="3" <?php echo (string)($data['retain_nonstatus_information'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Process Perf Data</label>
                        <div class="radio-group">
                            <label><input type="radio" name="process_perf_data" value="1" <?php echo (string)($data['process_perf_data'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="process_perf_data" value="0" <?php echo (string)($data['process_perf_data'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="process_perf_data" value="2" <?php echo (string)($data['process_perf_data'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="process_perf_data" value="3" <?php echo (string)($data['process_perf_data'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Is Volatile</label>
                        <div class="radio-group">
                            <label><input type="radio" name="is_volatile" value="1" <?php echo (string)($data['is_volatile'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="is_volatile" value="0" <?php echo (string)($data['is_volatile'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="is_volatile" value="2" <?php echo (string)($data['is_volatile'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="is_volatile" value="3" <?php echo (string)($data['is_volatile'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                </div>
            </div>

            <button type="submit" id="save-button" class="save-button">Save Changes</button>
        </form>
    </div>
    <script>
        document.getElementById('save-button').addEventListener('click', function(e) {
            this.textContent = 'Saving...';
        });
    </script>
</body>
</html>