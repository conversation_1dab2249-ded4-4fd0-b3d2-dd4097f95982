<?php
// Read the configuration file
$config_file = 'conf/bubblemap_configs';
$config_content = file_get_contents($config_file);

// Check if "main view" line exists
$main_view_exists = false;
$main_view_value = '';

if ($config_content !== false) {
    $lines = explode("\n", $config_content);
    foreach ($lines as $line) {
        $line = trim($line);
        if (strpos($line, 'main view:') === 0) {
            $main_view_exists = true;
            $main_view_value = trim(substr($line, 10)); // Remove "main view: " prefix
            break;
        }
    }
}

// If "main view" doesn't exist, create it with default value "bubble"
if (!$main_view_exists) {
    $config_content .= "\nmain view: bubble";
    file_put_contents($config_file, $config_content);
    $main_view_value = 'bubble';
}

// Redirect based on main view value
switch ($main_view_value) {
    case 'list':
        header('Location: hostlist.php');
        exit;
    case 'table':
        header('Location: tableview.php');
        exit;
    case 'dashboards':
        header('Location: networkmap.php');
        exit;
    case 'bubble':
    default:
        // Do nothing for bubble view (default behavior)
        break;
}
?>
