<?php
header('Content-Type: application/json');

include "loadenv.php";

function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"];
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

class HostOSDetector {
    // Cache for OS detection results - avoid repeated calls
    private $osCache = [];
    // Cache for MAC vendor lookup results
    private $macCache = [];
    // Cache timeout in seconds (10 minutes)
    private $cacheTimeout = 600;
    
    // Add SNMP detection method
    private function detectViaSNMP($ip) {
        // First try SNMP v1/v2c
        $communityStrings = $this->getSnmpCommunities();
        if (!empty($communityStrings)) {
            $versions = ['v1', 'v2c'];
            foreach ($communityStrings as $community) {
                foreach ($versions as $version) {
                    $command = "snmpwalk -$version -c " . escapeshellarg($community) . " " . 
                              escapeshellarg($ip) . " *******.******* 2>&1";
                    $output = [];
                    exec($command, $output, $exitCode);
                    $result = implode("\n", $output);

                    if ($exitCode === 0 && !empty($result) && 
                        strpos($result, 'Timeout') === false && 
                        strpos($result, 'No Such') === false) {
                        
                        // Check for specific vendors in the SNMP output
                        $result_lower = strtolower($result);
                        if (strpos($result_lower, 'fortinet') !== false || 
                            strpos($result_lower, 'fgt') !== false || 
                            strpos($result_lower, 'forti') !== false) {
                            return "Vendor: Fortinet (detected by SNMP)";
                        } elseif (strpos($result_lower, 'vmware') !== false || 
                                 strpos($result_lower, 'esx') !== false || 
                                 strpos($result_lower, 'vsphere') !== false) {
                            return "Vendor: VMware (detected by SNMP)";
                        } elseif (strpos($result_lower, 'cisco') !== false) {
                            return "Vendor: Cisco (detected by SNMP)";
                        } elseif (strpos($result_lower, 'aruba') !== false) {
                            return "Vendor: Aruba (detected by SNMP)";
                        } elseif (strpos($result_lower, 'ruckus') !== false) {
                            return "Vendor: Ruckus (detected by SNMP)";
                        }
                    }
                }
            }
        }

        // If v1/v2c failed, try SNMPv3
        $credentials = $this->getSnmpv3Credentials();
        if ($credentials && !empty($credentials)) {
            foreach ($credentials as $cred) {
                $username = escapeshellarg($cred['snmp_username']);
                $authPassword = escapeshellarg($cred['snmp_password']);
                $authProtocol = escapeshellarg($cred['snmp_auth_protocol']);
                $privPassphrase = escapeshellarg($cred['snmp_priv_passphrase']);
                $privProtocol = escapeshellarg($cred['snmp_priv_protocol']);

                $command = "snmpwalk -v3 -l authPriv -u $username -a $authProtocol -A $authPassword " .
                          "-x $privProtocol -X $privPassphrase " . escapeshellarg($ip) . 
                          " *******.******* 2>&1";
                $output = [];
                exec($command, $output, $exitCode);
                $result = implode("\n", $output);

                if ($exitCode === 0 && !empty($result) && 
                    strpos($result, 'Timeout') === false && 
                    strpos($result, 'No Such') === false) {
                    
                    // Check for specific vendors in the SNMP output
                    $result_lower = strtolower($result);
                    if (strpos($result_lower, 'fortinet') !== false || 
                        strpos($result_lower, 'fgt') !== false || 
                        strpos($result_lower, 'forti') !== false) {
                        return "Vendor: Fortinet (detected by SNMP)";
                    } elseif (strpos($result_lower, 'vmware') !== false || 
                             strpos($result_lower, 'esx') !== false || 
                             strpos($result_lower, 'vsphere') !== false) {
                        return "Vendor: VMware (detected by SNMP)";
                    } elseif (strpos($result_lower, 'cisco') !== false) {
                        return "Vendor: Cisco (detected by SNMP)";
                    } elseif (strpos($result_lower, 'aruba') !== false) {
                        return "Vendor: Aruba (detected by SNMP)";
                    } elseif (strpos($result_lower, 'ruckus') !== false) {
                        return "Vendor: Ruckus (detected by SNMP)";
                    }
                }
            }
        }

        return null;
    }

    // Add database connection methods from snmpCheck.php
    private function getSnmpCommunities() {
        $host = $_ENV["DB_SERVER"];
        $dbname = "ndd";
        $username = $_ENV["DB_USER"];
        $password = $_ENV["DB_PASSWORD"];
    
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $stmt = $pdo->query("SELECT communityro FROM csnmp");
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            return [];
        }
    }    

    private function getSnmpv3Credentials() {
        $host = $_ENV["DB_SERVER"];
        $dbname = 'ndd';
        $user = $_ENV["DB_USER"];
        $pass = $_ENV["DB_PASSWORD"];
        
        try {
            $pdo = new PDO("mysql:host={$host};dbname={$dbname};charset=utf8mb4", $user, $pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $sql = "SELECT snmp_username, snmp_password, snmp_auth_protocol, snmp_priv_passphrase, snmp_priv_protocol FROM csnmpv3";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            return false;
        }
    }

    public function detectOS($ip, $isExternal = false) {
        // Check if we have a recent cache entry
        if ($this->checkCache($ip)) {
            return $this->osCache[$ip]['result'];
        }
        
        $remote_addr = $ip ?? $_SERVER['REMOTE_ADDR'];
        if (empty($remote_addr)) {
            return "Could not determine remote address.";
        }

        // First check hostname for specific patterns
        $hostname = gethostbyaddr($remote_addr);
        if ($hostname && $hostname !== $remote_addr) {
            $hostname_lower = strtolower($hostname);
            if (strpos($hostname_lower, 'esx') !== false || strpos($hostname_lower, 'vsphere') !== false) {
                $result = "Vendor: VMware (detected by hostname)";
                $this->cacheResult($ip, $result);
                return $result;
            } elseif (strpos($hostname_lower, 'fgt') !== false || strpos($hostname_lower, 'forti') !== false) {
                $result = "Vendor: Fortinet (detected by hostname)";
                $this->cacheResult($ip, $result);
                return $result;
            }
        }

        $output = "";
        $ttl = $this->getTTL($remote_addr);
        
        // We already checked for Windows TTL at a higher level, but keep this for completeness
        // when the function is called directly
        if ($ttl > 0 && $ttl >= 100 && $ttl <= 128) {
            $result = "OS: Windows (detected by TTL " . $ttl . ")";
            $this->cacheResult($ip, $result);
            return $result;
        }
        
        // Then check MAC vendor (fast lookup)
        $mac_info = $this->getMacVendor($remote_addr);
        if ($mac_info['mac']) {
            if (!empty($output)) $output .= " | ";
            $output .= "MAC Address: " . $mac_info['mac'];
            if ($mac_info['vendor']) {
                $output .= " | Vendor: " . $mac_info['vendor'] . " (MAC Lookup)";
            }
        }

        // If not Windows, check other TTL patterns
        if ($ttl > 0 && empty($output)) {
            if ($ttl >= 60 && $ttl <= 64) {  // Linux/Unix
                $output .= "OS: Linux/Unix (detected by TTL " . $ttl . ")";
            } elseif ($ttl >= 200 && $ttl <= 255) {  // Cisco/BSD
                $output .= "OS: BSD/Cisco (detected by TTL " . $ttl . ")";
            }
        }

        // Only run nmap as a last resort and only if output is still empty
        if (empty($output)) {
            // Check if we're allowed to do more intensive scanning
            if (!$this->isHighLoadPeriod()) {
                $nmap_os = $this->getOSNmap($remote_addr);
                if ($nmap_os) {
                    $output .= "OS: " . $nmap_os . " (Nmap)";
                }
            } else {
                $output .= "OS detection delayed (high system load)";
            }
        }

        // If all other methods failed, try SNMP as a last resort
        if (empty(trim($output, " |"))) {
            $snmpResult = $this->detectViaSNMP($remote_addr);
            if ($snmpResult) {
                $this->cacheResult($ip, $snmpResult);
                return $snmpResult;
            }
            
            $result = $isExternal ? "External network detected" : "Could not determine MAC address, vendor, or OS.";
            $this->cacheResult($ip, $result);
            return $result;
        }
        
        $this->cacheResult($ip, $output);
        return $output;
    }

    private function getMacVendor($remote_addr) {
        // Check MAC cache first
        if (isset($this->macCache[$remote_addr]) && 
            (time() - $this->macCache[$remote_addr]['time'] < $this->cacheTimeout)) {
            return [
                'mac' => $this->macCache[$remote_addr]['mac'],
                'vendor' => $this->macCache[$remote_addr]['vendor']
            ];
        }
        
        $result = [
            'mac' => null,
            'vendor' => null
        ];

        // Use arp command first (much faster than nmap)
        $arp_cmd = "arp -n " . escapeshellarg($remote_addr);
        $arp_output = @shell_exec($arp_cmd);
        
        if ($arp_output && preg_match('/([0-9A-Fa-f]{2}[:-]){5}[0-9A-Fa-f]{2}/i', $arp_output, $matches)) {
            $result['mac'] = $matches[0];
            
            // Try to get vendor from a local OUI database if available
            $result['vendor'] = $this->getVendorFromOUI($result['mac']);
            
            // If no vendor found locally, only then use nmap
            if (!$result['vendor'] && !$this->isHighLoadPeriod()) {
                $nmap_cmd = "sudo nmap -sn --host-timeout 2s " . escapeshellarg($remote_addr);
                $nmap_output = @shell_exec($nmap_cmd);
                
                if (preg_match('/MAC Address: (([0-9A-Fa-f]{2}[:-]){5}[0-9A-Fa-f]{2}) \(([^)]+)\)/i', $nmap_output, $matches)) {
                    $result['mac'] = $matches[1];
                    $result['vendor'] = trim($matches[3]);
                }
            }
            
            // Special case: Treat Broadcom as VMware
            if (isset($result['vendor']) && 
                (strtolower($result['vendor']) === 'broadcom' || 
                 strtolower($result['vendor']) === 'intel corporate')) {
                $result['vendor'] = 'VMware';
            }
            
            // Cache the result
            $this->macCache[$remote_addr] = [
                'mac' => $result['mac'],
                'vendor' => $result['vendor'],
                'time' => time()
            ];
        }

        return $result;
    }
    
    // Function to get vendor from a local OUI database file if available
    private function getVendorFromOUI($mac) {
        // This would use a local file containing MAC address prefixes and vendors
        // For now, return null as implementation depends on having such a file
        return null;
    }

    private function getOSNmap($host) {
        // Only run if system load is acceptable
        if ($this->isHighLoadPeriod()) {
            return null;
        }
        
        $cmd_nmap = "sudo nmap -O --osscan-limit --max-retries 1 --max-os-tries 1 --host-timeout 3s " . escapeshellarg($host);
        $output_nmap = shell_exec($cmd_nmap);

        if (preg_match('/OS details: (.+)/i', $output_nmap, $matches_nmap)) {
            return trim($matches_nmap[1]);
        } elseif (preg_match('/Aggressive OS guesses: (.+)/i', $output_nmap, $matches_nmap)) {
            return trim($matches_nmap[1]);
        }
        return null;
    }

    public function getTTL($host) {
        // Use a more efficient ping command with reduced packets and timeout
        $cmd = "ping -c 1 -W 1 " . escapeshellarg($host);
        $output = shell_exec($cmd);

        if (preg_match('/ttl=(\d+)/i', $output, $matches)) {
            return (int)$matches[1];
        }
        return 0;
    }
    
    // Check if the system is under high load
    private function isHighLoadPeriod() {
        $load = sys_getloadavg();
        // If 1-minute load average is above 2.0, consider it high load
        return ($load[0] > 2.0);
    }
    
    // Check if we have a valid cache entry for this IP
    private function checkCache($ip) {
        return isset($this->osCache[$ip]) && 
               (time() - $this->osCache[$ip]['time'] < $this->cacheTimeout);
    }
    
    // Store a result in the cache
    private function cacheResult($ip, $result) {
        $this->osCache[$ip] = [
            'result' => $result,
            'time' => time()
        ];
    }
}

// Initialize a simple cache to store OS detection results for the current request
$requestCache = [];

// Initialize default values
$defaultOs = 'unknown';
$defaultSvg = 'imgs/OS-Vendors/unknown-svgrepo-com.svg';
$defaultAlt = 'Unknown';

// Get the IP, external parameter, and deepscan parameter from the query
$ip = $_GET['ip'] ?? '';
$isExternal = isset($_GET['external']) && $_GET['external'] === 'True';
$deepScan = isset($_GET['deepscan']) && $_GET['deepscan'] === 'true';

// Check if we have this result in the request cache
$cacheKey = $ip . ($isExternal ? '_ext' : '') . ($deepScan ? '_deep' : '');
if (isset($requestCache[$cacheKey])) {
    echo json_encode($requestCache[$cacheKey]);
    exit;
}

// Check the database for an existing Vendor (skip if doing a deep scan)
if ($ip && !$deepScan) {
    $conn = getDatabaseConnection();

    $stmt = $conn->prepare("SELECT Vendor FROM hosts WHERE ip = ? LIMIT 1");
    if ($stmt === false) {
        error_log("Prepare failed: " . $conn->error);
    } else {
        $stmt->bind_param("s", $ip);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        if ($row && !is_null($row['Vendor']) && !empty($row['Vendor'])) {
            $defaultOs = $row['Vendor'];
            
            switch ($defaultOs) {
                case 'windows':
                    $defaultSvg = 'imgs/OS-Vendors/windows-174-svgrepo-com.svg';
                    $defaultAlt = 'Windows';
                    break;
                case 'linux':
                    $defaultSvg = 'imgs/OS-Vendors/linux-svgrepo-com.svg';
                    $defaultAlt = 'Linux';
                    break;
                case 'mac':
                    $defaultSvg = 'imgs/OS-Vendors/apple-inc-svgrepo-com.svg';
                    $defaultAlt = 'macOS';
                    break;
                case 'android':
                    $defaultSvg = 'imgs/OS-Vendors/android-svgrepo-com.svg';
                    $defaultAlt = 'Android';
                    break;
                case 'freebsd':
                    $defaultSvg = 'imgs/OS-Vendors/freebsd-svgrepo-com.svg';
                    $defaultAlt = 'FreeBSD';
                    break;
                case 'openbsd':
                    $defaultSvg = 'imgs/OS-Vendors/openbsd-svgrepo-com.svg';
                    $defaultAlt = 'OpenBSD';
                    break;
                case 'cisco':
                    $defaultSvg = 'imgs/OS-Vendors/cisco-svgrepo-com.svg';
                    $defaultAlt = 'Cisco';
                    break;
                case 'aruba':
                    $defaultSvg = 'imgs/OS-Vendors/Aruba_Networks_logo.svg';
                    $defaultAlt = 'Aruba';
                    break;
                case 'ruckus':
                    $defaultSvg = 'imgs/OS-Vendors/ruckus-logo.png';
                    $defaultAlt = 'Ruckus';
                    break;
                case 'apc':
                    $defaultSvg = 'imgs/OS-Vendors/APC-logo.svg';
                    $defaultAlt = 'APC';
                    break;
                case 'dell':
                    $defaultSvg = 'imgs/OS-Vendors/dell-2-logo-svgrepo-com.svg';
                    $defaultAlt = 'Dell';
                    break;
                case 'fortinet':
                    $defaultSvg = 'imgs/OS-Vendors/fortinet-svgrepo-com.svg';
                    $defaultAlt = 'Fortinet';
                    break;
                case 'hp':
                    $defaultSvg = 'imgs/OS-Vendors/hp-svgrepo-com.svg';
                    $defaultAlt = 'HP';
                    break;
                case 'vmware':
                    $defaultSvg = 'imgs/OS-Vendors/vmware-svgrepo-com.svg';
                    $defaultAlt = 'VMware';
                    break;
                case 'canon':
                    $defaultSvg = 'imgs/OS-Vendors/Canon_Logo.svg';
                    $defaultAlt = 'Canon';
                    break;
                case 'samsung':
                    $defaultSvg = 'imgs/OS-Vendors/samsung-svg.svg';
                    $defaultAlt = 'Samsung';
                    break;
                case 'lexmark':
                    $defaultSvg = 'imgs/OS-Vendors/Lexmark-Logo.svg';
                    $defaultAlt = 'Lexmark';
                    break;
                case 'azure':
                    $defaultSvg = 'imgs/OS-Vendors/azure-svgrepo-com.svg';
                    $defaultAlt = 'Azure';
                    break;
                case 'external':  // Added case for external
                    $defaultSvg = 'imgs/OS-Vendors/internet-svgrepo-com.svg';
                    $defaultAlt = 'External Network';
                    break;
                case 'unknown':
                default:
                    $defaultSvg = 'imgs/OS-Vendors/unknown-svgrepo-com.svg';
                    $defaultAlt = 'Unknown';
                    break;
            }

            $stmt->close();
            $conn->close();
            
            $response = [
                'os' => $defaultOs,
                'svg' => $defaultSvg,
                'alt' => $defaultAlt,
                'scan_type' => 'db',
                'can_deep_scan' => true
            ];
            
            // Store in request cache
            $requestCache[$cacheKey] = $response;
            
            echo json_encode($response);
            exit;
        }
        $stmt->close();
    }
}

// If no Vendor in database, proceed with detection only if we aren't handling too many
// simultaneous requests
$activeRequests = intval(shell_exec("pgrep -c nmap")) ?: 0;
if ($activeRequests > 5) {
    // Too many active nmap processes, return unknown for now
    $response = [
        'os' => 'unknown',
        'svg' => 'imgs/OS-Vendors/unknown-svgrepo-com.svg',
        'alt' => 'Detection Queued'
    ];
    echo json_encode($response);
    exit;
}

// If no Vendor in database, proceed with detection
$detector = new HostOSDetector();

// First check TTL for Windows detection
$ttl = $detector->getTTL($ip);
if ($ttl > 0 && $ttl >= 100 && $ttl <= 128) {
    $defaultOs = 'windows';
    $defaultSvg = 'imgs/OS-Vendors/windows-174-svgrepo-com.svg';
    $defaultAlt = 'Windows (TTL ' . $ttl . ')';
    
    // Update the hosts table with Windows detection
    if ($ip) {
        $conn = getDatabaseConnection();
        $stmt = $conn->prepare("UPDATE hosts SET Vendor = ? WHERE ip = ?");
        if ($stmt !== false) {
            $stmt->bind_param("ss", $defaultOs, $ip);
            $stmt->execute();
            $stmt->close();
        }
        $conn->close();
    }
    
    // Prepare and return the response
    $response = [
        'os' => $defaultOs,
        'svg' => $defaultSvg,
        'alt' => $defaultAlt,
        'scan_type' => 'basic',
        'can_deep_scan' => true
    ];
    
    // Store in request cache
    $requestCache[$cacheKey] = $response;
    
    echo json_encode($response);
    exit;
}

// If deepScan is requested, run the CLI scanner
if ($deepScan) {
    $cliScanCmd = "cd /var/www/html/ndd && php cliscan.php " . escapeshellarg($ip);
    $cliScanOutput = shell_exec($cliScanCmd);
    
    // Parse the output from cliscan.php to extract manufacturer and OS information
    $mfr = null;
    $os = null;
    
    // Look for manufacturer and OS in the output
    if (preg_match('/\[mfr\] => ([^\n]+)/i', $cliScanOutput, $mfr_matches)) {
        $mfr = trim($mfr_matches[1]);
    }
    
    if (preg_match('/\[os\] => ([^\n]+)/i', $cliScanOutput, $os_matches)) {
        $os = trim($os_matches[1]);
    }
    
    // If we found manufacturer info, use it to determine the OS
    if ($mfr) {
        // If manufacturer is VMware, check if the OS contains Linux, Windows, etc.
        if (stripos($mfr, 'VMware') !== false) {
            if ($os && strtolower($os) !== 'unknown') {
                // Check if OS field contains common OS names
                if (stripos($os, 'Linux') !== false) {
                    $detectionResult = "OS: Linux (detected by deep scan)";
                } elseif (stripos($os, 'Windows') !== false) {
                    $detectionResult = "OS: Windows (detected by deep scan)";
                } elseif (stripos($os, 'ESXi') !== false || stripos($os, 'vSphere') !== false) {
                    $detectionResult = "Vendor: VMware (detected by deep scan)";
                } else {
                    // Use the OS field as is
                    $detectionResult = "OS: " . $os . " (detected by deep scan)";
                }
            } else {
                // Use VMware as the vendor if OS is unknown or empty
                $detectionResult = "Vendor: VMware (detected by deep scan)";
            }
        } else {
            // Otherwise use the manufacturer as the vendor
            $detectionResult = "Vendor: " . $mfr . " (detected by deep scan)";
        }
    } else {
        // If no manufacturer found, fall back to standard detection
        $detectionResult = $detector->detectOS($ip, $isExternal);
        $detectionResult .= " | Deep scan performed but no vendor found";
    }
} else {
    // Standard detection using nmap
    $detectionResult = $detector->detectOS($ip, $isExternal);
}

// Parse the detection result
$defaultOs = 'unknown';
$defaultSvg = 'imgs/OS-Vendors/unknown-svgrepo-com.svg';
$defaultAlt = 'Unknown';

// If external is True and no vendor/os detected, use internet SVG
if ($isExternal && $detectionResult === "External network detected") {
    $defaultOs = 'external';
    $defaultSvg = 'imgs/OS-Vendors/internet-svgrepo-com.svg';
    $defaultAlt = 'External Network';
} else {
    // Normal OS detection logic
    if (strpos($detectionResult, 'OS: Windows') !== false) {
        $defaultOs = 'windows';
        $defaultSvg = 'imgs/OS-Vendors/windows-174-svgrepo-com.svg';
        $defaultAlt = 'Windows';
    } elseif (strpos($detectionResult, 'OS:') !== false) {
        if (preg_match('/OS: (.+?) \(/', $detectionResult, $matches)) {
            $os = strtolower(trim($matches[1]));
            if (strpos($os, 'linux') !== false) {
                $defaultOs = 'linux';
                $defaultSvg = 'imgs/OS-Vendors/linux-svgrepo-com.svg';
                $defaultAlt = 'Linux';
            } elseif (strpos($os, 'mac') !== false || strpos($os, 'apple') !== false) {
                $defaultOs = 'mac';
                $defaultSvg = 'imgs/OS-Vendors/apple-inc-svgrepo-com.svg';
                $defaultAlt = 'macOS';
            } elseif (strpos($os, 'android') !== false) {
                $defaultOs = 'android';
                $defaultSvg = 'imgs/OS-Vendors/android-svgrepo-com.svg';
                $defaultAlt = 'Android';
            } elseif (strpos($os, 'freebsd') !== false) {
                $defaultOs = 'freebsd';
                $defaultSvg = 'imgs/OS-Vendors/freebsd-svgrepo-com.svg';
                $defaultAlt = 'FreeBSD';
            } elseif (strpos($os, 'openbsd') !== false) {
                $defaultOs = 'openbsd';
                $defaultSvg = 'imgs/OS-Vendors/openbsd-svgrepo-com.svg';
                $defaultAlt = 'OpenBSD';
            }
        }
    }

    // Check for vendor - prioritize vendor detection over generic OS detection
    if (str_contains($detectionResult, 'Vendor:')) {
        if (preg_match('/Vendor: (.+?) \(/', $detectionResult, $matches)) {
            $vendor = strtolower(trim($matches[1]));
            
            // First check for specific vendors that should override OS detection
            if (str_contains($vendor, 'fortinet')) {
                $defaultOs = 'fortinet';
                $defaultSvg = 'imgs/OS-Vendors/fortinet-svgrepo-com.svg';
                $defaultAlt = 'Fortinet';
            } elseif (str_contains($vendor, 'cisco')) {
                $defaultOs = 'cisco';
                $defaultSvg = 'imgs/OS-Vendors/cisco-svgrepo-com.svg';
                $defaultAlt = 'Cisco';
            } elseif (str_contains($vendor, 'vmware')) {
                $defaultOs = 'vmware';
                $defaultSvg = 'imgs/OS-Vendors/vmware-svgrepo-com.svg';
                $defaultAlt = 'VMware';
            } elseif (str_contains($vendor, 'aruba')) {
                $defaultOs = 'aruba';
                $defaultSvg = 'imgs/OS-Vendors/Aruba_Networks_logo.svg';
                $defaultAlt = 'Aruba';
            } elseif (str_contains($vendor, 'ruckus')) {
                $defaultOs = 'ruckus';
                $defaultSvg = 'imgs/OS-Vendors/ruckus-logo.png';
                $defaultAlt = 'Ruckus';
            } elseif (str_contains($vendor, 'apc')) {
                $defaultOs = 'apc';
                $defaultSvg = 'imgs/OS-Vendors/APC-logo.svg';
                $defaultAlt = 'APC';
            } elseif (str_contains($vendor, 'canon')) {
                $defaultOs = 'canon';
                $defaultSvg = 'imgs/OS-Vendors/Canon_Logo.svg';
                $defaultAlt = 'Canon';
            } elseif (str_contains($vendor, 'samsung')) {
                $defaultOs = 'samsung';
                $defaultSvg = 'imgs/OS-Vendors/samsung-svg.svg';
                $defaultAlt = 'Samsung';
            } elseif (str_contains($vendor, 'lexmark')) {
                $defaultOs = 'lexmark';
                $defaultSvg = 'imgs/OS-Vendors/Lexmark-Logo.svg';
                $defaultAlt = 'Lexmark';
            }
            // Then check for other vendors that might indicate OS
            elseif (str_contains($vendor, 'apple')) {
                $defaultOs = 'mac';
                $defaultSvg = 'imgs/OS-Vendors/apple-inc-svgrepo-com.svg';
                $defaultAlt = 'macOS';
            } elseif (str_contains($vendor, 'azure')) {
                $defaultOs = 'azure';
                $defaultSvg = 'imgs/OS-Vendors/azure-svgrepo-com.svg';
                $defaultAlt = 'Azure';
            } elseif (str_contains($vendor, 'dell')) {
                $defaultOs = 'dell';
                $defaultSvg = 'imgs/OS-Vendors/dell-2-logo-svgrepo-com.svg';
                $defaultAlt = 'Dell';
            } elseif (str_contains($vendor, 'hp') || str_contains($vendor, 'hewlett') || str_contains($vendor, 'packard')) {
                $defaultOs = 'hp';
                $defaultSvg = 'imgs/OS-Vendors/hp-svgrepo-com.svg';
                $defaultAlt = 'HP';
            }
        }
    }
}

// Update the hosts table
if ($ip) {
    $conn = getDatabaseConnection();

    $stmt = $conn->prepare("UPDATE hosts SET Vendor = ? WHERE ip = ?");
    if ($stmt === false) {
        error_log("Prepare failed: " . $conn->error);
    } else {
        $stmt->bind_param("ss", $defaultOs, $ip);
        if (!$stmt->execute()) {
            error_log("Execute failed: " . $stmt->error);
        }
        $stmt->close();
    }

    $conn->close();
}

// Prepare the response
$response = [
    'os' => $defaultOs,
    'svg' => $defaultSvg,
    'alt' => $defaultAlt,
    'scan_type' => $deepScan ? 'deep' : 'basic',
    'can_deep_scan' => true
];

// Store in request cache
$requestCache[$cacheKey] = $response;

// Return the result as JSON
echo json_encode($response);
?>