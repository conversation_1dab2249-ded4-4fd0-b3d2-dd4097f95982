<?php
/**
 * Universal main view configuration setter
 * Usage: include 'set_main_view.php'; setMainView('bubble');
 */

function setMainView($viewType) {
    $config_file = 'conf/bubblemap_configs';
    
    // Read the current configuration
    $config_content = file_get_contents($config_file);
    
    // Check if "main view" line exists
    $main_view_exists = false;
    $lines = explode("\n", $config_content);
    
    foreach ($lines as $index => $line) {
        $line = trim($line);
        if (strpos($line, 'main view:') === 0) {
            $main_view_exists = true;
            // Update the existing line
            $lines[$index] = "main view: $viewType";
            break;
        }
    }
    
    // If "main view" doesn't exist, add it
    if (!$main_view_exists) {
        $lines[] = "main view: $viewType";
    }
    
    // Write back to the configuration file
    $new_config_content = implode("\n", $lines);
    file_put_contents($config_file, $new_config_content);
}
?>
