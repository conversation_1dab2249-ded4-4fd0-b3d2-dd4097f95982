<?php
include 'language_selector.php';
include 'theme_loader.php'; // Include the theme loader
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Blesk - Dashboards</title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/networkmap.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">

    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <script src="functions/scanModal.js"></script>
    <script src="functions/helperFunctions.js"></script>
    <script src="functions/networkmapFunctions/iframeHandler.js"></script>
    <script src="functions/setMainView.js"></script>
    <script src="functions/globalAiAssistant.js"></script>
</head>

<body>
    <header>
        <div style="display: flex; align-items: center; gap: 15px;">
            <a href="../../index.php">
                <img style="width: 25px;" src="../../images/blesk-favicon.svg" alt="Home">
            </a>
            <div class="breadcrumbs">
                <a href="infra.php<?php echo isset($_GET['subnet']) ? '?subnet=true' : ''; ?>">
                    <i class="fa fa-home"></i> Home
                </a>
                <span class="separator">/</span>
                <a href="#" class="current" id="dashboardsBreadcrumb">
                    <i class="fa fa-map"></i> Dashboards and Visualization
                </a>
                <span class="separator" id="mapBreadcrumbSeparator" style="display:none;">/</span>
                <span class="current" id="mapBreadcrumbAlias" style="display:none;"></span>
            </div>
        </div>
        
        <div class="header-content">
            <div class="hamburger">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </div>
            <div class="header-buttons">
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="viewDropdownBtn"><i class="fa fa-eye"></i> View <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="viewDropdownContent">
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'bubble', 'https://<?php echo $_SERVER['HTTP_HOST']; ?>/bubblemaps/infra.php')"><i class="fa fa-th"></i> Infrastructure Mapping</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'list', 'hostlist.php')"><i class="fa fa-list"></i> Asset Overview</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'table', 'tableview.php')"><i class="fa fa-table"></i> Data Grid</a>
                    </div>
                </div>
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="menuDropdownBtn"><i class="fa fa-bars"></i> Control Panel <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="menuDropdownContent">
                        <a class="header-button" id="formModal-button"><i class="fa fa-search"></i> Scan</a>
                        <a class="header-button" onclick='showModal("https://<?php echo $_SERVER["HTTP_HOST"]; ?>/bubblemaps/credentials.php")'><i class="fa fa-key"></i> Credentials</a>
                        <a class="header-button" href="notifications.php"><i class="fa fa-bell"></i> Notifications</a>
                        <a class="header-button" href="reports.php"><i class="fa fa-file-text"></i> Reports</a>
                    </div>
                </div>
                <a class="header-button" id="openSettingsBtn"><i class="fa fa-cog"></i> Advanced Settings</a>
            </div>
        </div>
    </header>
    
    <?php include "settingsModal.php"; ?>
    
    <!-- iframe Modal -->
    <div id="infoModal" class="iframeModal">
        <div class="iframeModal-content" id="iframeModal-content">
            <span class="iframeMclose" title="Back" aria-label="Back">←</span>
            <div class="iframe-loader"></div>
            <iframe id="modal-frame"></iframe>
        </div>
    </div>
    
    <div class="networkmap-container">
        <iframe 
            class="networkmap-iframe" 
            src="https://<?php echo $_SERVER['HTTP_HOST']; ?>/nagvis/frontend/nagvis-js/index.php"
            title="Dashboards"
            allowfullscreen>
        </iframe>
    </div>
    
    <script>
        const urlParams = new URLSearchParams(window.location.search);
        const dictionaries = <?php echo json_encode($dictionaries); ?>;
        const selectedLang = '<?php echo $selectedLang; ?>';
        

        
        // Initialize the translator
        const translator = new Translator(dictionaries, selectedLang);
        translator.init();
        initTranslationObserver(translator);
        
        // Initialize scan modal for networkmap page
        window.scanModal = new ScanModal({
            modalId: 'formModal',
            hasAzureSupport: true,
            hasScanTypeSelector: true,
            hasInternalCheckbox: true,
            ipLabel: 'Enter a single ip, ip range or url.',
            forceScan: true
        });
        
        // Handle hamburger menu
        const hamburger = document.querySelector('.hamburger');
        const headerButtons = document.querySelector('.header-buttons');

        hamburger.addEventListener('click', function(e) {
            e.stopPropagation();
            this.classList.toggle('active');
            headerButtons.classList.toggle('active');
        });

        document.addEventListener('click', function(event) {
            if (!event.target.closest('.header-content')) {
                hamburger.classList.remove('active');
                headerButtons.classList.remove('active');
            }
        });
        
        // Handle View dropdown
        const viewDropdownBtn = document.getElementById('viewDropdownBtn');
        const viewDropdownContent = document.getElementById('viewDropdownContent');
        if(viewDropdownBtn && viewDropdownContent){
            viewDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                // Close other dropdowns first
                const menuDropdownContent = document.getElementById('menuDropdownContent');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
                viewDropdownContent.classList.toggle('show');
            });
        }
        
        // Handle Control Panel dropdown
        const menuDropdownBtn = document.getElementById('menuDropdownBtn');
        const menuDropdownContent = document.getElementById('menuDropdownContent');
        if(menuDropdownBtn && menuDropdownContent){
            menuDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                // Close other dropdowns first
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                menuDropdownContent.classList.toggle('show');
            });
        }
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e){
            if(!e.target.closest('.header-dropdown')){
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
            }
        });
        
        // Show modal function
        function showModal(url) {
            const modal = document.getElementById('infoModal');
            const iframe = document.getElementById('modal-frame');
            const modalBody = document.getElementById('iframeModal-content');

            if (url.includes('credentials.php')){
                modalBody.style.maxHeight = '800px';
                modalBody.style.maxWidth = '1000px';
                // Remove 'active' class from header-buttons when opening credentials modal
                ['.header-buttons', '.hamburger'].forEach(sel => {
                    const el = document.querySelector(sel);
                    if (el) el.classList.remove('active');
                });
            } else {
                // Explicitly reset all styles for non-credentials modals
                modalBody.style.maxHeight = '';
                modalBody.style.maxWidth = '';
                modalBody.style.top = '';
                modalBody.style.width = '';
                modalBody.style.height = '';
            }

            modal.classList.remove('loaded');
            iframe.style.display = 'none';
            iframe.src = url;
            modal.style.display = 'block';
            modal.classList.add('show');
            modal.classList.remove('small');

            iframe.onload = function() {
                const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                const navElement = iframeDocument.querySelector("nav");
                if (navElement) {
                    navElement.style.display = "none";
                }
                
                // Translate the iframe content
                if (typeof translator !== 'undefined') {
                    translator.translateIframe(iframe);
                }
                modal.classList.add('loaded');
                iframe.style.display = 'block';
            };
        }
        
        // Close iframe modal when clicking the close button (with closing animation)
        const closeButton = document.querySelector('.iframeMclose');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                const modal = document.getElementById('infoModal');
                if (!modal) return;
                modal.classList.remove('show');
                modal.classList.add('closing');
                setTimeout(() => {
                    modal.classList.remove('closing');
                    modal.style.display = 'none';
                }, 300);
            });
        }
        
        // Close modal when clicking outside (with closing animation)
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('infoModal');
            if (event.target == modal) {
                modal.classList.remove('show');
                modal.classList.add('closing');
                setTimeout(() => {
                    modal.classList.remove('closing');
                    modal.style.display = 'none';
                }, 300);
            }
        });

        // Breadcrumb: Map alias management and Dashboards reset
        (function(){
            const iframe = document.querySelector('.networkmap-iframe');
            const initialSrc = 'https://<?php echo $_SERVER["HTTP_HOST"]; ?>/nagvis/frontend/nagvis-js/index.php';
            const dashboardsCrumb = document.getElementById('dashboardsBreadcrumb') || document.querySelector('.breadcrumbs .current');
            const aliasEl = document.getElementById('mapBreadcrumbAlias');
            const sepEl = document.getElementById('mapBreadcrumbSeparator');

            function setAlias(alias){
                if (!alias || alias.trim() === '') {
                    if (aliasEl) aliasEl.style.display = 'none';
                    if (sepEl) sepEl.style.display = 'none';
                    if (aliasEl) aliasEl.textContent = '';
                    return;
                }
                if (aliasEl) {
                    aliasEl.textContent = alias;
                    aliasEl.style.display = 'inline';
                }
                if (sepEl) {
                    sepEl.style.display = 'inline';
                }
            }

            function getAlias(){
                try {
                    const win = iframe && (iframe.contentWindow || (iframe.contentDocument && iframe.contentDocument.defaultView));
                    if (!win) return null;
                    const props = win.oPageProperties;
                    if (props && (props.view_type === 'map' || props.view_type === 'automap')) {
                        return props.alias || props.map_name || null;
                    }
                } catch(e){}
                return null;
            }

            function refreshAlias(){
                const alias = getAlias();
                if (alias) setAlias(alias); else setAlias(null);
            }

            if (iframe) {
                iframe.addEventListener('load', function(){
                    // Reset alias on each load and try to set after a brief delay
                    setAlias(null);
                    setTimeout(refreshAlias, 300);
                    setTimeout(refreshAlias, 1000);
                });
            }

            // Poll periodically in case the iframe updates content without a full reload
            setInterval(refreshAlias, 2000);

            if (dashboardsCrumb) {
                dashboardsCrumb.style.cursor = 'pointer';
                dashboardsCrumb.addEventListener('click', function(e){
                    e.preventDefault();
                    if (!iframe) return;
                    iframe.src = initialSrc;
                    setAlias(null);
                });
            }
        })();
    </script>
    
</body>
</html>
