<?php
header('Content-Type: application/json');
include "loadenv.php";

try {
    // Validate input parameters
    if (!isset($_GET['ip'])) {
        throw new Exception("Missing required parameter: ip");
    }
    
    $ip = trim($_GET['ip']);
    
    if (empty($ip)) {
        throw new Exception("IP address cannot be empty");
    }
    
    // Connect to bubblemaps database
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    
    // Get host ID by IP address
    $sql = "SELECT id FROM hosts WHERE ip = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        $conn->close();
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param('s', $ip);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $stmt->close();
        $conn->close();
        throw new Exception("No host found with IP address: " . $ip);
    }
    
    $row = $result->fetch_assoc();
    $hostId = $row['id'];
    
    $stmt->close();
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'hostId' => $hostId,
        'ip' => $ip
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
