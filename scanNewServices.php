<?php
/**
 * Scan for new services that can be added to monitoring
 * This uses the same NDD scanning process as background_scan.php
 * but returns results directly for user selection
 */

// Include necessary files and set up error handling
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');

// Include necessary files
include_once 'loadenv.php';

function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "blesk";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();

    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "tfUsername" => $row["username"],
            "tfPassword" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }

    $conn->close();

    return $userCredentials;
}

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function getHostnameByIP($ip) {
    try {
        // Get credentials from the database
        $credentials = getUserCredentials();
        $hostname = getSelfIp();
        
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials");
        }

        $url = "https://$hostname/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true";
        
        error_log("DEBUG: Trying to get hostname for IP: $ip");
        error_log("DEBUG: Using Nagios URL: $url");
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        // Set HTTP Basic Authentication credentials
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        error_log("DEBUG: HTTP Code: $httpCode");
        if ($curlError) {
            error_log("DEBUG: cURL Error: $curlError");
        }
        
        if ($httpCode !== 200) {
            error_log("DEBUG: Non-200 response from Nagios API");
            throw new Exception("HTTP $httpCode returned from Nagios API");
        }
        
        $data = json_decode($response, true);
        if (!$data) {
            error_log("DEBUG: Failed to decode JSON response");
            error_log("DEBUG: Response was: " . substr($response, 0, 500));
            throw new Exception("Invalid JSON response from Nagios API");
        }
        
        if (!isset($data['data']['hostlist'])) {
            error_log("DEBUG: No hostlist in response");
            error_log("DEBUG: Response structure: " . print_r(array_keys($data), true));
            throw new Exception("No hostlist in Nagios API response");
        }
        
        error_log("DEBUG: Found " . count($data['data']['hostlist']) . " hosts in Nagios");
        
        foreach ($data['data']['hostlist'] as $hostName => $hostData) {
            if (isset($hostData['address'])) {
                error_log("DEBUG: Checking host $hostName with address " . $hostData['address']);
                if ($hostData['address'] === $ip) {
                    error_log("DEBUG: Found matching host: $hostName");
                    return $hostName;
                }
            }
        }
        
        error_log("DEBUG: No matching host found for IP: $ip");
        return null;
    } catch (Exception $e) {
        error_log("Error in getHostnameByIP: " . $e->getMessage());
        return null;
    }
}

function scanHostForServices($hostname, $ip) {
    try {
        // Get credentials for authentication
        $credentials = getUserCredentials();
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials for NDD scan");
        }
        
        $selfHostname = getSelfIp();
        
        $ch = curl_init("https://$selfHostname/ndd/host-ip-scan-proc.php");
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => [
                'iprange' => $ip,
                'caller' => 'host-ip',
                'subnet' => '',
                'url_ip' => $ip
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 300, // 5 minutes timeout
            CURLOPT_USERPWD => $credentials['tfUsername'] . ':' . $credentials['tfPassword'],
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            throw new Exception('Scan failed: ' . curl_error($ch));
        }
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP $httpCode returned from scan");
        }
        
        $effectiveUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        curl_close($ch);
        
        parse_str(parse_url($effectiveUrl, PHP_URL_QUERY), $params);
        
        if (!isset($params['scanfile'])) {
            throw new Exception('No scanfile returned from scan');
        }
        
        return $params['scanfile'];
    } catch (Exception $e) {
        error_log("Error in scanHostForServices: " . $e->getMessage());
        throw $e;
    }
}

function parseScanResults($scanfile, $hostIp) {
    try {
        $scanfilePath = "/usr/share/blesk/tmp/$scanfile";
        
        if (!file_exists($scanfilePath)) {
            throw new Exception("Scanfile not found: $scanfilePath");
        }
        
        // Instead of trying to parse the scan object directly, let's fetch the host-ip.php page
        // like services_background_scan.php does to get the formatted results
        $credentials = getUserCredentials();
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials for parsing results");
        }
        
        $selfHostname = getSelfIp();
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => "https://$selfHostname/ndd/host-ip.php?ip=$hostIp&scanfile=$scanfile",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_USERPWD => $credentials['tfUsername'] . ':' . $credentials['tfPassword'],
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false || $httpCode !== 200) {
            throw new Exception("Failed to fetch service information from host-ip.php");
        }
        
        $services = [];
        $validItems = [];
        
        // Extract services that can be added (using same logic as services_background_scan.php)
        if (preg_match('/<h3>Services that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $matches)) {
            $servicesHtml = $matches[1];
            // Remove HTML table content to avoid picking up template options
            $servicesHtml = preg_replace('/<table[^>]*>.*?<\/table>/s', '', $servicesHtml);
            preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $servicesHtml, $serviceMatches);
            
            if (!empty($serviceMatches[1])) {
                foreach ($serviceMatches[1] as $service) {
                    $trimmed = trim($service);
                    if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                        $validItems[] = $trimmed;
                        $services[] = [
                            'name' => $trimmed,
                            'category' => 'Services'
                        ];
                    }
                }
            }
        }
        
        // Extract port interfaces that can be added
        if (preg_match('/<h3>Port interfaces that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $portMatches)) {
            $portsHtml = $portMatches[1];
            // Remove HTML table content to avoid picking up template options
            $portsHtml = preg_replace('/<table[^>]*>.*?<\/table>/s', '', $portsHtml);
            preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $portsHtml, $portServiceMatches);
            
            if (!empty($portServiceMatches[1])) {
                foreach ($portServiceMatches[1] as $port) {
                    $trimmed = trim($port);
                    if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                        $validItems[] = $trimmed;
                        $services[] = [
                            'name' => $trimmed,
                            'category' => 'Port Interfaces'
                        ];
                    }
                }
            }
        }
        
        // Extract managed APs that can be added
        if (preg_match('/<h3>Managed APs that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $apMatches)) {
            $apsHtml = $apMatches[1];
            // Remove HTML table content to avoid picking up template options
            $apsHtml = preg_replace('/<table[^>]*>.*?<\/table>/s', '', $apsHtml);
            preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $apsHtml, $apServiceMatches);
            
            if (!empty($apServiceMatches[1])) {
                foreach ($apServiceMatches[1] as $ap) {
                    $trimmed = trim($ap);
                    if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                        $validItems[] = $trimmed;
                        $services[] = [
                            'name' => $trimmed,
                            'category' => 'Managed APs'
                        ];
                    }
                }
            }
        }
        
        // Extract database services (MSSQL, Oracle, MySQL)
        $dbTypes = ['MSSQL', 'Oracle', 'MySQL'];
        foreach ($dbTypes as $dbType) {
            if (preg_match("/<h3>$dbType services that can be added<\/h3>(.*?)(?:<h3>|$)/s", $response, $dbMatches)) {
                $dbHtml = $dbMatches[1];
                // Remove HTML table content to avoid picking up template options
                $dbHtml = preg_replace('/<table[^>]*>.*?<\/table>/s', '', $dbHtml);
                preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $dbHtml, $dbServiceMatches);
                
                if (!empty($dbServiceMatches[1])) {
                    foreach ($dbServiceMatches[1] as $dbService) {
                        $trimmed = trim($dbService);
                        if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                            $validItems[] = $trimmed;
                            $services[] = [
                                'name' => $trimmed,
                                'category' => $dbType . ' Services'
                            ];
                        }
                    }
                }
            }
        }
        
        // Extract FortiGate tunnels that can be added
        if (preg_match('/<h3>Fortigate tunnels that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $tunnelMatches)) {
            $tunnelsHtml = $tunnelMatches[1];
            // Remove HTML table content to avoid picking up template options
            $tunnelsHtml = preg_replace('/<table[^>]*>.*?<\/table>/s', '', $tunnelsHtml);
            preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $tunnelsHtml, $tunnelServiceMatches);
            
            if (!empty($tunnelServiceMatches[1])) {
                foreach ($tunnelServiceMatches[1] as $tunnel) {
                    $trimmed = trim($tunnel);
                    if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                        $validItems[] = $trimmed;
                        $services[] = [
                            'name' => $trimmed,
                            'category' => 'FortiGate Tunnels'
                        ];
                    }
                }
            }
        }
        
        // Extract Managed FortiSwitches that can be added
        if (preg_match('/<h3>Managed FortiSwitches that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $fortiswitchMatches)) {
            $fortiswitchHtml = $fortiswitchMatches[1];
            // Remove HTML table content to avoid picking up template options
            $fortiswitchHtml = preg_replace('/<table[^>]*>.*?<\/table>/s', '', $fortiswitchHtml);
            preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $fortiswitchHtml, $fortiswitchServiceMatches);
            
            if (!empty($fortiswitchServiceMatches[1])) {
                foreach ($fortiswitchServiceMatches[1] as $fortiswitch) {
                    $trimmed = trim($fortiswitch);
                    if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                        $validItems[] = $trimmed;
                        $services[] = [
                            'name' => $trimmed,
                            'category' => 'Managed FortiSwitches'
                        ];
                    }
                }
            }
        }
        
        // Add IDs and descriptions to services
        foreach ($services as $index => &$service) {
            $service['id'] = $index;
            $service['description'] = 'Service detected by scan';
            $service['host'] = $hostIp;
        }
        
        return [
            'scanfile' => $scanfile,
            'services' => $services
        ];
    } catch (Exception $e) {
        error_log("Error parsing scan results: " . $e->getMessage());
        throw $e;
    }
}

// Main execution
try {
    // Validate input
    if (!isset($_POST['hostip']) || !isset($_POST['hostname'])) {
        throw new Exception('Missing required parameters');
    }
    
    $hostIp = trim($_POST['hostip']);
    $hostname = trim($_POST['hostname']);
    
    // Validate IP address
    if (!filter_var($hostIp, FILTER_VALIDATE_IP)) {
        throw new Exception('Invalid IP address format');
    }
    
    // Get the real hostname from Nagios
    $realHostname = getHostnameByIP($hostIp);
    if (!$realHostname) {
        throw new Exception('Host not found in Nagios. Please ensure the host is properly configured.');
    }
    
    // Perform the scan
    $scanfile = scanHostForServices($realHostname, $hostIp);
    
    // Parse the scan results
    $results = parseScanResults($scanfile, $hostIp);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Scan completed successfully',
        'scanfile' => $results['scanfile'],
        'services' => $results['services'],
        'host' => [
            'ip' => $hostIp,
            'hostname' => $realHostname
        ]
    ]);
    
} catch (Exception $e) {
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
