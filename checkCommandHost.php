<?php
include "loadenv.php";
include 'theme_loader.php'; // Include the theme loader
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection
$db_host = $_ENV["DB_SERVER"];
$db_user = $_ENV["DB_USER"];
$db_pass = $_ENV["DB_PASSWORD"];
$db_name = 'db_nagiosql_v3';
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Prevent caching
header("Cache-Control: no-cache, must-revalidate");

// Fetch parameters (unchanged)
$hostname = $_GET['hostname'] ?? '';
$host_id = null;
$check_command = '';
$command_id = null;
$command_args = array_fill(0, 8, ''); 
$command_line = '';
$commands = []; 
$all_hosts = []; 
$parent_hosts = []; 
$child_host_ids = []; 
$host_addresses = [];
$bubble_hostnames = [];

// Functions `getSelfIp()` and `simulateVerifyActions()` remain unchanged
function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function simulateVerifyActions($ip) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
        ]);

        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception('Login failed: ' . curl_error($ch));
        }
        error_log("Login response code: " . curl_getinfo($ch, CURLINFO_HTTP_CODE));

        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            error_log("Redirecting to: $redirect");
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                throw new Exception("Action $action failed: " . curl_error($ch));
            }
            error_log("Action $action completed with response code: " . curl_getinfo($ch, CURLINFO_HTTP_CODE));
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log("Error in simulateVerifyActions: " . $e->getMessage());
    }
}

// Fetch commands and hosts (unchanged)
$sql = "SELECT id, command_name, command_line FROM tbl_command ORDER BY command_name ASC";
$result = $conn->query($sql);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $commands[$row['id']] = [
            'command_name' => $row['command_name'],
            'command_line' => $row['command_line']
        ];
    }
} else {
    die("Failed to fetch commands: " . $conn->error);
}

// Fetch hosts (including IP address) from NagiosQL DB
$sql = "SELECT id, host_name, address FROM tbl_host ORDER BY host_name ASC";
$result = $conn->query($sql);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $all_hosts[$row['id']] = $row['host_name'];
        $host_addresses[$row['id']] = $row['address'];
    }
} else {
    die("Failed to fetch hosts: " . $conn->error);
}

// ---------------------------------------------------------------------------
// Fetch Bubble View nicknames (hostname column) keyed by IP from bubblemaps DB
// ---------------------------------------------------------------------------
$bub_conn = new mysqli($db_host, $db_user, $db_pass, 'bubblemaps');
if (!$bub_conn->connect_error) {
    $bub_sql = "SELECT ip, hostname FROM hosts WHERE blacklist = 0";
    $bub_result = $bub_conn->query($bub_sql);
    if ($bub_result) {
        while ($row = $bub_result->fetch_assoc()) {
            $bubble_hostnames[$row['ip']] = $row['hostname'];
        }
    }
    $bub_conn->close();
}

if (!empty($hostname)) {
    $sql = "SELECT id, check_command FROM tbl_host WHERE host_name = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        die("Prepare failed: " . $conn->error);
    }
    $stmt->bind_param("s", $hostname);
    $stmt->execute();
    $result = $stmt->get_result();
    $host_data = $result->fetch_assoc();
    $stmt->close();

    if ($host_data) {
        $host_id = $host_data['id'];
        $check_command = $host_data['check_command'] ?? '';

        if (!empty($check_command)) {
            $parts = explode('!', $check_command);
            $command_id = (int)($parts[0] ?? 0);
            for ($i = 1; $i <= 8 && $i < count($parts); $i++) {
                $command_args[$i - 1] = $parts[$i];
            }
        }

        if ($command_id && isset($commands[$command_id])) {
            $command_line = $commands[$command_id]['command_line'];
        }

        $sql = "SELECT idMaster FROM tbl_lnkHostToHost WHERE idSlave = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $host_id);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $child_host_ids[] = $row['idMaster'];
        }
        $stmt->close();

        $sql = "SELECT idSlave FROM tbl_lnkHostToHost WHERE idMaster = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $host_id);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $parent_hosts[] = $row['idSlave'];
        }
        $stmt->close();
    }
}

// Handle form submission with parents update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $host_id) {
    // Update check_command
    $new_command_id = (int)($_POST['command_id'] ?? $command_id);
    $new_args = [];
    for ($i = 0; $i < 8; $i++) {
        $arg = $_POST["arg$i"] ?? '';
        if ($arg !== '') {
            $new_args[] = $arg;
        }
    }
    $new_check_command = $new_command_id . (!empty($new_args) ? '!' . implode('!', $new_args) : '');

    // Determine if the host has parents
    $new_parent_hosts = !empty($_POST['parent_hosts']) ? array_map('intval', $_POST['parent_hosts']) : [];
    $has_parents = !empty($new_parent_hosts) ? 1 : 0;

    // Update tbl_host with check_command and parents
    $sql = "UPDATE tbl_host SET check_command = ?, parents = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        die("Prepare failed: " . $conn->error);
    }
    $stmt->bind_param("sii", $new_check_command, $has_parents, $host_id);
    if (!$stmt->execute()) {
        die("Execute failed: " . $stmt->error);
    }
    $stmt->close();

    // Update parent hosts in tbl_lnkHostToHost
    $sql = "DELETE FROM tbl_lnkHostToHost WHERE idMaster = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $host_id);
    $stmt->execute();
    $stmt->close();

    if (!empty($new_parent_hosts)) {
        $sql = "INSERT INTO tbl_lnkHostToHost (idMaster, idSlave) VALUES (?, ?)";
        $stmt = $conn->prepare($sql);
        foreach ($new_parent_hosts as $parent_id) {
            if ($parent_id !== $host_id && !in_array($parent_id, $child_host_ids)) {
                $stmt->bind_param("ii", $host_id, $parent_id);
                $stmt->execute();
            }
        }
        $stmt->close();
    }

    
    simulateVerifyActions(getSelfIp());
    sleep(3);
    // Refresh data after update
    $sql = "SELECT check_command FROM tbl_host WHERE id = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        die("Prepare failed: " . $conn->error);
    }
    $stmt->bind_param("i", $host_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $host_data = $result->fetch_assoc();
    $stmt->close();

    if ($host_data) {
        $check_command = $host_data['check_command'] ?? '';
        $parts = explode('!', $check_command);
        $command_id = (int)($parts[0] ?? 0);
        $command_args = array_fill(0, 8, '');
        for ($i = 1; $i <= 8 && $i < count($parts); $i++) {
            $command_args[$i - 1] = $parts[$i];
        }
        if ($command_id && isset($commands[$command_id])) {
            $command_line = $commands[$command_id]['command_line'];
        }
    }

    // Simple header redirect to prevent form resubmission
    $section = $_GET['section'] ?? 'all';
    $redirectUrl = "checkCommandHost.php?hostname=" . urlencode($hostname);
    if ($section !== 'all') {
        $redirectUrl .= "&section=" . urlencode($section);
    }
    header("Location: " . $redirectUrl);
    exit;
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Command for <?php echo htmlspecialchars($hostname ?: 'No Host'); ?></title>
    <link href="styles/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/advancedCommands.css">
</head>
<body>
    <div class="container">
        <h1><?php echo htmlspecialchars($hostname ?: 'No Host'); ?></h1>

        <?php if (empty($host_id) && !empty($hostname)): ?>
            <p class="no-data">No data found for host: <?php echo htmlspecialchars($hostname); ?></p>
        <?php endif; ?>

        <form method="POST">
            <?php 
            $section = $_GET['section'] ?? 'all'; // Default to showing all sections
            if ($section === 'parents' || $section === 'all'): ?>
            <div class="form-section">
                <h2>Parent Hosts</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Select Parent Hosts</label>
                        <select name="parent_hosts[]" class="select2" multiple>
                            <?php foreach ($all_hosts as $id => $host_name): ?>
                                <?php if ($id !== $host_id && !in_array($id, $child_host_ids)): // Exclude current host and its children ?>
                                    <?php 
                                        $optionText = htmlspecialchars($host_name);
                                        $ipAddress = $host_addresses[$id] ?? '';
                                        $bubbleName = $bubble_hostnames[$ipAddress] ?? '';
                                        if ($bubbleName && strcasecmp($bubbleName, $host_name) !== 0) {
                                            $optionText .= ' (' . htmlspecialchars($bubbleName) . ')';
                                        }
                                    ?>
                                    <option value="<?php echo $id; ?>" <?php echo in_array($id, $parent_hosts) ? 'selected' : ''; ?>>
                                        <?php echo $optionText; ?>
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($section === 'all'): ?>
            <div class="form-section">
                <h2>Command Settings</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Command</label>
                        <select name="command_id" id="command-select">
                            <option value="">-- Select a Command --</option>
                            <?php foreach ($commands as $id => $command): ?>
                                <option value="<?php echo $id; ?>" <?php echo $id === $command_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($command['command_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Command Line</label>
                        <input type="text" id="command-line" value="<?php echo htmlspecialchars($command_line); ?>" disabled>
                    </div>
                </div>
                <div class="form-grid">
                    <?php for ($i = 0; $i < 8; $i++): ?>
                        <div class="form-group">
                            <label>$ARG<?php echo $i + 1; ?>$</label>
                            <input type="text" name="arg<?php echo $i; ?>" class="arg-input" value="<?php echo htmlspecialchars($command_args[$i]); ?>">
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($host_id): ?>
            <button type="submit" id="save-button" class="save-button">Save Changes</button>
            <?php endif; ?>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for parent hosts
            $('.select2').select2({
                placeholder: "Select parent hosts",
                allowClear: true,
                width: '100%'
            });

            <?php if ($section === 'all'): ?>
            const commands = <?php echo json_encode($commands); ?>;
            const commandSelect = document.getElementById('command-select');
            const commandLineInput = document.getElementById('command-line');
            const argInputs = document.querySelectorAll('.arg-input');

            commandSelect.addEventListener('change', function() {
                const selectedId = this.value;
                if (selectedId && commands[selectedId]) {
                    commandLineInput.value = commands[selectedId].command_line;
                } else {
                    commandLineInput.value = '';
                }
                // Clear all argument fields
                argInputs.forEach(input => {
                    input.value = '';
                });
            });
            <?php endif; ?>

            document.getElementById('save-button')?.addEventListener('click', function() {
                this.textContent = 'Saving...';
            });
        });
    </script>
</body>
</html>