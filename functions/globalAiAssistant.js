// Global AI Assistant - accessible on all pages with F2
(function() {
    'use strict';

    // Global AI Assistant state
    let isVisible = false;
    let chatHistory = [];

    // Initialize the AI assistant when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        createAiAssistant();
        setupEventListeners();
    });

    // Create the AI assistant UI
    function createAiAssistant() {
        // Create the main container
        const aiContainer = document.createElement('div');
        aiContainer.id = 'global-ai-assistant';
        aiContainer.className = 'global-ai-assistant';
        aiContainer.style.display = 'none';

        // Create the chat interface
        aiContainer.innerHTML = `
            <div class="ai-chat-container">
                <div class="ai-chat-header">
                    <div class="ai-chat-title">
                        <img src="imgs/icons/ai-avatar.png" alt="AI Assistant" class="ai-avatar">
                        <span>AI Assistant</span>
                    </div>
                    <div class="ai-chat-controls">
                        <button class="ai-chat-minimize" title="Minimize (F2)">
                            <i class="fa fa-minus"></i>
                        </button>
                        <button class="ai-chat-close" title="Close">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="ai-chat-messages" id="ai-chat-messages">
                </div>
                
                <div class="ai-chat-input-container">
                    <div class="ai-chat-input-wrapper">
                        <textarea 
                            id="ai-chat-input" 
                            class="ai-chat-input" 
                            placeholder="Ask me anything..."
                            rows="1"
                        ></textarea>
                        <button class="ai-chat-send" id="ai-chat-send">
                            <i class="fa fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add styles
        const styles = document.createElement('style');
        styles.textContent = `
            .global-ai-assistant {
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 420px;
                height: 550px;
                background: var(--surface, #ffffff);
                border: 1px solid var(--border, #e0e0e0);
                border-radius: 16px;
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .ai-chat-container {
                display: flex;
                flex-direction: column;
                height: 100%;
            }

            .ai-chat-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 18px 24px;
                background: var(--primary, #007bff);
                color: white;
                border-bottom: 1px solid var(--border, #e0e0e0);
            }

            .ai-chat-title {
                display: flex;
                align-items: center;
                gap: 12px;
                font-weight: 600;
                font-size: 16px;
            }

            .ai-avatar {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                object-fit: cover;
                filter: var(--avatar-filter, none);
            }

            .ai-chat-controls {
                display: flex;
                gap: 8px;
            }

            .ai-chat-controls button {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 4px 8px;
                border-radius: 4px;
                transition: background-color 0.2s;
            }

            .ai-chat-controls button:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .ai-chat-messages {
                flex: 1;
                overflow-y: auto;
                padding: 20px;
                display: flex;
                flex-direction: column;
                gap: 16px;
                scrollbar-width: thin;
                scrollbar-color: var(--border, #e0e0e0) transparent;
            }

            .ai-chat-messages::-webkit-scrollbar {
                width: 6px;
            }

            .ai-chat-messages::-webkit-scrollbar-track {
                background: transparent;
            }

            .ai-chat-messages::-webkit-scrollbar-thumb {
                background: var(--border, #e0e0e0);
                border-radius: 3px;
            }

            .ai-chat-messages::-webkit-scrollbar-thumb:hover {
                background: var(--text-secondary, #666);
            }

            .ai-message {
                display: flex;
                margin-bottom: 8px;
            }

            .ai-message.ai-user-message {
                justify-content: flex-end;
            }

            .ai-message.ai-assistant-message {
                justify-content: flex-start;
            }

            .ai-message.ai-system-message {
                justify-content: flex-start;
            }

            .ai-message-content {
                max-width: 85%;
                padding: 14px 18px;
                border-radius: 20px;
                position: relative;
                display: flex;
                align-items: flex-start;
                gap: 10px;
            }

            .ai-message-avatar {
                width: 28px;
                height: 28px;
                border-radius: 50%;
                object-fit: cover;
                flex-shrink: 0;
                filter: var(--avatar-filter, none);
            }

            .ai-user-message .ai-message-content {
                background: var(--primary, #007bff);
                color: white;
                border-bottom-right-radius: 4px;
            }

            .ai-assistant-message .ai-message-content {
                background: var(--background, #f8f9fa);
                color: var(--text, #333);
                border: 1px solid var(--border, #e0e0e0);
                border-bottom-left-radius: 4px;
            }

            .ai-system-message .ai-message-content {
                background: var(--warning, #fff3cd);
                color: var(--text, #333);
                border: 1px solid var(--warning, #ffeaa7);
                border-bottom-left-radius: 4px;
            }

            .ai-message-text {
                flex: 1;
                line-height: 1.4;
                word-wrap: break-word;
                white-space: pre-line;
            }

            .ai-chat-input-container {
                padding: 20px 24px;
                border-top: 1px solid var(--border, #e0e0e0);
                background: var(--surface, #ffffff);
            }

            .ai-chat-input-wrapper {
                display: flex;
                align-items: center;
                gap: 10px;
                background: var(--background, #f8f9fa);
                border: 1px solid var(--border, #e0e0e0);
                border-radius: 28px;
                padding: 10px 18px;
            }

            .ai-chat-input {
                flex: 1;
                border: none;
                background: transparent;
                outline: none;
                resize: none;
                font-family: inherit;
                font-size: 14px;
                line-height: 1.4;
                max-height: 100px;
                min-height: 20px;
                color: var(--text, #333);
                padding: 0;
                margin: 0;
                scrollbar-width: thin;
                scrollbar-color: var(--border, #e0e0e0) transparent;
            }

            .ai-chat-input::-webkit-scrollbar {
                width: 4px;
            }

            .ai-chat-input::-webkit-scrollbar-track {
                background: transparent;
            }

            .ai-chat-input::-webkit-scrollbar-thumb {
                background: var(--border, #e0e0e0);
                border-radius: 2px;
            }

            .ai-chat-input::-webkit-scrollbar-thumb:hover {
                background: var(--text-secondary, #666);
            }

            .ai-chat-input::placeholder {
                color: var(--text-secondary, #666);
            }

            .ai-chat-send {
                background: var(--primary, #007bff);
                color: white;
                border: none;
                border-radius: 50%;
                width: 36px;
                height: 36px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                flex-shrink: 0;
                font-size: 14px;
            }

            .ai-chat-send:hover {
                background: var(--primary-hover, #0056b3);
                transform: scale(1.05);
            }

            .ai-chat-send:active {
                transform: scale(0.95);
            }

            .ai-chat-send:disabled {
                background: var(--text-secondary, #666);
                cursor: not-allowed;
                transform: none;
            }

            /* Dark theme adjustments */
            @media (prefers-color-scheme: dark) {
                .global-ai-assistant {
                    background: var(--surface, #2d3748);
                    border-color: var(--border, #4a5568);
                }

                .ai-chat-messages {
                    background: var(--surface, #2d3748);
                }

                .ai-assistant-message .ai-message-content {
                    background: var(--background, #1a202c);
                    color: var(--text, #e2e8f0);
                    border-color: var(--border, #4a5568);
                }

                .ai-chat-input-container {
                    background: var(--surface, #2d3748);
                }

                .ai-chat-input-wrapper {
                    background: var(--background, #1a202c);
                    border-color: var(--border, #4a5568);
                }

                .ai-chat-input {
                    color: var(--text, #e2e8f0);
                }

                .ai-chat-input::placeholder {
                    color: var(--text-secondary, #a0aec0);
                }
            }

            /* Responsive design */
            @media (max-width: 480px) {
                .global-ai-assistant {
                    width: calc(100vw - 40px);
                    height: calc(100vh - 40px);
                    bottom: 20px;
                    right: 20px;
                    border-radius: 12px;
                }
                
                .ai-chat-header {
                    padding: 16px 20px;
                }
                
                .ai-chat-messages {
                    padding: 16px;
                    gap: 12px;
                }
                
                .ai-chat-input-container {
                    padding: 16px 20px;
                }
                
                .ai-chat-input-wrapper {
                    padding: 8px 14px;
                }
            }

            /* Animation for showing/hiding */
            .global-ai-assistant {
                transform: translateY(20px);
                opacity: 0;
                transition: transform 0.3s ease, opacity 0.3s ease;
            }

            .global-ai-assistant.show {
                transform: translateY(0);
                opacity: 1;
            }
        `;

        document.head.appendChild(styles);
        document.body.appendChild(aiContainer);
        
        // Add initial welcome message
        addMessage("Hello! I'm your AI assistant. How can I help you today?", 'assistant');
    }

    // Setup event listeners
    function setupEventListeners() {
        // F2 key to toggle AI assistant
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F2') {
                e.preventDefault();
                toggleAiAssistant();
            }
        });

        // Close button
        document.addEventListener('click', function(e) {
            if (e.target.closest('.ai-chat-close')) {
                hideAiAssistant();
            }
        });

        // Minimize button
        document.addEventListener('click', function(e) {
            if (e.target.closest('.ai-chat-minimize')) {
                hideAiAssistant();
            }
        });

        // Send button
        document.addEventListener('click', function(e) {
            if (e.target.closest('.ai-chat-send')) {
                sendMessage();
            }
        });

        // Enter key to send message
        document.addEventListener('keydown', function(e) {
            if (e.target.id === 'ai-chat-input') {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            }
        });

        // Auto-resize textarea
        document.addEventListener('input', function(e) {
            if (e.target.id === 'ai-chat-input') {
                autoResizeTextarea(e.target);
            }
        });
    }

    // Toggle AI assistant visibility
    function toggleAiAssistant() {
        const aiAssistant = document.getElementById('global-ai-assistant');
        if (!aiAssistant) return;

        if (isVisible) {
            hideAiAssistant();
        } else {
            showAiAssistant();
        }
    }

    // Show AI assistant
    function showAiAssistant() {
        const aiAssistant = document.getElementById('global-ai-assistant');
        if (!aiAssistant) return;

        aiAssistant.style.display = 'flex';
        setTimeout(() => {
            aiAssistant.classList.add('show');
        }, 10);
        
        isVisible = true;
        
        // Focus on input
        const input = document.getElementById('ai-chat-input');
        if (input) {
            input.focus();
        }
    }

    // Hide AI assistant
    function hideAiAssistant() {
        const aiAssistant = document.getElementById('global-ai-assistant');
        if (!aiAssistant) return;

        aiAssistant.classList.remove('show');
        setTimeout(() => {
            aiAssistant.style.display = 'none';
        }, 300);
        
        isVisible = false;
    }

    // Send message
    function sendMessage() {
        const input = document.getElementById('ai-chat-input');
        const message = input.value.trim();
        
        if (!message) return;

        // Add user message to chat
        addMessage(message, 'user');
        
        // Clear input
        input.value = '';
        autoResizeTextarea(input);
        
        // Disable send button while processing
        const sendButton = document.getElementById('ai-chat-send');
        sendButton.disabled = true;
        
        // Simulate AI response (placeholder for now)
        setTimeout(() => {
            const response = "I'm here to help! This is a placeholder response. The AI functionality will be implemented later.";
            addMessage(response, 'assistant');
            sendButton.disabled = false;
        }, 1000);
    }

    // Add message to chat
    function addMessage(text, sender) {
        const messagesContainer = document.getElementById('ai-chat-messages');
        if (!messagesContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ai-${sender}-message`;
        
        let avatarHtml = '';
        if (sender !== 'user') {
            avatarHtml = '<img src="imgs/icons/ai-avatar.png" alt="AI" class="ai-message-avatar">';
        }
        
        messageDiv.innerHTML = `
            <div class="ai-message-content">
                ${avatarHtml}
                <div class="ai-message-text">${escapeHtml(text).replace(/\n/g, '<br>')}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Store in chat history
        chatHistory.push({ sender, text, timestamp: Date.now() });
    }

    // Auto-resize textarea
    function autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
    }

    // Escape HTML to prevent XSS
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public API
    window.GlobalAiAssistant = {
        show: showAiAssistant,
        hide: hideAiAssistant,
        toggle: toggleAiAssistant,
        addMessage: addMessage
    };

})();
