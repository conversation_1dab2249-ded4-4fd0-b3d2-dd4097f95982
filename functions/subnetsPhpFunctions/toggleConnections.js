/*
 * Toggle Connections Visibility on subnets.php page
 * Adds a new button to the header that shows / hides subnet-to-subnet connection arrows.
 * The visibility state is stored on window.showConnections (default: false).
 */

(function () {
    // Ensure we only run once DOM is ready
    document.addEventListener('DOMContentLoaded', function () {
        // Use existing toggle button if present; otherwise create in status area
        let toggleBtn = document.getElementById('toggle-connections-btn');
        if (!toggleBtn) {
            // Find the status count wrapper for fallback
            const statusCountWrapper = document.querySelector('#status-count-wrapper');

            if (!statusCountWrapper) return; // Safety check

            // Create the toggle button with status filter styling
            toggleBtn = document.createElement('span');
            toggleBtn.className = 'connections-toggle';
            toggleBtn.id = 'toggle-connections-btn';
            toggleBtn.title = 'Toggle Subnet Connections';
            toggleBtn.innerHTML = '<i class="fa fa-link"></i> Connections';

            // Insert the button after the service count container (after status filters)
            const serviceCountContainer = document.querySelector('#service-count-container');
            if (serviceCountContainer) {
                serviceCountContainer.parentNode.insertBefore(toggleBtn, serviceCountContainer.nextSibling);
            } else {
                // Fallback: append to the end if service count container not found
                statusCountWrapper.appendChild(toggleBtn);
            }
        }

        // Create mobile version of the toggle button
        const mobileToggleBtn = document.createElement('span');
        mobileToggleBtn.className = 'connections-toggle';
        mobileToggleBtn.id = 'toggle-connections-btn-mobile';
        mobileToggleBtn.title = 'Toggle Subnet Connections';
        mobileToggleBtn.innerHTML = '<i class="fa fa-link"></i> Connections';

        // Insert mobile version after the APM progress mobile element
        const apmProgressMobile = document.querySelector('#apm-progress-mobile');
        if (apmProgressMobile) {
            apmProgressMobile.parentNode.insertBefore(mobileToggleBtn, apmProgressMobile.nextSibling);
        }

        // State variable (will be loaded from server)
        if (typeof window.showConnections === 'undefined') {
            window.showConnections = true; // Default fallback
        }

        // Function to load connection toggle state from server
        async function loadConnectionToggle() {
            try {
                const response = await fetch('configHandler.php?param=connectionToggle');
                const data = await response.json();
                if (data.success) {
                    window.showConnections = data.connectionToggle === 'on';
                    return data.connectionToggle;
                }
            } catch (error) {
                console.error('Error loading connection toggle state:', error);
            }
            return 'on'; // Default fallback
        }

        // Function to save connection toggle state to server
        async function saveConnectionToggle(toggle) {
            try {
                const response = await fetch('configHandler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ connectionToggle: toggle })
                });
                const data = await response.json();
                return data.success;
            } catch (error) {
                console.error('Error saving connection toggle state:', error);
                return false;
            }
        }

        // Load initial state from server
        loadConnectionToggle().then(() => {
            updateConnectionsVisibility();
        });

        // Helper to update arrow-group visibility & button appearance
        function updateConnectionsVisibility() {
            const arrowGroup = document.querySelector('g.arrow-group');
            const speedLabelGroup = document.querySelector('g.speed-label-group');
            
            if (arrowGroup) {
                arrowGroup.style.display = window.showConnections ? '' : 'none';
            }
            
            if (speedLabelGroup) {
                speedLabelGroup.style.display = window.showConnections ? '' : 'none';
            }
            
            // Update both desktop and mobile button appearances
            const buttons = [toggleBtn, mobileToggleBtn];
            buttons.forEach(btn => {
                if (btn) {
                    btn.classList.toggle('active', window.showConnections);
                    btn.innerHTML = '<i class="fa fa-link"></i> Connections';
                }
            });
        }

        // Toggle logic for desktop
        toggleBtn.addEventListener('click', function (e) {
            e.preventDefault();
            window.showConnections = !window.showConnections;
            updateConnectionsVisibility();
            // Save the new state to server
            saveConnectionToggle(window.showConnections ? 'on' : 'off');
        });

        // Toggle logic for mobile
        if (mobileToggleBtn) {
            mobileToggleBtn.addEventListener('click', function (e) {
                e.preventDefault();
                window.showConnections = !window.showConnections;
                updateConnectionsVisibility();
                // Save the new state to server
                saveConnectionToggle(window.showConnections ? 'on' : 'off');
            });
        }

        // Initial attempt (arrowGroup may not exist yet)
        updateConnectionsVisibility();

        // Observe the canvas container for arrowGroup creation / re-render
        const canvasContainer = document.getElementById('canvas-container');
        if (canvasContainer) {
            const observer = new MutationObserver(updateConnectionsVisibility);
            observer.observe(canvasContainer, { childList: true, subtree: true });
        }
    });
})();
