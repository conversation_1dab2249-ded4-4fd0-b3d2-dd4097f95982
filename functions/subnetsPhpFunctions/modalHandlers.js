/**
 * Modal handling functionality for subnets.php
 * Handles iframe modals, scan modal, and credentials modal
 */

/**
 * Show modal function for credentials and feature status indicators
 */
function showModal(url, modalType = null, serviceName = null, tab = null) {
    const modal = document.getElementById('infoModal');
    const iframe = document.getElementById('modal-frame');
    const modalBody = document.getElementById('iframeModal-content');
    const originalStyles = modalBody.style.cssText;

    if (url.includes('credentials.php')){
        modalBody.style.maxHeight = '800px';
        modalBody.style.maxWidth = '1000px';
        // Remove 'active' class from header-buttons when opening credentials modal
        ['.header-buttons', '.hamburger'].forEach(sel => {
            const el = document.querySelector(sel);
            if (el) el.classList.remove('active');
        });
    } else {
        // Explicitly reset all styles for non-credentials modals
        modalBody.style.maxHeight = '';
        modalBody.style.maxWidth = '';
        modalBody.style.top = '';
        modalBody.style.width = '';
        modalBody.style.height = '';
    }

    modal.classList.remove('loaded');
    iframe.style.display = 'none';
    iframe.src = url;
    modal.style.display = 'block';
    modal.classList.add('show');
    modal.classList.remove('small');

    // Set data attributes for opening specific modals within the iframe
    if (modalType === 'host') {
        iframe.setAttribute('data-open-host-modal', 'true');
        if (tab) {
            iframe.setAttribute('data-open-tab', tab);
        }
    } else if (modalType === 'service' && serviceName) {
        iframe.setAttribute('data-open-service-modal', 'true');
        iframe.setAttribute('data-service-name', serviceName);
        if (tab) {
            iframe.setAttribute('data-open-tab', tab);
        }
    }

    iframe.onload = function() {
        const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
        const navElement = iframeDocument.querySelector("nav");
        if (navElement) {
            navElement.style.display = "none";
        }
        
        // Check if we need to open a host or service modal
        if (iframe.hasAttribute('data-open-host-modal')) {
            // Try to trigger a click on the host card to open host modal
            setTimeout(() => {
                try {
                    const hostCard = iframeDocument.getElementById('host-card');
                    if (hostCard) {
                        hostCard.click();
                        
                        // If we need to open a specific tab
                        const tab = iframe.getAttribute('data-open-tab');
                        if (tab === 'performance') {
                            // Wait for the modal to open and then click on the performance tab
                            setTimeout(() => {
                                try {
                                    // Find and click the performance tab button
                                    const performanceTab = iframeDocument.querySelector('button.tab-button[data-tab="performance"]');
                                    if (performanceTab) {
                                        performanceTab.click();
                                    } else {
                                        console.error('Performance tab button not found');
                                    }
                                } catch (e) {
                                    console.error('Failed to open performance tab:', e);
                                }
                            }, 700);
                        }
                    }
                } catch (e) {
                    console.error('Failed to auto-open host modal:', e);
                }
                iframe.removeAttribute('data-open-host-modal');
                iframe.removeAttribute('data-open-tab');
            }, 500);
        } else if (iframe.hasAttribute('data-open-service-modal')) {
            // Try to trigger a click on the specific service card
            setTimeout(() => {
                try {
                    const serviceName = iframe.getAttribute('data-service-name');
                    const encodedServiceName = encodeURIComponent(serviceName);
                    const serviceCard = iframeDocument.querySelector(`.service-card[data-service="${encodedServiceName}"]`);
                    if (serviceCard) {
                        serviceCard.click();
                        
                        // If we need to open a specific tab
                        const tab = iframe.getAttribute('data-open-tab');
                        if (tab === 'performance') {
                            // Wait for the modal to open and then click on the performance tab
                            setTimeout(() => {
                                try {
                                    // Find and click the performance tab button
                                    const performanceTab = iframeDocument.querySelector('button.tab-button[data-tab="performance"]');
                                    if (performanceTab) {
                                        performanceTab.click();
                                    } else {
                                        console.error('Performance tab button not found');
                                    }
                                } catch (e) {
                                    console.error('Failed to open performance tab:', e);
                                }
                            }, 700);
                        }
                    }
                } catch (e) {
                    console.error('Failed to auto-open service modal:', e);
                }
                iframe.removeAttribute('data-open-service-modal');
                iframe.removeAttribute('data-service-name');
                iframe.removeAttribute('data-open-tab');
            }, 500);
        }
        
        // Apply translations if translator is available
        if (typeof translator !== 'undefined') {
            translator.translateIframe(iframe);
        }
        modal.classList.add('loaded');
        iframe.style.display = 'block';
    };
}

/**
 * Initialize scan modal functionality - Now handled by modular scanModal.js
 */

/**
 * Initialize iframe modal close functionality
 */
function initIframeModal() {
    document.querySelector('.iframeMclose').addEventListener('click', function() {
        const modal = document.getElementById('infoModal');
        if (!modal) return;
        modal.classList.remove('show');
        modal.classList.add('closing');
        setTimeout(() => {
            modal.classList.remove('closing');
            modal.style.display = 'none';
        }, 300);
    });

    window.addEventListener('click', function(event) {
        const modal = document.getElementById('infoModal');
        if (event.target == modal) {
            modal.classList.remove('show');
            modal.classList.add('closing');
            setTimeout(() => {
                modal.classList.remove('closing');
                modal.style.display = 'none';
            }, 300);
        }
    });
}

/**
 * Global function to view subnet hosts (called from the detail card)
 */
function viewSubnet(subnet, hostname, infra) {
    window.location.href = `hosts.php?subnet=${encodeURIComponent(subnet)}&subnetNickname=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(infra)}&subnet=true`;
} 