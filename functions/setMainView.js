/**
 * Fixed setMainView function that prevents race conditions
 * This function should be included in all pages that use the View dropdown
 */

// Global function to set main view via AJAX with proper navigation handling
function setMainView(viewType, targetUrl = null) {
    // If no target URL provided, determine it based on view type
    if (!targetUrl) {
        switch (viewType) {
            case 'bubble':
                targetUrl = 'https://' + window.location.hostname + '/bubblemaps/infra.php';
                break;
            case 'list':
                targetUrl = 'hostlist.php';
                break;
            case 'table':
                targetUrl = 'tableview.php';
                break;
            case 'dashboards':
                targetUrl = 'networkmap.php';
                break;
            default:
                console.error('Unknown view type:', viewType);
                return Promise.reject(new Error('Unknown view type: ' + viewType));
        }
    }

    const formData = new FormData();
    formData.append('view', viewType);
    
    return fetch('set_main_view_ajax.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            console.log('Main view set to:', viewType);
            // Navigate to the target URL after successful AJAX call
            window.location.href = targetUrl;
        } else {
            console.error('Failed to set main view:', data.error);
            // Still navigate even if setting main view fails
            // This prevents users from getting stuck
            window.location.href = targetUrl;
        }
    })
    .catch(error => {
        console.error('Error setting main view:', error);
        // Navigate even if there's an error to prevent getting stuck
        window.location.href = targetUrl;
    });
}

// Function to handle view dropdown clicks with proper event handling
function handleViewDropdownClick(event, viewType, targetUrl = null) {
    event.preventDefault();
    event.stopPropagation();
    
    // Show loading indicator if available
    const button = event.target.closest('.header-button');
    if (button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Loading...';
        button.style.pointerEvents = 'none';
        
        // Restore button after a timeout (fallback)
        setTimeout(() => {
            button.innerHTML = originalText;
            button.style.pointerEvents = '';
        }, 3000);
    }
    
    setMainView(viewType, targetUrl);
}
