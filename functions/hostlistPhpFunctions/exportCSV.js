/*
 * Export Hostlist table to CSV
 * Exports ALL filtered data (across all pages) to CSV file.
 * Note: Attempt column is excluded from the export.
 */
function exportHostListToCSV() {
    // Use the full filtered data instead of just the visible table
    const allData = window.filteredHostListData;
    
    if (!allData || allData.length === 0) {
        alert('No data available to export.');
        return;
    }

    // Check the same conditions that determine when host rows should be hidden in the UI
    const activeServiceFilters = document.querySelectorAll('.hostlist-status-filter[data-type="service"].active');
    const areServiceFiltersActive = activeServiceFilters.length > 0;
    
    const servicegroupSelect = document.getElementById('servicegroup-filter');
    const selectedServicegroup = servicegroupSelect ? servicegroupSelect.value : 'all';
    const areServicegroupFiltersActive = selectedServicegroup !== 'all';
    
    const searchModeSelect = document.getElementById('search-mode');
    const searchMode = searchModeSelect ? searchModeSelect.value : 'host';
    const isServiceSearchMode = searchMode === 'service';
    
    const searchInput = document.getElementById('searchBar-list');
    const hasSearchTerm = searchInput && searchInput.value.trim() !== '';
    
    // Determine if we should hide host rows (same logic as in renderHostList)
    const shouldHideHostRows = areServiceFiltersActive || (isServiceSearchMode && hasSearchTerm) || areServicegroupFiltersActive;

    const csvRows = [];

    // Add header row
    const headers = ['Host', 'Service', 'Status', 'Last Check', 'Duration', 'Status Information'];
    csvRows.push(headers.join(','));

    // Process all data (not just visible table rows)
    allData.forEach(host => {
        // Only add host row (Ping service) if host rows should not be hidden
        if (!shouldHideHostRows) {
            const hostRow = [
                sanitizeCSVCell(host.display_name),
                'Ping (ICMP)',
                sanitizeCSVCell(host.status_text),
                sanitizeCSVCell(formatDate(host.last_check)),
                sanitizeCSVCell(host.duration),
                sanitizeCSVCell(host.plugin_output)
            ];
            csvRows.push(hostRow.join(','));
        }

        // Add service rows
        host.services.forEach(service => {
            const serviceRow = [
                sanitizeCSVCell(host.display_name),
                sanitizeCSVCell(service.name),
                sanitizeCSVCell(service.status_text),
                sanitizeCSVCell(formatDate(service.last_check)),
                sanitizeCSVCell(service.duration),
                sanitizeCSVCell(service.plugin_output)
            ];
            csvRows.push(serviceRow.join(','));
        });
    });

    if (csvRows.length <= 1) { // Only header present
        alert('No data to export.');
        return;
    }

    const csvContent = '\uFEFF' + csvRows.join('\n'); // Prepend BOM for Excel compatibility
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `hostlist_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// Utility – escape/quote cell values if they contain special characters
function sanitizeCSVCell(text) {
    if (text === null || text === undefined) return '';
    let value = text.replace(/\n|\r/g, ' ').trim();
    value = value.replace(/"/g, '""'); // Escape quotes
    if (/[",]/.test(value)) {
        value = `"${value}"`;
    }
    return value;
}

// Attach click handler once DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const btn = document.getElementById('export-csv-btn');
    if (btn) {
        btn.addEventListener('click', exportHostListToCSV);
    }
}); 