<?php
header('Content-Type: application/json');

$positionsFile = __DIR__ . '/../../conf/hostgroup_positions.json';

// Function to create/fix JSON structure
function ensureCorrectJsonStructure($positionsFile) {
    $needsUpdate = false;

    // If file doesn't exist, create it
    if (!file_exists($positionsFile)) {
        @mkdir(dirname($positionsFile), 0775, true);
        $data = ['infras' => new stdClass()];
        file_put_contents($positionsFile, json_encode($data, JSON_PRETTY_PRINT));
        return $data;
    }

    // Read and parse existing file
    $raw = file_get_contents($positionsFile);
    $data = json_decode($raw, true);

    // Fix invalid JSON or missing structure
    if (!is_array($data)) {
        $data = ['infras' => []];
        $needsUpdate = true;
    }

    // Ensure 'infras' key exists and is an array
    if (!isset($data['infras']) || !is_array($data['infras'])) {
        $data['infras'] = [];
        $needsUpdate = true;
    }

    // Check each infrastructure for correct structure
    foreach ($data['infras'] as $infraName => $infraData) {
        if (!is_array($infraData)) {
            // Convert old format to new format
            $data['infras'][$infraName] = [
                'lockActive' => false,
                'positions' => new stdClass(),
                'subnets' => new stdClass()
            ];
            $needsUpdate = true;
        } else {
            // Ensure required keys exist
            if (!isset($infraData['lockActive'])) {
                $data['infras'][$infraName]['lockActive'] = false;
                $needsUpdate = true;
            }
            if (!isset($infraData['positions'])) {
                $data['infras'][$infraName]['positions'] = new stdClass();
                $needsUpdate = true;
            }
            if (!isset($infraData['subnets'])) {
                $data['infras'][$infraName]['subnets'] = new stdClass();
                $needsUpdate = true;
            }
        }
    }

    // Write back if structure was fixed
    if ($needsUpdate) {
        file_put_contents($positionsFile, json_encode($data, JSON_PRETTY_PRINT));
    }

    return $data;
}

$method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Read raw JSON
$raw = file_get_contents('php://input');
$payload = json_decode($raw, true);
if (!is_array($payload)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON']);
    exit;
}

$infra = $payload['infra'] ?? '';
$subnet = $payload['subnet'] ?? '';
$positions = $payload['positions'] ?? null;
$lockActive = isset($payload['lockActive']) ? (bool)$payload['lockActive'] : false;

if ($infra === '' || !is_array($positions)) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing infra or positions']);
    exit;
}

// Ensure correct JSON structure
$data = ensureCorrectJsonStructure($positionsFile);

// Handle subnet-specific positions
if (!empty($subnet)) {
    // Ensure infra structure exists with correct format
    if (!isset($data['infras'][$infra])) {
        $data['infras'][$infra] = [
            'lockActive' => false,
            'positions' => new stdClass(),
            'subnets' => new stdClass()
        ];
    }

    // Ensure subnets structure exists
    if (!isset($data['infras'][$infra]['subnets'])) {
        $data['infras'][$infra]['subnets'] = new stdClass();
    }

    // Save subnet-specific positions
    $data['infras'][$infra]['subnets'][$subnet] = [
        'lockActive' => $lockActive,
        'positions' => $positions,
    ];
} else {
    // Regular infra-level positions
    // Preserve existing subnet data if it exists
    $existingSubnets = isset($data['infras'][$infra]['subnets']) ? $data['infras'][$infra]['subnets'] : new stdClass();

    // Ensure infra structure exists with correct format
    if (!isset($data['infras'][$infra])) {
        $data['infras'][$infra] = [
            'lockActive' => $lockActive,
            'positions' => $positions,
            'subnets' => $existingSubnets
        ];
    } else {
        // Update existing structure
        $data['infras'][$infra]['lockActive'] = $lockActive;
        $data['infras'][$infra]['positions'] = $positions;
        $data['infras'][$infra]['subnets'] = $existingSubnets;
    }
}

// Write back
if (file_put_contents($positionsFile, json_encode($data, JSON_PRETTY_PRINT)) === false) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to write positions file']);
    exit;
}

echo json_encode(['success' => true]);

