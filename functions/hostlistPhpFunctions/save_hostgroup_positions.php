<?php
header('Content-Type: application/json');

$positionsFile = __DIR__ . '/../../conf/hostgroup_positions.json';

$method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Read raw JSON
$raw = file_get_contents('php://input');
$payload = json_decode($raw, true);
if (!is_array($payload)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON']);
    exit;
}

$infra = $payload['infra'] ?? '';
$subnet = $payload['subnet'] ?? '';
$positions = $payload['positions'] ?? null;
$lockActive = isset($payload['lockActive']) ? (bool)$payload['lockActive'] : false;

if ($infra === '' || !is_array($positions)) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing infra or positions']);
    exit;
}

// Ensure file exists with base structure
if (!file_exists($positionsFile)) {
    @mkdir(dirname($positionsFile), 0775, true);
    file_put_contents($positionsFile, json_encode(['infras' => new stdClass()], JSON_PRETTY_PRINT));
}

// Load existing
$currentRaw = file_get_contents($positionsFile);
$data = json_decode($currentRaw, true);
if (!is_array($data)) {
    $data = ['infras' => []];
}
if (!isset($data['infras']) || !is_array($data['infras'])) {
    $data['infras'] = [];
}

// Handle subnet-specific positions
if (!empty($subnet)) {
    // Ensure infra structure exists
    if (!isset($data['infras'][$infra])) {
        $data['infras'][$infra] = [
            'lockActive' => false,
            'positions' => [],
            'subnets' => []
        ];
    }

    // Ensure subnets structure exists
    if (!isset($data['infras'][$infra]['subnets'])) {
        $data['infras'][$infra]['subnets'] = [];
    }

    // Save subnet-specific positions
    $data['infras'][$infra]['subnets'][$subnet] = [
        'lockActive' => $lockActive,
        'positions' => $positions,
    ];
} else {
    // Regular infra-level positions
    // Preserve existing subnet data if it exists
    $existingSubnets = isset($data['infras'][$infra]['subnets']) ? $data['infras'][$infra]['subnets'] : [];

    $data['infras'][$infra] = [
        'lockActive' => $lockActive,
        'positions' => $positions,
    ];

    // Restore subnet data
    if (!empty($existingSubnets)) {
        $data['infras'][$infra]['subnets'] = $existingSubnets;
    }
}

// Write back
if (file_put_contents($positionsFile, json_encode($data, JSON_PRETTY_PRINT)) === false) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to write positions file']);
    exit;
}

echo json_encode(['success' => true]);

