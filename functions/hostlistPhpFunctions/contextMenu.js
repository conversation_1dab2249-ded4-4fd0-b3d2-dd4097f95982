/**
 * Context Menu functionality for Hostlist view
 * Handles right-click context menus for hosts and services
 */

/**
 * Initialize context menu functionality for host and service rows
 */
function initContextMenu() {
    // Add right-click event listeners to host and service rows
    document.addEventListener('contextmenu', async function(e) {
        const hostRow = e.target.closest('.hostlist-host-row');
        const serviceRow = e.target.closest('.hostlist-service-row');
        
        if (hostRow || serviceRow) {
            e.preventDefault();
            
            if (hostRow) {
                // Handle host right-click
                const hostname = hostRow.getAttribute('data-hostname');
                const ip = hostRow.getAttribute('data-ip');
                
                if (hostname && ip) {
                    // Set current host info for context menu
                    window.currentHostIP = ip;
                    window.currentHostname = hostname;
                    const content = await generateContextMenu(null, hostname, true);
                    showHostListContextMenu(content, e);
                }
            } else if (serviceRow) {
                // Handle service right-click
                const hostname = serviceRow.getAttribute('data-hostname');
                const ip = serviceRow.getAttribute('data-ip');
                const serviceName = serviceRow.getAttribute('data-service-name') || serviceRow.querySelector('.service-name')?.textContent.trim();
                
                if (hostname && ip && serviceName) {
                    // Set current host info for context menu
                    window.currentHostIP = ip;
                    window.currentHostname = hostname;
                    window.currentServiceName = serviceName;
                    
                    const encodedServiceName = serviceName.replace(/\s+/g, '+');
                    const content = await generateContextMenu(encodedServiceName, hostname, false);
                    showHostListContextMenu(content, e);
                }
            }
        }
    });
    
    // Close context menu when clicking elsewhere
    document.addEventListener('click', function() {
        const contextMenu = document.getElementById('custom-context-menu');
        if (contextMenu) {
            contextMenu.style.display = 'none';
        }
    });
}

/**
 * Show context menu for hostlist items
 * @param {string} content - HTML content for the menu
 * @param {Event} e - Mouse event
 */
function showHostListContextMenu(content, e) {
    const contextMenu = document.getElementById('custom-context-menu');
    if (!contextMenu) return;
    
    contextMenu.innerHTML = content;
    contextMenu.style.display = 'block';
    contextMenu.style.left = e.pageX + 'px';
    contextMenu.style.top = e.pageY + 'px';
    
    // Adjust position if menu goes off screen
    const rect = contextMenu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
        contextMenu.style.left = (e.pageX - rect.width) + 'px';
    }
    if (rect.bottom > window.innerHeight) {
        contextMenu.style.top = (e.pageY - rect.height) + 'px';
    }
}

/**
 * Load context menu scripts dynamically to avoid initialization errors
 */
function loadContextMenuScripts() {
    // Load openModals.js first
    const openModalsScript = document.createElement('script');
    openModalsScript.src = 'functions/hostApmFunctions/openModals.js';
    openModalsScript.onload = function() {
        // Then load contextMenu.js
        const contextMenuScript = document.createElement('script');
        contextMenuScript.src = 'functions/hostApmFunctions/contextMenu.js';
        contextMenuScript.onload = function() {
            // Override functions for hostlist context
            setTimeout(() => {
                const originalCallDeleteService = window.callDeleteService;
                window.callDeleteService = function(ipAddress, serviceName) {
                    // Use our stored hostname instead of the URL parameter
                    const correctHostname = window.currentHostname || window.currentHostIP || ipAddress;
                    return originalCallDeleteService(correctHostname, serviceName);
                };
                
                // Override renameHostApm for hostlist context
                window.renameHostApm = function(hostname) {
                    renameHostInHostlist(hostname);
                };
                
                // Override openParentOptionsApm for hostlist context
                window.openParentOptionsApm = function(hostname) {
                    openParentOptionsHostlist(hostname);
                };
                
                // Override showHostGroupModalApm for hostlist context
                window.showHostGroupModalApm = function(hostname) {
                    showHostGroupModalHostlist(hostname);
                };
            }, 100);
        };
        document.head.appendChild(contextMenuScript);
    };
    document.head.appendChild(openModalsScript);
}

/**
 * Function to handle service deletion from context menu - REAL implementation
 */
function callDeleteService(ipAddress, serviceName) {
    // Use the current hostname/IP we stored when right-clicking
    const correctIP = window.currentHostIP || ipAddress;
    const actualServiceName = serviceName || window.currentServiceName;
    
    // Check if serviceName is an array for multiple services
    const isMultiple = Array.isArray(actualServiceName);
    const serviceCount = isMultiple ? actualServiceName.length : 1;
    const serviceText = isMultiple ? 
        (serviceCount > 1 ? `${serviceCount} selected services` : actualServiceName[0]) : 
        actualServiceName;
        
    if (!confirm(`Delete ${serviceText}?`)) {
        return;
    }
    
    const phpScriptUrl = 'delete_service.php';

    // Create a floating message element with consistent styling
    const floatingMessage = document.createElement('div');
    floatingMessage.id = 'service-delete-loading';
    floatingMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 8px;
        z-index: 10000;
        text-align: center;
    `;
    floatingMessage.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i>
        </div>
        <div>Deleting ${serviceText}...</div>
    `;

    document.body.appendChild(floatingMessage);

    const formData = new FormData();
    // Use IP address as required by delete_service.php
    formData.append('ip', correctIP);
    
    if (isMultiple) {
        // Add each service as array elements as expected by delete_service.php
        actualServiceName.forEach((service, index) => {
            formData.append(`servicetodelete[${index}]`, service);
        });
    } else {
        formData.append('servicetodelete', actualServiceName);
    }

    fetch(phpScriptUrl, {
        method: 'POST',
        body: formData,
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(data => {
            // Remove loading message
            const loadingElement = document.getElementById('service-delete-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            // Check if response starts with "Success:" as per delete_service.php
            if (typeof data === 'string' && data.trim().startsWith("Success:")) {
                // Show success message briefly then reload
                const successDiv = document.createElement('div');
                successDiv.style.cssText = floatingMessage.style.cssText;
                successDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-check-circle" style="font-size: 24px; color: #4caf50;"></i>
                    </div>
                    <div>${serviceText} deleted successfully!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Refreshing...</div>
                `;
                document.body.appendChild(successDiv);
                
                setTimeout(() => {
                    if (typeof populateHostListPage === 'function') {
                        Promise.all([
                            populateHostListPage(),
                            updateFeatureStatusIndicators(), // Consolidated update
                            successDiv.style.display = 'none'
                        ]);
                    }
                }, 3000);
            } else {
                // Show error message with server response
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = floatingMessage.style.cssText;
                errorDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #ff9800;"></i>
                    </div>
                    <div>Service deletion failed!</div>
                    <div style="font-size: 12px; margin-top: 5px;">${data.trim()}</div>
                `;
                document.body.appendChild(errorDiv);
                
                setTimeout(() => {
                    errorDiv.remove();
                }, 3000);
            }
        })
        .catch(error => {
            // Remove loading message
            const loadingElement = document.getElementById('service-delete-loading');
            if (loadingElement) {
                loadingElement.remove();
            }

            console.error('There was an error calling the PHP script:', error);
            
            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = floatingMessage.style.cssText;
            errorDiv.innerHTML = `
                <div style="margin-bottom: 10px;">
                    <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #f44336;"></i>
                </div>
                <div>An error occurred during service deletion</div>
                <div style="font-size: 12px; margin-top: 5px;">Please try again</div>
            `;
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 3000);
        });
}

/**
 * Function to handle host blacklisting from context menu - REAL implementation
 */
function blacklistHostApm(hostname) {
    // Close any open context menus
    const contextMenu = document.getElementById('custom-context-menu');
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
    
    // Close any open modals
    const modal = document.getElementById('service-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = "auto";
    }

    // Show a confirmation dialog to the user
    const isConfirmed = confirm("Are you sure you want to stop monitoring this host?");

    if (!isConfirmed) {
        return; // Exit the function if the user cancels
    }

    // Use the current hostname/IP we stored when right-clicking
    const ip = window.currentHostIP || hostname;
    const infra = new URLSearchParams(window.location.search).get('infra');

    if (!ip) {
        alert("Error: Could not determine host IP address.");
        return;
    }

    // Show loading message
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'blacklist-loading';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 8px;
        z-index: 10000;
        text-align: center;
    `;
    loadingDiv.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i>
        </div>
        <div>Blacklisting host...</div>
    `;
    document.body.appendChild(loadingDiv);

    // Send the request to blacklist the host
    const xhr = new XMLHttpRequest();
    const blacklistUrl = `blacklistHost.php?ip=${encodeURIComponent(ip)}${infra ? '&infra=' + encodeURIComponent(infra) : ''}`;
    
    xhr.open('GET', blacklistUrl, true);
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            // Remove loading message
            const loadingElement = document.getElementById('blacklist-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            if (xhr.status === 200) {
                // Show success message briefly then redirect
                const successDiv = document.createElement('div');
                successDiv.style.cssText = loadingDiv.style.cssText;
                successDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-check-circle" style="font-size: 24px; color: #4caf50;"></i>
                    </div>
                    <div>Host blacklisted successfully!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Refreshing...</div>
                `;
                document.body.appendChild(successDiv);
                
                // Refresh the page after 3 seconds
                setTimeout(() => {
                    if (typeof populateHostListPage === 'function') {
                        Promise.all([
                            populateHostListPage(),
                            updateFeatureStatusIndicators(), // Consolidated update
                            successDiv.style.display = 'none'
                        ]);
                    }
                }, 3000);
            } else {
                // Show error message
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = loadingDiv.style.cssText;
                errorDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #f44336;"></i>
                    </div>
                    <div>Error blacklisting host. Please try again.</div>
                `;
                document.body.appendChild(errorDiv);
                
                setTimeout(() => {
                    errorDiv.remove();
                }, 3000);
            }
        }
    };
    xhr.send();
}

/**
 * Function to handle host renaming from context menu in hostlist context
 */
function renameHostInHostlist(hostname) {
    // Close any open context menus
    const contextMenu = document.getElementById('custom-context-menu');
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
    
    // Use the current hostname/IP we stored when right-clicking
    const currentHostname = window.currentHostname || hostname;
    const currentHostIP = window.currentHostIP;
    
    // Show rename dialog
    const newName = prompt('Enter new hostname:', currentHostname);
    if (!newName || newName.trim() === '') {
        return; // Exit if no name entered or cancelled
    }
    
    const trimmedNewName = newName.trim();
    
    // If the new name is the same as the current name, no need to update
    if (trimmedNewName === currentHostname) {
        return;
    }
    
    // Show loading indicator
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'rename-loading';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 8px;
        z-index: 10000;
        text-align: center;
    `;
    loadingDiv.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i>
        </div>
        <div>Updating hostname...</div>
    `;
    document.body.appendChild(loadingDiv);
    
    // Get the host ID from the current page data
    // We need to find the host ID from the bubblemaps database
    fetch(`get_host_id.php?ip=${encodeURIComponent(currentHostIP)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.hostId) {
                // Make the API call to update the hostname
                return fetch('update_host_name.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${encodeURIComponent(data.hostId)}&newName=${encodeURIComponent(trimmedNewName)}`
                });
            } else {
                throw new Error('Could not find host ID');
            }
        })
        .then(response => response.json())
        .then(data => {
            // Remove loading message
            const loadingElement = document.getElementById('rename-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            if (data.success) {
                // Show success message briefly then refresh the page
                const successDiv = document.createElement('div');
                successDiv.style.cssText = loadingDiv.style.cssText;
                successDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-check-circle" style="font-size: 24px; color: #4caf50;"></i>
                    </div>
                    <div>Hostname updated successfully!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Refreshing...</div>
                `;
                document.body.appendChild(successDiv);
                
                // Refresh the page after 5 seconds
                setTimeout(() => {
                    if (typeof populateHostListPage === 'function') {
                        Promise.all([
                            populateHostListPage(),
                            updateFeatureStatusIndicators(), // Consolidated update
                            successDiv.style.display = 'none'
                        ]);
                    }
                }, 5000);
            } else {
                // Show error message
                const errorMsg = document.createElement('div');
                errorMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:#F44336;color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
                errorMsg.textContent = `Error: ${data.message}`;
                document.body.appendChild(errorMsg);
                setTimeout(() => errorMsg.remove(), 5000);
            }
        })
        .catch(error => {
            // Remove loading message
            const loadingElement = document.getElementById('rename-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            console.error('Error renaming host:', error);
            
            // Show error message
            const errorMsg = document.createElement('div');
            errorMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:#F44336;color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
            errorMsg.textContent = 'Error updating hostname. Please try again.';
            document.body.appendChild(errorMsg);
            setTimeout(() => errorMsg.remove(), 5000);
        });
}

/**
 * Function to open parent options for a host in hostlist context
 */
function openParentOptionsHostlist(hostname) {
    // Close any open context menus
    const contextMenu = document.getElementById('custom-context-menu');
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
    
    // Use the current hostname we stored when right-clicking
    const currentHostname = window.currentHostname || hostname;
    
    // Get the infra parameter from URL
    const urlParams = new URLSearchParams(window.location.search);
    const infra = encodeURIComponent(urlParams.get('infra') || '');
    
    // Open the parent options modal
    const url = `checkCommandHost.php?hostname=${encodeURIComponent(currentHostname)}&infra=${infra}&section=parents`;
    
    // Use the same showModal function as hosts.php with proper sizing
    if (typeof showModal === 'function') {
        showModal(url);
    } else {
        // Fallback to window.open if modal function not available
        window.open(url, '_blank', 'width=800,height=600');
    }
}

/**
 * Function to fetch all hostgroups for relocate functionality in hostlist context
 */
async function fetchAllHostGroupsHostlist() {
    const url = 'get_hostgroups_from_db.php';
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'text/plain',
            },
        });
        
        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
        
        const text = await response.text();
        return text.trim().split('\n').filter(line => line.length > 0);
    } catch (error) {
        console.error('Error fetching all host groups:', error);
        return [];
    }
}

/**
 * Function to show hostgroup selection modal for relocate functionality in hostlist context
 */
async function showHostGroupModalHostlist(hostname) {
    // Close any open context menus
    const contextMenu = document.getElementById('custom-context-menu');
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
    
    try {
        const hostGroups = await fetchAllHostGroupsHostlist();
        
        // Use the current hostname/IP we stored when right-clicking
        const currentHostname = window.currentHostname || hostname;
        const hostIp = window.currentHostIP || '';
        
        if (!hostIp) {
            alert('Error: Could not determine host IP address.');
            return;
        }
        
        // Create modal HTML using the new hostlist-specific CSS classes
        const contextInfo = `Moving host: ${currentHostname}${hostIp !== currentHostname ? ` (${hostIp})` : ''}`;
        const modalHTML = `
            <div id="hostgroup-modal-hostlist" class="hostlist-relocate-modal show">
                <div class="hostlist-relocate-modal-content">
                    <span class="hostlist-relocate-modal-close" onclick="closeHostGroupModalHostlist()">&times;</span>
                    <h3>Select Host Group</h3>
                    <div class="context-info">${contextInfo}</div>
                    <div style="display: flex; gap: 8px; margin-bottom: 15px;">
                        <input type="text" id="hostgroup-search-hostlist" placeholder="Search host groups..." style="flex: 1;">
                        <button id="add-hostgroup-btn-hostlist" title="Add new hostgroup">+</button>
                    </div>
                    <div id="hostgroup-list-hostlist" style="max-height: 300px; overflow-y: auto;"></div>
                </div>
            </div>
        `;
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Prevent body scrolling when modal is open
        document.body.style.overflow = 'hidden';
        
        // Setup event listeners
        const modal = document.getElementById('hostgroup-modal-hostlist');
        const searchInput = document.getElementById('hostgroup-search-hostlist');
        const addBtn = document.getElementById('add-hostgroup-btn-hostlist');
        const listContainer = document.getElementById('hostgroup-list-hostlist');
        
        // Render host groups
        const renderGroups = (filter = '') => {
            const filteredGroups = hostGroups.filter(group => {
                const [, alias] = group.split('. ', 2);
                return alias.toLowerCase().includes(filter.toLowerCase());
            });
            
            listContainer.innerHTML = filteredGroups.length ? 
                filteredGroups.map(group => {
                    const [id, alias] = group.split('. ', 2);
                    return `<div class="hostgroup-item" data-id="${id}">${alias}</div>`;
                }).join('') :
                '<div style="padding: 8px 12px; color: #999;">No host groups found</div>';
        };
        
        // Search functionality
        searchInput.addEventListener('input', (e) => renderGroups(e.target.value));
        
        // Add hostgroup functionality
        addBtn.addEventListener('click', async () => {
            const name = prompt('Enter new hostgroup name:');
            if (name) {
                try {
                    const response = await fetch('add_hostgroup.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `hostgroup_name=${encodeURIComponent(name)}&alias=${encodeURIComponent(name)}&config_id=1`
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        // Refresh the host groups list
                        const newHostGroups = await fetchAllHostGroupsHostlist();
                        hostGroups.length = 0; // Clear the array
                        hostGroups.push(...newHostGroups); // Add new data
                        renderGroups(searchInput.value); // Re-render with current filter
                        
                        // Show success message
                        const successMsg = document.createElement('div');
                        successMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:#4CAF50;color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
                        successMsg.textContent = `Hostgroup "${name}" added successfully!`;
                        document.body.appendChild(successMsg);
                        setTimeout(() => successMsg.remove(), 3000);
                    } else {
                        alert(`Error: ${data.message}`);
                    }
                } catch (error) {
                    console.error('Error adding hostgroup:', error);
                    alert('Error adding hostgroup');
                }
            }
        });
        
        // Host group selection
        listContainer.addEventListener('click', async (e) => {
            if (e.target.classList.contains('hostgroup-item')) {
                const hostGroupId = e.target.dataset.id;
                const hostGroupName = e.target.textContent;
                
                const confirmed = confirm(`Move host "${currentHostname}" to hostgroup "${hostGroupName}"?`);
                if (!confirmed) return;
                
                // Close modal first to restore scrolling
                closeHostGroupModalHostlist();
                await moveHostToGroupHostlist(hostIp, hostGroupId, hostGroupName);
            }
        });
        
        // Close on outside click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeHostGroupModalHostlist();
        });
        
        // Initial render
        renderGroups();
        searchInput.focus();
        
    } catch (error) {
        console.error('Error showing host group modal:', error);
        alert('Error loading host groups');
    }
}

/**
 * Function to close hostgroup modal in hostlist context
 */
function closeHostGroupModalHostlist() {
    const modal = document.getElementById('hostgroup-modal-hostlist');
    if (modal) {
        modal.remove();
        // Restore body scrolling
        document.body.style.overflow = 'auto';
    }
}

/**
 * Function to move host to group in hostlist context
 */
async function moveHostToGroupHostlist(hostIp, hostGroupId, hostGroupName) {
    const loadingDiv = document.createElement('div');
    loadingDiv.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.8);color:white;padding:20px;border-radius:8px;z-index:10000;text-align:center;';
    loadingDiv.innerHTML = `Moving host... <div style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:30px;height:30px;animation:spin 1s linear infinite;margin:10px auto;"></div>`;
    document.body.appendChild(loadingDiv);
    
    // Add spin animation if not already present
    if (!document.getElementById('spinner-styles-hostlist')) {
        const style = document.createElement('style');
        style.id = 'spinner-styles-hostlist';
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }
    
    try {
        const response = await fetch('move_host_to_group.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `ips=${encodeURIComponent(JSON.stringify([hostIp]))}&hostGroupId=${encodeURIComponent(hostGroupId)}`
        });
        
        const result = await response.json();
        if (result.success) {
            // Show success message
            const successMsg = document.createElement('div');
            successMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:#4CAF50;color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
            successMsg.textContent = `Host moved to "${hostGroupName}" successfully!`;
            document.body.appendChild(successMsg);
            setTimeout(() => successMsg.remove(), 5000);
            
            // Refresh the hostlist page
            if (typeof populateHostListPage === 'function') {
                Promise.all([
                    populateHostListPage(),
                    updateFeatureStatusIndicators() // Consolidated update
                ]);
            }
            
            if (result.details?.errorCount > 0) {
                alert(`${result.message}\n\nErrors:\n${result.details.errors.join('\n')}`);
            }
        } else {
            alert(`Error: ${result.message}`);
        }
    } catch (error) {
        console.error('Error moving host:', error);
        alert('An error occurred while moving the host');
    } finally {
        document.body.removeChild(loadingDiv);
    }
} 