<?php
header('Content-Type: application/json');

// File path for saved layouts
$positionsFile = __DIR__ . '/../../conf/hostgroup_positions.json';

// Function to create/fix JSON structure
function ensureCorrectJsonStructure($positionsFile) {
    $needsUpdate = false;

    // If file doesn't exist, create it
    if (!file_exists($positionsFile)) {
        @mkdir(dirname($positionsFile), 0775, true);
        $data = ['infras' => new stdClass()];
        file_put_contents($positionsFile, json_encode($data, JSON_PRETTY_PRINT));
        return $data;
    }

    // Read and parse existing file
    $raw = file_get_contents($positionsFile);
    $data = json_decode($raw, true);

    // Fix invalid JSON or missing structure
    if (!is_array($data)) {
        $data = ['infras' => []];
        $needsUpdate = true;
    }

    // Ensure 'infras' key exists and is an array
    if (!isset($data['infras']) || !is_array($data['infras'])) {
        $data['infras'] = [];
        $needsUpdate = true;
    }

    // Check each infrastructure for correct structure
    foreach ($data['infras'] as $infraName => $infraData) {
        if (!is_array($infraData)) {
            // Convert old format to new format
            $data['infras'][$infraName] = [
                'lockActive' => false,
                'positions' => new stdClass(),
                'subnets' => new stdClass()
            ];
            $needsUpdate = true;
        } else {
            // Ensure required keys exist
            if (!isset($infraData['lockActive'])) {
                $data['infras'][$infraName]['lockActive'] = false;
                $needsUpdate = true;
            }
            if (!isset($infraData['positions'])) {
                $data['infras'][$infraName]['positions'] = new stdClass();
                $needsUpdate = true;
            }
            if (!isset($infraData['subnets'])) {
                $data['infras'][$infraName]['subnets'] = new stdClass();
                $needsUpdate = true;
            }
        }
    }

    // Write back if structure was fixed
    if ($needsUpdate) {
        file_put_contents($positionsFile, json_encode($data, JSON_PRETTY_PRINT));
    }

    return $data;
}

$infra = isset($_GET['infra']) ? trim($_GET['infra']) : '';
$subnet = isset($_GET['subnet']) ? trim($_GET['subnet']) : '';

if ($infra === '') {
    http_response_code(400);
    echo json_encode(['error' => 'Missing infra parameter']);
    exit;
}

// Ensure correct JSON structure
$data = ensureCorrectJsonStructure($positionsFile);

$layouts = isset($data['infras']) && is_array($data['infras']) ? $data['infras'] : [];

// If subnet is specified, look for subnet-specific layout
if (!empty($subnet)) {
    $infraData = isset($layouts[$infra]) ? $layouts[$infra] : [];
    $subnetLayouts = isset($infraData['subnets']) && is_array($infraData['subnets']) ? $infraData['subnets'] : [];
    $layout = isset($subnetLayouts[$subnet]) ? $subnetLayouts[$subnet] : null;

    if ($layout === null) {
        // Return default empty layout for this subnet
        $layout = [
            'lockActive' => false,
            'zoom' => null,
            'positions' => new stdClass()
        ];
    }
} else {
    // Regular infra-level layout
    $layout = isset($layouts[$infra]) ? $layouts[$infra] : null;

    if ($layout === null) {
        // Return default empty layout for this infra
        $layout = [
            'lockActive' => false,
            'zoom' => null,
            'positions' => new stdClass()
        ];
    }
}

echo json_encode($layout);

