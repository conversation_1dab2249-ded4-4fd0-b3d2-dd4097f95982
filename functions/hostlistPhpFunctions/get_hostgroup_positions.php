<?php
header('Content-Type: application/json');

// File path for saved layouts
$positionsFile = __DIR__ . '/../../conf/hostgroup_positions.json';

// Ensure file exists; if not, create with empty structure
if (!file_exists($positionsFile)) {
    $init = [ 'infras' => new stdClass() ];
    // Attempt to create directory if missing (should exist already)
    @mkdir(dirname($positionsFile), 0775, true);
    file_put_contents($positionsFile, json_encode($init, JSON_PRETTY_PRINT));
}

$infra = isset($_GET['infra']) ? trim($_GET['infra']) : '';
$subnet = isset($_GET['subnet']) ? trim($_GET['subnet']) : '';

if ($infra === '') {
    http_response_code(400);
    echo json_encode(['error' => 'Missing infra parameter']);
    exit;
}

// Read file
$raw = file_get_contents($positionsFile);
$data = json_decode($raw, true);
if (!is_array($data)) {
    $data = [ 'infras' => [] ];
}

$layouts = isset($data['infras']) && is_array($data['infras']) ? $data['infras'] : [];

// If subnet is specified, look for subnet-specific layout
if (!empty($subnet)) {
    $infraData = isset($layouts[$infra]) ? $layouts[$infra] : [];
    $subnetLayouts = isset($infraData['subnets']) && is_array($infraData['subnets']) ? $infraData['subnets'] : [];
    $layout = isset($subnetLayouts[$subnet]) ? $subnetLayouts[$subnet] : null;

    if ($layout === null) {
        // Return default empty layout for this subnet
        $layout = [
            'lockActive' => false,
            'zoom' => null,
            'positions' => new stdClass()
        ];
    }
} else {
    // Regular infra-level layout
    $layout = isset($layouts[$infra]) ? $layouts[$infra] : null;

    if ($layout === null) {
        // Return default empty layout for this infra
        $layout = [
            'lockActive' => false,
            'zoom' => null,
            'positions' => new stdClass()
        ];
    }
}

echo json_encode($layout);

