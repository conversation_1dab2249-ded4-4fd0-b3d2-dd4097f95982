/**
 * Table View initialization and rendering
 * Groups hosts by hostgroup and renders each group in its own table/section.
 * Reuses existing helpers and UI patterns from the List View.
 */

// Bubble View nicknames cache
let bubbleHostnamesMap = null;

// Global cache to hold the grouped data for search/refresh
let tableViewData = {
  // hostgroupName: Array<Host>
};

// Table ordering management
let tableOrderConfig = null;

document.addEventListener('DOMContentLoaded', () => {
  // Initialize shared pieces used in list view
  initFeatureStatusIndicators();
  setupPopoverHandlers();
  setupTableviewHeaderInteractions();
  setupTableviewSearch();
  setupTableviewFilters();
  setupMobileHeaderLayout();
  initContextMenu();
  setupModalEventHandlers();
  loadContextMenuScripts();
  initAutoRefresh();

  // Load table order configuration
  loadTableOrderConfig();

  // Initial population
  refreshTableView();
});

/**
 * Loads the table order configuration from the JSON file
 */
async function loadTableOrderConfig() {
  try {
    const response = await fetch('functions/tableviewPhpFunctions/tableview_order.json');
    if (response.ok) {
      tableOrderConfig = await response.json();
    } else {
      // File doesn't exist, create default configuration
      tableOrderConfig = { hostgroupOrder: [] };
      await saveTableOrderConfig();
    }
  } catch (error) {
    console.log('Table order config not found, will create default:', error);
    tableOrderConfig = { hostgroupOrder: [] };
    await saveTableOrderConfig();
  }
}

/**
 * Saves the table order configuration to the JSON file
 */
async function saveTableOrderConfig() {
  try {
    const response = await fetch('functions/tableviewPhpFunctions/save_tableview_order.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tableOrderConfig, null, 2)
    });
    
    if (!response.ok) {
      console.warn('Failed to save table order config:', response.status);
    } else {
      const result = await response.json();
      if (result.error) {
        console.warn('Error saving table order config:', result.error);
      }
    }
  } catch (error) {
    console.error('Error saving table order config:', error);
  }
}

/**
 * Updates the table order configuration with new hostgroups
 * @param {Array<string>} currentHostgroups - Current hostgroup names
 */
async function updateTableOrderConfig(currentHostgroups) {
  if (!tableOrderConfig) {
    await loadTableOrderConfig();
  }

  const existingOrder = tableOrderConfig.hostgroupOrder || [];
  const newHostgroups = currentHostgroups.filter(hg => !existingOrder.includes(hg));
  
  if (newHostgroups.length > 0) {
    // Add new hostgroups at the beginning, but sort them alphabetically
    const sortedNewHostgroups = newHostgroups.sort((a, b) => a.localeCompare(b));
    tableOrderConfig.hostgroupOrder = [...sortedNewHostgroups, ...existingOrder];
    await saveTableOrderConfig();
  }
}

async function refreshTableView() {
  const container = document.getElementById('tableview-content');
  if (container) {
    container.innerHTML = '<div class="hostlist-loading"><div class="spinner"></div><div>Loading grouped hosts and services...</div></div>';
  }

  try {
    // Ensure Bubble View nicknames are available for rendering
    await fetchBubbleHostnames();
    // Determine active filters from header (same behavior as list view)
    const activeHostFilters = document.querySelectorAll('.hostlist-status-filter[data-type="host"].active');
    const activeServiceFilters = document.querySelectorAll('.hostlist-status-filter[data-type="service"].active');

    const hostFilters = Array.from(activeHostFilters).map(btn => btn.getAttribute('data-status'));
    const hostFilterValue = hostFilters.length > 0 ? hostFilters : 'all';

    const serviceFilters = Array.from(activeServiceFilters).map(btn => btn.getAttribute('data-status'));
    const serviceFilterValue = serviceFilters.length > 0 ? serviceFilters : 'all';

    const hostgroupSelect = document.getElementById('hostgroup-filter');
    const servicegroupSelect = document.getElementById('servicegroup-filter');
    const selectedHostgroup = hostgroupSelect ? hostgroupSelect.value : 'all';
    const selectedServicegroup = servicegroupSelect ? servicegroupSelect.value : 'all';

    // Build combined host/service data using existing helper (already filtered by host/service/servicegroup)
    const combined = await buildHostServiceData(hostFilterValue, serviceFilterValue, selectedHostgroup, selectedServicegroup);

    // Get host->hostgroups mapping from objectjson hostlist (most accurate)
    const hostObjectAll = await fetchHostObjectData(null);

    function extractGroups(hostObj) {
      if (!hostObj || typeof hostObj !== 'object') return [];
      const hg = hostObj.hostgroups;
      if (!hg) return [];
      // Accept a variety of shapes: array of strings, array of objects, object map, comma-separated string
      if (Array.isArray(hg)) {
        return hg.map(x => {
          if (typeof x === 'string') return x;
          if (x && typeof x === 'object') return x.hostgroup_name || x.name || x.alias || Object.keys(x)[0];
          return null;
        }).filter(Boolean);
      } else if (typeof hg === 'string') {
        return hg.split(',').map(s => s.trim()).filter(Boolean);
      } else if (typeof hg === 'object') {
        // Often an object keyed by group name
        return Object.keys(hg);
      }
      return [];
    }

    // Fallback membership via hostgrouplist if hostgroups are missing
    const hostgroupMembership = await fetchHostgroupMembership();
    const allGroupNames = Object.keys(hostgroupMembership);

    const grouped = {};
    const ungrouped = [];
    combined.forEach(host => {
      const objHost = hostObjectAll?.data?.hostlist ? hostObjectAll.data.hostlist[host.name] : null;
      let groups = extractGroups(objHost);
      if ((!groups || groups.length === 0) && allGroupNames.length > 0) {
        // Try membership map
        groups = allGroupNames.filter(g => hostgroupMembership[g] && hostgroupMembership[g].has(host.name));
      }
      if (groups && groups.length) {
        groups.forEach(g => {
          if (!grouped[g]) grouped[g] = [];
          grouped[g].push(host);
        });
      } else {
        ungrouped.push(host);
      }
    });
    if (ungrouped.length > 0) grouped['Ungrouped'] = ungrouped;

    tableViewData = grouped;

    // Update status counts in filter buttons
    const statusCountsData = await fetchStatusCounts(selectedHostgroup, selectedServicegroup);
    updateStatusFilterCounts(statusCountsData);

    // Decide which groups to render
    let groupsToRender;
    if (selectedHostgroup && selectedHostgroup !== 'all') {
      groupsToRender = [selectedHostgroup];
    } else {
      // Use table order configuration if available, otherwise fallback to default ordering
      const nonEmpty = Object.keys(grouped).filter(g => Array.isArray(grouped[g]) && grouped[g].length > 0);
      
      // Update the order configuration with any new hostgroups
      await updateTableOrderConfig(nonEmpty);
      
      if (tableOrderConfig && tableOrderConfig.hostgroupOrder && tableOrderConfig.hostgroupOrder.length > 0) {
        // Use configured order, filtering to only include groups that have hosts
        const configuredOrder = tableOrderConfig.hostgroupOrder.filter(g => nonEmpty.includes(g));
        const unconfiguredGroups = nonEmpty.filter(g => !configuredOrder.includes(g));
        
        // Put Ungrouped at the end if it exists
        const ungroupedIdx = unconfiguredGroups.indexOf('Ungrouped');
        if (ungroupedIdx >= 0) {
          unconfiguredGroups.splice(ungroupedIdx, 1);
        }
        
        // Sort unconfigured groups alphabetically
        unconfiguredGroups.sort((a, b) => a.localeCompare(b));
        
        groupsToRender = [...configuredOrder, ...unconfiguredGroups];
        if (grouped['Ungrouped'] && grouped['Ungrouped'].length > 0) {
          groupsToRender.push('Ungrouped');
        }
      } else {
        // Create initial alphabetical order for new configuration
        const sortedGroups = nonEmpty.filter(g => g !== 'Ungrouped').sort((a, b) => a.localeCompare(b));
        if (grouped['Ungrouped'] && grouped['Ungrouped'].length > 0) {
          sortedGroups.push('Ungrouped');
        }
        groupsToRender = sortedGroups;
        
        // Save this as the initial configuration
        if (tableOrderConfig) {
          tableOrderConfig.hostgroupOrder = sortedGroups;
          await saveTableOrderConfig();
        }
      }
    }

    renderGroupedTables(groupsToRender, grouped);
  } catch (err) {
    console.error('Error refreshing table view:', err);
    if (container) {
      container.innerHTML = `<div class="hostlist-error">Error loading data: ${err.message}</div>`;
    }
  }
}

function renderGroupedTables(groupsToRender, grouped) {
  const container = document.getElementById('tableview-content');
  if (!container) return;

  if (!groupsToRender || groupsToRender.length === 0) {
    container.innerHTML = '<div class="tableview-empty">No hostgroups found</div>';
    return;
  }

  let html = '<div class="tableview-sections">';
  groupsToRender.forEach((groupName, index) => {
    const hosts = grouped[groupName] || [];

    // Calculate quick service badges summary for header
    let svcOk = 0, svcWarn = 0, svcCrit = 0, svcUnk = 0;
    hosts.forEach(h => h.services.forEach(s => {
      if (s.status_class === 'ok') svcOk++;
      else if (s.status_class === 'warning') svcWarn++;
      else if (s.status_class === 'critical') svcCrit++;
      else if (s.status_class === 'unknown') svcUnk++;
    }));

    html += `<section class="tableview-section" data-hostgroup="${escapeHtml(groupName)}" data-index="${index}">
      <h3>
        <span>${escapeHtml(groupName)}</span>
        <span class="svc-badges">
          <span class="svc-badge ok" title="OK">${svcOk}</span>
          <span class="svc-badge warning" title="Warning">${svcWarn}</span>
          <span class="svc-badge critical" title="Critical">${svcCrit}</span>
          <span class="svc-badge unknown" title="Unknown">${svcUnk}</span>
        </span>
      </h3>
      ${generateTableHTMLTableview(hosts, false, (document.getElementById('servicegroup-filter')?.value || 'all') !== 'all')}
    </section>`;
  });
  html += '</div>';
  container.innerHTML = html;
}

function setupTableviewHeaderInteractions() {
  // Reuse the same reset logic and multi-select behavior from list view
  const hostFilters = document.querySelectorAll('.hostlist-status-filter[data-type="host"]');
  const serviceFilters = document.querySelectorAll('.hostlist-status-filter[data-type="service"]');
  const resetAllFilters = document.getElementById('resetAllFilters');
  const hostgroupSelect = document.getElementById('hostgroup-filter');
  const servicegroupSelect = document.getElementById('servicegroup-filter');
  const adjustOrderBtn = document.getElementById('adjustOrderBtn');

  // Populate dropdowns
  (async () => {
    try {
      const hostgroups = await fetchHostGroups();
      if (hostgroupSelect && hostgroups.length > 0) {
        const allOption = hostgroupSelect.querySelector('option[value="all"]');
        hostgroupSelect.innerHTML = '';
        if (allOption) hostgroupSelect.appendChild(allOption);
        hostgroups.forEach(g => {
          const opt = document.createElement('option');
          opt.value = g; opt.textContent = g; hostgroupSelect.appendChild(opt);
        });
      }

      const servicegroups = await fetchServiceGroups();
      if (servicegroupSelect && servicegroups.length > 0) {
        const allOption = servicegroupSelect.querySelector('option[value="all"]');
        servicegroupSelect.innerHTML = '';
        if (allOption) servicegroupSelect.appendChild(allOption);
        servicegroups.forEach(g => {
          const opt = document.createElement('option');
          opt.value = g; opt.textContent = g; servicegroupSelect.appendChild(opt);
        });
      }
    } catch (e) {
      console.error('Failed to populate filters:', e);
    }
  })();

  resetAllFilters?.addEventListener('click', async () => {
    hostFilters.forEach(btn => btn.classList.remove('active'));
    serviceFilters.forEach(btn => btn.classList.remove('active'));
    if (hostgroupSelect) hostgroupSelect.value = 'all';
    if (servicegroupSelect) servicegroupSelect.value = 'all';
    await refreshTableView();
  });

  hostFilters.forEach(btn => {
    btn.addEventListener('click', async () => {
      serviceFilters.forEach(b => b.classList.remove('active'));
      btn.classList.toggle('active');
      await refreshTableView();
    });
  });
  serviceFilters.forEach(btn => {
    btn.addEventListener('click', async () => {
      hostFilters.forEach(b => b.classList.remove('active'));
      btn.classList.toggle('active');
      await refreshTableView();
    });
  });

  hostgroupSelect?.addEventListener('change', async () => {
    if (servicegroupSelect && hostgroupSelect.value !== 'all') {
      servicegroupSelect.value = 'all';
    }
    await refreshTableView();
  });
  servicegroupSelect?.addEventListener('change', async () => {
    if (hostgroupSelect && servicegroupSelect.value !== 'all') {
      hostgroupSelect.value = 'all';
    }
    await refreshTableView();
  });

  adjustOrderBtn?.addEventListener('click', () => {
    openHostgroupOrderModal();
  });
}

function setupTableviewSearch() {
  const searchInput = document.getElementById('searchBar-tableview');
  const clearBtn = document.getElementById('tableview-clear-search');
  const searchModeSelect = document.getElementById('search-mode-tableview');

  function applySearch() {
    const mode = searchModeSelect?.value || 'host';
    const rawValue = (searchInput?.value || '').toLowerCase();
    const isExact = ((rawValue.startsWith('"') && rawValue.endsWith('"')) || (rawValue.startsWith("'") && rawValue.endsWith("'"))) && rawValue.length > 2;
    const term = isExact ? rawValue.substring(1, rawValue.length - 1).toLowerCase() : rawValue.trim();

    // If empty, just re-render full groups currently in data
    if (!term) {
      const hostgroupSelect = document.getElementById('hostgroup-filter');
      const selectedHostgroup = hostgroupSelect ? hostgroupSelect.value : 'all';
      const groups = selectedHostgroup !== 'all' ? [selectedHostgroup] : Object.keys(tableViewData);
      renderGroupedTables(groups, tableViewData);
      return;
    }

    // Build a filtered copy of tableViewData
    const filtered = {};
    Object.entries(tableViewData).forEach(([group, hosts]) => {
      const outHosts = [];
      hosts.forEach(h => {
        const hostNameLower = h.display_name.toLowerCase();
        const hostIpLower = h.address.toLowerCase();
        if (mode === 'host') {
          let match = false;
          if (isExact) match = (hostNameLower === term) || (hostIpLower === term);
          else match = hostNameLower.includes(term) || hostIpLower.includes(term);
          if (match) outHosts.push({...h});
        } else {
          const matchedServices = h.services.filter(s => {
            const svcLower = s.name.toLowerCase();
            return isExact ? svcLower === term : svcLower.includes(term);
          });
          if (matchedServices.length) outHosts.push({...h, services: matchedServices});
        }
      });
      if (outHosts.length) filtered[group] = outHosts;
    });

    const groups = Object.keys(filtered);
    if (groups.length === 0) {
      const container = document.getElementById('tableview-content');
      if (container) container.innerHTML = '<div class="tableview-empty">No matching results</div>';
    } else {
      renderGroupedTables(groups, filtered);
    }
  }

  searchInput?.addEventListener('input', applySearch);
  searchModeSelect?.addEventListener('change', () => {
    if (searchModeSelect.value === 'host') searchInput.placeholder = 'Search hosts...';
    else searchInput.placeholder = 'Search services...';
    applySearch();
  });
  clearBtn?.addEventListener('click', () => { if (searchInput) searchInput.value = ''; applySearch(); });
}

function setupTableviewFilters() {
  // Placeholder to keep parity with list view organization if needed later
}

function escapeHtml(s) {
  return String(s)
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

// Minimal fetch for Bubble View nicknames (IP => nickname)
async function fetchBubbleHostnames() {
  if (bubbleHostnamesMap !== null) return bubbleHostnamesMap;
  try {
    const res = await fetch('get_bubble_hostnames.php');
    if (!res.ok) throw new Error(`HTTP ${res.status}`);
    bubbleHostnamesMap = await res.json();
  } catch (e) {
    console.error('Error fetching Bubble View hostnames:', e);
    bubbleHostnamesMap = {};
  }
  return bubbleHostnamesMap;
}

// Status icon population (host/service) for tableview
function populateHostListStatusIconsTableview(data) {
  const iconMappings = {
    host: [
      { condition: (i) => i.checks_enabled === false, iconClass: 'fa-times-circle', tooltip: 'Active checks are off' },
      { condition: (i) => i.flap_detection_enabled === false, iconClass: 'fa-flag', tooltip: 'Flap detection is off' },
      { condition: (i) => i.notifications_enabled === false, iconClass: 'fa-bell-slash', tooltip: 'Notifications are off' },
      { condition: (i) => i.accept_passive_checks === false, iconClass: 'fa-eye-slash', tooltip: 'Passive checks are off' },
      { condition: (i) => i.event_handler_enabled === false, iconClass: 'fa-toggle-off', tooltip: 'Event handler is off' },
      { condition: (i) => Number(i.scheduled_downtime_depth) !== 0, iconClass: 'fa-moon-o', tooltip: 'In scheduled downtime' },
      { condition: (i) => i.obsess === false, iconClass: 'fa-meh-o', tooltip: 'Obsessing is off' },
      { condition: (i) => i.problem_has_been_acknowledged === true, iconClass: 'fa-gavel', tooltip: 'Problem has been acknowledged' }
    ],
    service: [
      { condition: (i) => i.checks_enabled === false, iconClass: 'fa-times-circle', tooltip: 'Active checks are off' },
      { condition: (i) => i.flap_detection_enabled === false, iconClass: 'fa-flag', tooltip: 'Flap detection is off' },
      { condition: (i) => i.notifications_enabled === false, iconClass: 'fa-bell-slash', tooltip: 'Notifications are off' },
      { condition: (i) => i.accept_passive_checks === false, iconClass: 'fa-eye-slash', tooltip: 'Passive checks are off' },
      { condition: (i) => i.event_handler_enabled === false, iconClass: 'fa-toggle-off', tooltip: 'Event handler is off' },
      { condition: (i) => Number(i.scheduled_downtime_depth) !== 0, iconClass: 'fa-moon-o', tooltip: 'In scheduled downtime' },
      { condition: (i) => i.obsess === false, iconClass: 'fa-meh-o', tooltip: 'Obsessing is off' },
      { condition: (i) => i.problem_has_been_acknowledged === true, iconClass: 'fa-gavel', tooltip: 'Problem has been acknowledged' }
    ]
  };

  data.forEach(host => {
    const hostIconContainer = document.querySelector(`.host-status-icons[data-host-name="${host.name}"]`);
    if (hostIconContainer) {
      hostIconContainer.innerHTML = '';
      iconMappings.host.forEach(mapping => {
        if (mapping.condition(host)) {
          const icon = document.createElement('i');
          icon.className = `fa ${mapping.iconClass} hostlist-status-icon`;
          icon.title = mapping.tooltip;
          hostIconContainer.appendChild(icon);
        }
      });
    }

    host.services.forEach(service => {
      const serviceIconContainer = document.querySelector(`.service-status-icons[data-host-name="${host.name}"][data-service-name="${service.name}"]`);
      if (serviceIconContainer) {
        serviceIconContainer.innerHTML = '';
        iconMappings.service.forEach(mapping => {
          if (mapping.condition(service)) {
            const icon = document.createElement('i');
            icon.className = `fa ${mapping.iconClass} hostlist-status-icon`;
            icon.title = mapping.tooltip;
            serviceIconContainer.appendChild(icon);
          }
        });
      }
    });
  });
}

// Render HTML for one group's host table. Always expanded rows for tableview.
function generateTableHTMLTableview(hostArray) {
  let tableHTML = `
    <table class="hostlist-table">
      <colgroup>
        <col class="col-host" style="width: 40%;">
        <col class="col-status" style="width: 25%;">
        <col class="col-service" style="width: 35%;">
      </colgroup>
      <thead>
        <tr>
          <th class="col-host">Host</th>
          <th class="col-status">Status</th>
          <th class="col-service">Services</th>
        </tr>
      </thead>
      <tbody>`;

  hostArray.forEach(host => {
    const bubbleName = bubbleHostnamesMap && bubbleHostnamesMap[host.address];
    const showBubbleName = bubbleName && bubbleName.trim() && bubbleName.toLowerCase() !== host.display_name.toLowerCase();

    const counts = countServiceStatuses(host.services || []);
    const badges = [
      counts.ok ? `<span class=\"svc-badge ok\" title=\"OK\">${counts.ok} OK</span>` : '',
      counts.warning ? `<span class=\"svc-badge warning\" title=\"Warning\">${counts.warning} WARNING</span>` : '',
      counts.critical ? `<span class=\"svc-badge critical\" title=\"Critical\">${counts.critical} CRITICAL</span>` : '',
      counts.unknown ? `<span class=\"svc-badge unknown\" title=\"Unknown\">${counts.unknown} UNKNOWN</span>` : '',
      counts.pending ? `<span class=\"svc-badge pending\" title=\"Pending\">${counts.pending} PENDING</span>` : ''
    ].filter(Boolean).join(' ');

    // If no services, show host status instead
    let serviceDisplay;
    if (!badges || badges.trim() === '') {
      // No services - show host status
      if (host.status_class === 'ok') {
        serviceDisplay = '<span class=\"svc-badge ok\" title=\"OK\">OK</span>';
      } else if (host.status_class === 'down') {
        serviceDisplay = '<span class=\"svc-badge critical\" title=\"DOWN\">DOWN</span>';
      } else if (host.status_class === 'unknown') {
        serviceDisplay = '<span class=\"svc-badge unknown\" title=\"UNREACHABLE\">UNREACHABLE</span>';
      } else {
        serviceDisplay = '<span class=\"svc-badge pending\" title=\"PENDING\">PENDING</span>';
      }
    } else {
      serviceDisplay = badges;
    }

    tableHTML += `
      <tr class=\"hostlist-host-row\" data-host-id=\"${host.name}\" data-hostname=\"${host.display_name}\" data-ip=\"${host.address}\" data-subnet=\"${host.subnet || 'External'}\" data-status=\"${host.status_class}\">\n\
        <td class=\"col-host\" title=\"${host.address} - ${host.status_text}${showBubbleName ? ` - Bubble: ${bubbleName}` : ''}\" onclick=\"openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')\">\n\
          <div class=\"host-cell-content\">\n\
            ${host.action_url ? `<i class='fa fa-area-chart graph-icon' title='Performance data available' onclick=\"event.stopPropagation(); openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', true, 'performance')\"></i>` : ''}\n\
            <span class=\"hostname ${host.status_class === 'down' ? 'down' : ''}\">${host.display_name}</span>\n\
          </div>\n\
        </td>\n\
        <td class=\"col-status\" onclick=\"openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')\">\n\
          <span class=\"hostlist-status ${host.status_class}\">${host.status_text}</span>\n\
        </td>\n\
        <td class=\"col-service\" onclick=\"openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')\">\n\
          <div class=\"svc-badges\">${serviceDisplay}</div>\n\
        </td>\n\
      </tr>`;
  });

  tableHTML += `</tbody></table>`;
  return tableHTML;
}

function countServiceStatuses(services) {
  const counts = { ok: 0, warning: 0, critical: 0, unknown: 0, pending: 0 };
  services.forEach(s => {
    if (!s || !s.status_class) return;
    if (counts[s.status_class] !== undefined) counts[s.status_class] += 1;
  });
  return counts;
}

// Expose for autoRefresh to call consistently
window.populateHostListPage = refreshTableView;

// Build hostgroup membership: { groupName: Set(hostnames) }
async function fetchHostgroupMembership() {
  try {
    const resp = await fetch(`https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostgrouplist&details=true`);
    if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
    const data = await resp.json();
    const map = {};
    if (data?.data?.hostgrouplist) {
      Object.entries(data.data.hostgrouplist).forEach(([groupName, groupObj]) => {
        const members = new Set();
        const raw = groupObj.members;
        if (Array.isArray(raw)) {
          raw.forEach(m => {
            if (typeof m === 'string') members.add(m);
            else if (m && typeof m === 'object' && m.host_name) members.add(m.host_name);
          });
        } else if (raw && typeof raw === 'object') {
          Object.values(raw).forEach(m => {
            if (typeof m === 'string') members.add(m);
            else if (m && typeof m === 'object' && m.host_name) members.add(m.host_name);
          });
        }
        map[groupName] = members;
      });
    }
    return map;
  } catch (e) {
    console.error('Failed to fetch hostgroup membership:', e);
    return {};
  }
}

/**
 * Opens the hostgroup order adjustment modal
 */
function openHostgroupOrderModal() {
  if (!tableOrderConfig || !tableOrderConfig.hostgroupOrder) return;
  
  const currentOrder = [...tableOrderConfig.hostgroupOrder];
  
  // Create modal HTML using exact same structure as filters modal
  const modalHTML = `
    <div id="hostgroupOrderModal" class="sr-modal" style="display: flex;">
      <div class="sr-modal-content" style="min-width: 600px; max-width: 800px;">
        <span class="sr-close" onclick="closeHostgroupOrderModal()">&times;</span>
        <h2>Adjust Hostgroup Order</h2>
        
        <div class="sr-field">
          <label>Quick Order Options</label>
          <div class="time-shortcuts">
            <button type="button" class="time-shortcut-btn" onclick="applyQuickOrder('alphabetical')">
              <i class="fa fa-sort-alpha-asc"></i> Alphabetical
            </button>
            <button type="button" class="time-shortcut-btn" onclick="applyQuickOrder('reverse-alphabetical')">
              <i class="fa fa-sort-alpha-desc"></i> Reverse Alphabetical
            </button>
            <button type="button" class="time-shortcut-btn" onclick="applyQuickOrder('status-priority')">
              <i class="fa fa-exclamation-triangle"></i> Status Priority
            </button>
            <button type="button" class="time-shortcut-btn" onclick="applyQuickOrder('host-count')">
              <i class="fa fa-server"></i> Host Count
            </button>
          </div>
        </div>
        
        <div class="sr-field">
          <label>Reorder Hostgroups</label>
          <div class="drag-instructions">
            <i class="fa fa-arrows"></i> Drag and drop hostgroups to reorder them
          </div>
          <div id="draggableHostgroups" class="draggable-list">
            ${currentOrder.map((groupName, index) => `
              <div class="draggable-item" data-group="${escapeHtml(groupName)}" draggable="true">
                <div class="item-content">
                  <span class="group-name">${escapeHtml(groupName)}</span>
                  <span class="group-host-count">${tableViewData[groupName] ? tableViewData[groupName].length : 0} hosts</span>
                </div>
                <div class="drag-handle">
                  <i class="fa fa-grip-vertical"></i>
                </div>
              </div>
            `).join('')}
          </div>
        </div>
        
        <div class="sr-actions">
          <button type="button" id="save-order-cancel" class="generate-btn" onclick="closeHostgroupOrderModal()">Cancel</button>
          <button type="button" id="save-order-confirm" class="generate-btn" onclick="saveHostgroupOrder()">Save Order</button>
        </div>
      </div>
    </div>
  `;
  
  // Add modal to body
  document.body.insertAdjacentHTML('beforeend', modalHTML);
  
  // Setup drag and drop functionality
  setupModalDragAndDrop();
}

/**
 * Closes the hostgroup order modal
 */
function closeHostgroupOrderModal() {
  const modal = document.getElementById('hostgroupOrderModal');
  if (modal) {
    modal.remove();
  }
}

/**
 * Applies a quick order to the hostgroups
 * @param {string} orderType - The type of order to apply
 */
function applyQuickOrder(orderType) {
  if (!tableOrderConfig || !tableOrderConfig.hostgroupOrder) return;
  
  let newOrder = [...tableOrderConfig.hostgroupOrder];
  
  switch (orderType) {
    case 'alphabetical':
      newOrder = newOrder.filter(g => g !== 'Ungrouped').sort((a, b) => a.localeCompare(b));
      if (newOrder.includes('Ungrouped')) {
        newOrder.push('Ungrouped');
      }
      break;
      
    case 'reverse-alphabetical':
      newOrder = newOrder.filter(g => g !== 'Ungrouped').sort((a, b) => b.localeCompare(a));
      if (newOrder.includes('Ungrouped')) {
        newOrder.push('Ungrouped');
      }
      break;
      
    case 'status-priority':
      // Sort by critical hosts first, then warning, then ok
      newOrder.sort((a, b) => {
        const aHosts = tableViewData[a] || [];
        const bHosts = tableViewData[b] || [];
        
        const aCritical = aHosts.filter(h => h.status_class === 'down' || h.services.some(s => s.status_class === 'critical')).length;
        const bCritical = bHosts.filter(h => h.status_class === 'down' || h.services.some(s => s.status_class === 'critical')).length;
        
        if (aCritical !== bCritical) return bCritical - aCritical;
        
        const aWarning = aHosts.filter(h => h.services.some(s => s.status_class === 'warning')).length;
        const bWarning = bHosts.filter(h => h.services.some(s => s.status_class === 'warning')).length;
        
        if (aWarning !== bWarning) return bWarning - aWarning;
        
        return a.localeCompare(b);
      });
      break;
      
    case 'host-count':
      newOrder.sort((a, b) => {
        const aCount = (tableViewData[a] || []).length;
        const bCount = (tableViewData[b] || []).length;
        return bCount - aCount;
      });
      break;
  }
  
  // Update the draggable list
  updateDraggableList(newOrder);
}

/**
 * Updates the draggable list with new order
 * @param {Array<string>} newOrder - The new order of hostgroups
 */
function updateDraggableList(newOrder) {
  const container = document.getElementById('draggableHostgroups');
  if (!container) return;
  
  container.innerHTML = newOrder.map((groupName, index) => `
    <div class="draggable-item" data-group="${escapeHtml(groupName)}" draggable="true">
      <div class="item-content">
        <span class="group-name">${escapeHtml(groupName)}</span>
        <span class="group-host-count">${tableViewData[groupName] ? tableViewData[groupName].length : 0} hosts</span>
      </div>
      <div class="drag-handle">
        <i class="fa fa-grip-vertical"></i>
      </div>
    </div>
  `).join('');
  
  // Re-setup drag and drop after updating the list
  setupModalDragAndDrop();
}



/**
 * Gets the current order from the DOM
 * @returns {Array<string>} The current order of hostgroups
 */
function getCurrentOrderFromDOM() {
  const container = document.getElementById('draggableHostgroups');
  if (!container) return [];
  
  const items = container.querySelectorAll('.draggable-item');
  return Array.from(items).map(item => item.getAttribute('data-group'));
}

/**
 * Updates the button states for all items
 */
function updateItemButtonStates() {
  const container = document.getElementById('draggableHostgroups');
  const items = container.querySelectorAll('.draggable-item');
  
  items.forEach((item, index) => {
    const upBtn = item.querySelector('.move-up-btn');
    const downBtn = item.querySelector('.move-down-btn');
    
    if (upBtn) upBtn.disabled = index === 0;
    if (downBtn) downBtn.disabled = index === items.length - 1;
  });
}

/**
 * Sets up drag and drop functionality in the modal
 */
function setupModalDragAndDrop() {
  const container = document.getElementById('draggableHostgroups');
  if (!container) return;

  const items = container.querySelectorAll('.draggable-item');
  
  items.forEach(item => {
    item.addEventListener('dragstart', handleDragStart);
    item.addEventListener('dragover', handleDragOver);
    item.addEventListener('drop', handleDrop);
    item.addEventListener('dragend', handleDragEnd);
    item.addEventListener('dragenter', handleDragEnter);
    item.addEventListener('dragleave', handleDragLeave);
  });
}

let draggedItem = null;
let draggedIndex = -1;
let dragOverItem = null;

function handleDragStart(e) {
  draggedItem = e.target;
  draggedIndex = Array.from(e.target.parentNode.children).indexOf(e.target);
  e.target.classList.add('dragging');
  e.dataTransfer.effectAllowed = 'move';
}

function handleDragEnter(e) {
  e.preventDefault();
  if (this === draggedItem) return;
  dragOverItem = this;
}

function handleDragLeave(e) {
  e.preventDefault();
  if (this === draggedItem) return;
  
  // Only remove classes if we're not dragging over a child element
  if (!this.contains(e.relatedTarget)) {
    this.classList.remove('drag-above', 'drag-below');
  }
}

function handleDragOver(e) {
  e.preventDefault();
  e.dataTransfer.dropEffect = 'move';
  
  if (this === draggedItem) return;
  
  const rect = this.getBoundingClientRect();
  const midY = rect.top + rect.height / 2;
  
  // Remove all drag indicators first
  const allItems = document.querySelectorAll('.draggable-item');
  allItems.forEach(item => {
    item.classList.remove('drag-above', 'drag-below');
  });
  
  // Add appropriate indicator
  if (e.clientY < midY) {
    this.classList.add('drag-above');
  } else {
    this.classList.add('drag-below');
  }
}

function handleDrop(e) {
  e.preventDefault();
  
  if (this === draggedItem) return;
  
  const dropIndex = Array.from(this.parentNode.children).indexOf(this);
  const rect = this.getBoundingClientRect();
  const midY = rect.top + rect.height / 2;
  
  // Determine if we're dropping above or below the target
  const finalIndex = e.clientY < midY ? dropIndex : dropIndex + 1;
  
  // Get current order and reorder
  const currentOrder = getCurrentOrderFromDOM();
  const itemToMove = currentOrder[draggedIndex];
  
  if (itemToMove && draggedIndex !== finalIndex) {
    // Create new array with reordered items
    const newOrder = [...currentOrder];
    newOrder.splice(draggedIndex, 1);
    newOrder.splice(finalIndex > draggedIndex ? finalIndex - 1 : finalIndex, 0, itemToMove);
    
    // Update the list
    updateDraggableList(newOrder);
    
    // Highlight the moved item briefly
    setTimeout(() => {
      const movedItem = document.querySelector(`[data-group="${itemToMove}"]`);
      if (movedItem) {
        movedItem.classList.add('moved');
        setTimeout(() => {
          movedItem.classList.remove('moved');
        }, 800);
      }
    }, 100);
  }
}

function handleDragEnd(e) {
  this.classList.remove('dragging');
  
  // Remove all drag indicators
  const items = document.querySelectorAll('.draggable-item');
  items.forEach(item => {
    item.classList.remove('drag-above', 'drag-below');
  });
  
  draggedItem = null;
  draggedIndex = -1;
  dragOverItem = null;
}

/**
 * Saves the current hostgroup order from the modal
 */
async function saveHostgroupOrder() {
  const container = document.getElementById('draggableHostgroups');
  if (!container) return;

  const items = container.querySelectorAll('.draggable-item');
  const newOrder = Array.from(items).map(item => 
    item.getAttribute('data-group')
  );

  // Update the configuration
  if (tableOrderConfig) {
    tableOrderConfig.hostgroupOrder = newOrder;
    await saveTableOrderConfig();
  }
  
  // Close modal and refresh view
  closeHostgroupOrderModal();
  refreshTableView();
}

