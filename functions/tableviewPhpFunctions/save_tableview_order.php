<?php
/**
 * <PERSON><PERSON> for saving tableview order configuration
 * This endpoint receives POST requests with JSON data and saves it to tableview_order.json
 */

// Set content type to JSON
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get the JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON data']);
    exit;
}

// Validate the data structure
if (!isset($data['hostgroupOrder']) || !is_array($data['hostgroupOrder'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid data structure']);
    exit;
}

// Define the target file path
$configFile = __DIR__ . '/tableview_order.json';

try {
    // Write the configuration to file
    $result = file_put_contents($configFile, json_encode($data, JSON_PRETTY_PRINT));
    
    if ($result === false) {
        throw new Exception('Failed to write to file');
    }
    
    // Return success response
    echo json_encode(['success' => true, 'message' => 'Configuration saved successfully']);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to save configuration: ' . $e->getMessage()]);
}
?>
