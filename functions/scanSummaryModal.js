/**
 * Scan Summary Modal System
 * Displays a summary of scan results after redirecting to hosts.php
 */

class ScanSummaryModal {
    constructor() {
        this.modalId = 'scanSummaryModal';
        this.modal = null;
        this.init();
    }

    init() {
        this.checkForScanResults();
    }

    checkForScanResults() {
        const urlParams = new URLSearchParams(window.location.search);
        const scanSummary = urlParams.get('scanSummary');
        
        if (scanSummary && scanSummary.trim() !== '') {
            try {
                const summaryData = JSON.parse(decodeURIComponent(scanSummary));
                // Only show if we have valid data with actual scan results
                if (summaryData && (summaryData.hostsUp > 0 || summaryData.hostsDown > 0 || summaryData.totalScanned > 0)) {
                    this.showScanSummary(summaryData);
                    
                    // Clean up URL parameters after showing modal
                    this.cleanupURL();
                }
            } catch (error) {
                console.error('Error parsing scan summary data:', error);
                // Clean up invalid URL parameters
                this.cleanupURL();
            }
        }
    }

    showScanSummary(data) {
        this.createModal(data);
        this.show();
    }

    createModal(data) {
        // Remove existing modal if present
        const existingModal = document.getElementById(this.modalId);
        if (existingModal) {
            existingModal.remove();
        }

        const modalHTML = this.generateModalHTML(data);
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        this.modal = document.getElementById(this.modalId);
        this.setupEventListeners();
        
        // Add closeModal method for consistency with other modals
        this.modal.closeModal = function() {
            this.remove();
        };
    }

    generateModalHTML(data) {
        const {
            scanType = 'Network',
            targetInput = 'Unknown',
            hostsUp = 0,
            hostsDown = 0,
            totalScanned = 0,
            infrastructure = 'Unknown',
            scannedIPs = [],
            downIPs = [],
            upIPs = [],
            scanTime = new Date().toLocaleString(),
            isSingleIP = false
        } = data;

        let summaryContent = '';
        
        if (isSingleIP) {
            // Single IP scan - show specific status
            if (hostsUp > 0) {
                summaryContent = `
                    <div class="scan-summary-success">
                        <i class="fa fa-check-circle"></i>
                        <h3>Host is Online</h3>
                        <p>The scanned host <strong>${targetInput}</strong> is responding.</p>
                        <p class="scan-summary-note">If this host is not already monitored, it will appear in the host pending approval notification.</p>
                    </div>
                `;
            } else {
                summaryContent = `
                    <div class="scan-summary-warning">
                        <i class="fa fa-exclamation-triangle"></i>
                        <h3>Host Not Responding</h3>
                        <p>The scanned host <strong>${targetInput}</strong> is not responding or is down.</p>
                        <p class="scan-summary-note">This host may need further investigation.</p>
                    </div>
                `;
            }
        } else {
            // Range/multiple IP scan - show detailed breakdown
            summaryContent = `
                <div class="scan-summary-stats">
                    <h3>Scan Results Summary</h3>
                    <div class="scan-stats-grid">
                        <div class="scan-stat-item scan-stat-total">
                            <i class="fa fa-search"></i>
                            <span class="scan-stat-number">${totalScanned}</span>
                            <span class="scan-stat-label">IPs Scanned</span>
                        </div>
                        <div class="scan-stat-item scan-stat-up">
                            <i class="fa fa-check-circle"></i>
                            <span class="scan-stat-number">${hostsUp}</span>
                            <span class="scan-stat-label">Hosts Up</span>
                        </div>
                        <div class="scan-stat-item scan-stat-down">
                            <i class="fa fa-times-circle"></i>
                            <span class="scan-stat-number">${hostsDown}</span>
                            <span class="scan-stat-label">Unreachable IPs</span>
                        </div>
                    </div>
                    ${hostsUp > 0 ? `<p class="scan-summary-note">Hosts that are up will appear in the host pending approval notification if they're not already monitored.</p>` : ''}
                </div>
            `;
        }

        // Add discovered hosts section if any
        let discoveredHostsSection = '';
        if (upIPs && upIPs.length > 0) {
            // Create table rows with 4 IPs per row
            const rowSize = 4;
            const rows = [];
            for (let i = 0; i < upIPs.length; i += rowSize) {
                const rowIPs = upIPs.slice(i, i + rowSize);
                rows.push(rowIPs);
            }
            
            discoveredHostsSection = `
                <div class="discovered-hosts">
                    <h4><i class="fa fa-server"></i> Discovered Hosts (${upIPs.length})</h4>
                    <div class="discovered-hosts-table-container">
                        <table class="discovered-hosts-table">
                            ${rows.map(row => `
                                <tr>
                                    ${row.map(ip => `<td class="discovered-host-cell">${ip}</td>`).join('')}
                                    ${row.length < rowSize ? '<td class="discovered-host-cell-empty"></td>'.repeat(rowSize - row.length) : ''}
                                </tr>
                            `).join('')}
                        </table>
                    </div>
                </div>
            `;
        }

        return `
            <div id="${this.modalId}" class="scan-summary-modal-overlay">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h2><i class="fa fa-search"></i> Scan Complete</h2>
                        <button class="modal-close" onclick="scanSummaryModal.hide()" title="Close" style="background: none; border: none; cursor: pointer; padding: 5px;">
                            <i class="fa fa-times" style="font-size: 18px;"></i>
                        </button>
                    </div>
                    <div class="modal-content-approval" style="padding-top: 24px;">
                        <div class="scan-summary-info">
                            <div class="scan-info-row">
                                <span class="scan-info-label">Target:</span>
                                <span class="scan-info-value">${targetInput}</span>
                            </div>
                            <div class="scan-info-row">
                                <span class="scan-info-label">Infrastructure:</span>
                                <span class="scan-info-value">${infrastructure}</span>
                            </div>
                            <div class="scan-info-row">
                                <span class="scan-info-label">Scan Type:</span>
                                <span class="scan-info-value">${scanType}</span>
                            </div>
                            <div class="scan-info-row">
                                <span class="scan-info-label">Completed:</span>
                                <span class="scan-info-value">${scanTime}</span>
                            </div>
                        </div>
                        
                        ${summaryContent}
                        ${discoveredHostsSection}
                    </div>
                    <div class="modal-footer">
                        <button class="scan-summary-btn scan-summary-btn-primary" onclick="scanSummaryModal.hide()">
                            <i class="fa fa-check"></i> Continue
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        const closeBtn = this.modal.querySelector('.modal-close');
        
        // Close modal on X click
        if (closeBtn) {
            closeBtn.onclick = () => {
                this.hide();
            };
        }
    }

    show() {
        if (this.modal) {
            this.modal.style.display = 'flex';
            this.modal.style.alignItems = 'center';
            this.modal.style.justifyContent = 'center';
            // Add animation class after a brief delay
            setTimeout(() => {
                this.modal.classList.add('show');
            }, 10);
        }
    }

    hide() {
        if (this.modal) {
            this.modal.style.display = 'none';
            this.modal.classList.remove('show');
        }
    }

    cleanupURL() {
        // Remove scan summary parameters from URL without page reload
        const url = new URL(window.location);
        url.searchParams.delete('scanSummary');
        window.history.replaceState({}, document.title, url);
    }
}

// Function to generate minimal scan-specific CSS that works with existing modal styles
function generateScanSummaryStyles() {
    return `
<style>
/* Scan summary modal overlay */
.scan-summary-modal-overlay {
    display: flex;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

/* Scan-specific styles that work with existing modal theme styles */
.scan-summary-info {
    background: var(--surface, #f9f9f9);
    border: 1px solid var(--border, #e0e0e0);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.scan-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border, #e0e0e0);
}

.scan-info-row:last-child {
    border-bottom: none;
}

.scan-info-label {
    font-weight: 600;
    opacity: 0.8;
}

.scan-info-value {
    font-weight: 500;
}

.scan-summary-stats {
    text-align: center;
    margin-bottom: 20px;
}

.scan-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.scan-stat-item {
    background: var(--surface, #f9f9f9);
    border: 1px solid var(--border, #e0e0e0);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.scan-stat-item i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.scan-stat-total i {
    color: #2196f3;
}

.scan-stat-up i {
    color: #4caf50;
}

.scan-stat-down i {
    color: #f44336;
}

.scan-stat-number {
    font-size: 28px;
    font-weight: bold;
    display: block;
    margin: 8px 0;
}

.scan-stat-label {
    font-size: 14px;
    opacity: 0.8;
    letter-spacing: 0.5px;
}

.scan-summary-success {
    background: linear-gradient(135deg, #4caf5015, #4caf5008);
    border: 1px solid #4caf5040;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.scan-summary-success i {
    color: #4caf50;
    font-size: 48px;
    margin-bottom: 15px;
}

.scan-summary-success h3 {
    margin: 0 0 10px 0;
    font-size: 22px;
}

.scan-summary-warning {
    background: linear-gradient(135deg, #ff980015, #ff980008);
    border: 1px solid #ff980040;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.scan-summary-warning i {
    color: #ff9800;
    font-size: 48px;
    margin-bottom: 15px;
}

.scan-summary-warning h3 {
    margin: 0 0 10px 0;
    font-size: 22px;
}

.scan-summary-note {
    font-size: 14px;
    opacity: 0.7;
    margin-top: 10px;
    font-style: italic;
}

.discovered-hosts {
    background: var(--surface, #f9f9f9);
    border: 1px solid var(--border, #e0e0e0);
    border-radius: 8px;
    padding: 24px;
    margin-top: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.discovered-hosts h4 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    color: inherit;
    border-bottom: 1px solid var(--border, #e0e0e0);
    padding-bottom: 12px;
}

.discovered-hosts-table-container {
    overflow-x: auto;
}

.discovered-hosts-table {
    width: 100%;
    border-collapse: collapse;
}

.discovered-host-cell {
    background: var(--background, #ffffff);
    border: 1px solid var(--border, #e0e0e0);
    padding: 12px 16px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    vertical-align: middle;
}

.discovered-host-cell-empty {
    border: none;
    padding: 12px 16px;
}

.discovered-hosts-table tr:nth-child(even) .discovered-host-cell {
    background: var(--surface, #f9f9f9);
}



.scan-summary-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #4a90e2;
    color: #ffffff;
}

.scan-summary-btn-primary:hover {
    background: #357abd;
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .scan-stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>
`;
}

// Generate and inject styles into the document
const scanSummaryStyles = generateScanSummaryStyles();

// Inject styles into the document
document.head.insertAdjacentHTML('beforeend', scanSummaryStyles);

// Global instance
let scanSummaryModal = null;

// Initialize only when needed
function initScanSummaryModal() {
    if (!scanSummaryModal) {
        scanSummaryModal = new ScanSummaryModal();
    }
    return scanSummaryModal;
}

// Auto-initialize when DOM is ready, but only check for scan results
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        const urlParams = new URLSearchParams(window.location.search);
        const scanSummary = urlParams.get('scanSummary');
        if (scanSummary && scanSummary.trim() !== '') {
            initScanSummaryModal();
        }
    });
} else {
    const urlParams = new URLSearchParams(window.location.search);
    const scanSummary = urlParams.get('scanSummary');
    if (scanSummary && scanSummary.trim() !== '') {
        initScanSummaryModal();
    }
}
