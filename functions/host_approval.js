/**
 * Host Approval JavaScript Functions
 * Provides functionality for checking and managing hosts pending approval
 */

// Global variables
let pendingHosts = [];
let notificationInterval = null;
let currentInfra = '';
let hostsAdded = false;

// Initialize the host approval system
function initHostApproval(infraName) {
    currentInfra = infraName || '';
    hostsAdded = false;
    
    // Create notification container if it doesn't exist
    if (!document.getElementById('host-approval-notification')) {
        const notificationDiv = document.createElement('div');
        notificationDiv.id = 'host-approval-notification';
        notificationDiv.className = 'approval-notification hidden';
        document.body.appendChild(notificationDiv);
    }
    
    // Start checking for pending hosts
    checkPendingHosts();
    
    // Set interval to check periodically
    if (notificationInterval) {
        clearInterval(notificationInterval);
    }
    notificationInterval = setInterval(checkPendingHosts, 60000); // Check every minute
}

// Check for hosts pending approval
function checkPendingHosts() {
    const url = `host_approval.php?subnet=${urlParams.get('subnet')}&list=pending${currentInfra ? '&infra=' + encodeURIComponent(currentInfra) : ''}`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            const previousHadHosts = pendingHosts && pendingHosts.length > 0;
            pendingHosts = data.hosts || [];
            const totalHosts = data.total || pendingHosts.length;
            const limited = data.limited || false;

            if (pendingHosts.length > 0) {
                showNotification(totalHosts, limited);
                // After showing the notification, fetch OS for each host
                pendingHosts.forEach(host => {
                    fetchHostOS(host.ip);
                });
            } else {
                hideNotification();
                if (hostsAdded) {
                    location.reload();
                }
            }
        })
        .catch(error => console.error('Error checking pending hosts:', error));
}

// Show notification about pending hosts
function showNotification(totalHosts, limited) {
    const notification = document.getElementById('host-approval-notification');
    if (!notification) return;
    
    // Create notification content
    notification.innerHTML = `
        <h3><i class="fa fa-bell"></i> Hosts Pending Approval</h3>
        <p><span>${totalHosts}</span> new host${totalHosts !== 1 ? 's' : ''} detected${limited ? ' (showing 50 of ' + totalHosts + ')' : ''}</p>
        <button class="show-all" onclick="showAllPendingHosts()"><i class="fa fa-list"></i> View All Hosts</button>
    `;
    
    // Show the notification
    notification.classList.remove('hidden');
}

// Hide notification
function hideNotification() {
    const notification = document.getElementById('host-approval-notification');
    if (notification) {
        notification.classList.add('hidden');
    }
}

// Approve a host
function approveHost(ip, infra) {
    const button = event.target.closest('button');
    if (button) {
        button.classList.add('loading');
        button.disabled = true;
    }
    sendApprovalAction(ip, infra, 'approve');
}

// Reject a host
function rejectHost(ip, infra) {
    const button = event.target.closest('button');
    if (button) {
        button.classList.add('loading');
        button.disabled = true;
    }
    sendApprovalAction(ip, infra, 'reject');
}

// Send approval action to the server
function sendApprovalAction(ip, infra, action) {
    const url = `host_approval.php?ip=${encodeURIComponent(ip)}&infra=${encodeURIComponent(infra)}&action=${action}`;
    
    fetch(url)
        .then(response => response.text())
        .then(result => {
            console.log(result);
            // Refresh the list after action
            checkPendingHosts();
            hostsAdded = true;
        })
        .catch(error => {
            console.error(`Error ${action}ing host:`, error);
            // Remove loading state from all buttons
            document.querySelectorAll('button.loading').forEach(btn => {
                btn.classList.remove('loading');
                btn.disabled = false;
            });
        });
}

// Show modal with all pending hosts
function showAllPendingHosts() {
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';

    // Variables for pagination
    let currentPage = 1;
    let searchTerm = '';
    
    // Store currentPage in window for access by other functions
    window.currentPage = currentPage;

    // Function to generate table rows based on hosts
    const generateTableRows = (hostsToDisplay) => {
        if (hostsToDisplay.length === 0) {
            return '<tr><td colspan="7" class="text-center">No hosts found matching your search criteria</td></tr>';
        }
        
        return hostsToDisplay.map(host => `
            <tr data-ip="${host.ip}">
                <td><input type="checkbox" class="host-checkbox" data-ip="${host.ip}" data-infra="${host.infra}"></td>
                <td><span class="os-icon-placeholder"></span></td>
                <td>${host.hostname || host.ip}</td>
                <td>${host.ip}</td>
                <td>${host.subnet}</td>
                <td>${host.infra}</td>
                <td class="host-service-action-cell">
                    <button class="approve" onclick="approveHost('${host.ip}', '${host.infra}'); this.closest('tr').remove();"><i class="fa fa-check"></i></button>
                    <button class="reject" onclick="rejectHost('${host.ip}', '${host.infra}'); this.closest('tr').remove();"><i class="fa fa-times"></i></button>
                </td>
            </tr>
        `).join('');
    };
    
    // Function to fetch hosts from the server with search and pagination
    const fetchHosts = (page, search = '') => {
        const tableBody = document.getElementById('pendingHostsTableBody');
        const paginationContainer = document.getElementById('hostsPagination');
        const searchResultsInfo = document.getElementById('searchResultsInfo');
        const searchInput = document.getElementById('pendingHostSearch');
        const clearSearchButton = document.getElementById('clearPendingHostSearch');

        if (tableBody) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</td></tr>';
        }

        if (searchInput && clearSearchButton) {
            clearSearchButton.style.display = searchInput.value ? 'inline-block' : 'none';
        }

        // Build the URL with all necessary parameters
        let url = `host_approval.php?subnet=${urlParams.get('subnet')}&list=pending&page=${page}`;

        if (currentInfra) {
            url += `&infra=${encodeURIComponent(currentInfra)}`;
        }

        if (search) {
            url += `&search=${encodeURIComponent(search)}`;
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                const hosts = data.hosts || [];
                const totalHosts = data.total || 0;
                const totalPages = data.total_pages || 1;

                if (tableBody) {
                    tableBody.innerHTML = generateTableRows(hosts);
                    hosts.forEach(host => {
                        fetchHostOS(host.ip);
                    });
                }
                
                // Clear selection count when fetching new data
                updateHostSelectedCount();

                if (paginationContainer) {
                    updatePagination(paginationContainer, page, totalPages, totalHosts);
                }

                if (searchResultsInfo) {
                    if (search) {
                        searchResultsInfo.innerHTML = `Found ${totalHosts} host${totalHosts !== 1 ? 's' : ''} matching "${search}"`;
                        searchResultsInfo.style.display = 'block';
                    } else {
                        searchResultsInfo.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching hosts:', error);
                if (tableBody) {
                    tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Error loading hosts. Please try again.</td></tr>';
                }
            });
    };
    
    // Function to update pagination controls
    const updatePagination = (container, currentPage, totalPages, totalItems) => {
        // Don't show pagination if there's only one page
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        // Calculate which page numbers to show
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);
        
        // Adjust startPage if endPage is at max
        if (endPage === totalPages) {
            startPage = Math.max(1, endPage - 4);
        }
        
        let paginationHTML = `
            <div class="pagination-info">Showing ${(currentPage - 1) * 50 + 1}-${Math.min(currentPage * 50, totalItems)} of ${totalItems}</div>
            <div class="pagination-controls">
        `;
        
        // Previous button
        paginationHTML += `
            <button class="pagination-btn ${currentPage === 1 ? 'disabled' : ''}" 
                ${currentPage === 1 ? 'disabled' : `onclick="changeHostPage(${currentPage - 1})"`}>
                <i class="fa fa-angle-left"></i>
            </button>
        `;
        
        // First page
        if (startPage > 1) {
            paginationHTML += `
                <button class="pagination-btn" onclick="changeHostPage(1)">1</button>
                ${startPage > 2 ? '<span class="pagination-ellipsis">...</span>' : ''}
            `;
        }
        
        // Page numbers
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" 
                    onclick="changeHostPage(${i})">${i}</button>
            `;
        }
        
        // Last page
        if (endPage < totalPages) {
            paginationHTML += `
                ${endPage < totalPages - 1 ? '<span class="pagination-ellipsis">...</span>' : ''}
                <button class="pagination-btn" onclick="changeHostPage(${totalPages})">${totalPages}</button>
            `;
        }
        
        // Next button
        paginationHTML += `
            <button class="pagination-btn ${currentPage === totalPages ? 'disabled' : ''}" 
                ${currentPage === totalPages ? 'disabled' : `onclick="changeHostPage(${currentPage + 1})"`}>
                <i class="fa fa-angle-right"></i>
            </button>
        </div>`;
        
        container.innerHTML = paginationHTML;
    };
    
    // Make updatePagination globally accessible
    window.updateHostPagination = updatePagination;
    
    // Initialize the modal
    modalOverlay.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-header">
                <h2><i class="fa fa-server"></i> Hosts Pending Approval</h2>
                <div style="display: flex; align-items: center;">
                    <div class="dropdown" style="margin-right: 10px; position: relative;">
                        <button class="dropdown-toggle" onclick="toggleHostDropdown()" title="More options" style="background: none; border: none; cursor: pointer; margin-right: 10px; padding: 5px;">
                            <i class="fa fa-ellipsis-v" style="font-size: 18px;"></i>
                        </button>
                        <div id="hostDropdownMenu" class="dropdown-menu" style="display: none;">
                            <button class="dropdown-item" onclick="rejectAllHostsGlobal()"><i class="fa fa-times"></i> Reject All</button>
                        </div>
                    </div>
                    <button onclick="refreshHostsTable()" title="Refresh" style="background: none; border: none; cursor: pointer; margin-right: 10px; padding: 5px;">
                        <i class="fa fa-refresh" style="font-size: 18px;"></i>
                    </button>
                    <div class="modal-separator"></div>
                    <button class="modal-close" onclick="closeHostApprovalModal()" title="Close" style="background: none; border: none; cursor: pointer; padding: 5px;">
                        <i class="fa fa-times" style="font-size: 18px;"></i>
                    </button>
                </div>
            </div>
            <div class="modal-search-controls">
                <div class="search-input-wrapper">
                    <input type="text" id="pendingHostSearch" placeholder="Search by hostname or IP..." class="modal-search-input">
                    <button type="button" id="clearPendingHostSearch" class="clear-search-btn" style="display:none;">&times;</button>
                </div>
                <div id="searchResultsInfo" class="search-results-info" style="display: none;"></div>
            </div>
            <div class="modal-content-approval">
                <table class="host-table">
                    <thead>
                        <tr>
                            <th id="checkboxHeader"><input type="checkbox" id="hostSelectAllCheckbox" onclick="toggleHostSelectAll()"></th>
                            <th>OS</th>
                            <th>Hostname</th>
                            <th>IP Address</th>
                            <th>Subnet</th>
                            <th>Infrastructure</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="pendingHostsTableBody">
                        <tr><td colspan="7" class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</td></tr>
                    </tbody>
                </table>
            </div>
            <div id="hostsPagination" class="pagination-container"></div>
            <div class="modal-multi-select-controls">
                <div class="multi-select-info">
                    <span id="hostSelectedCount">0</span> hosts selected
                </div>
                <div class="multi-select-actions">
                    <button class="approve" onclick="approveSelectedHosts()"><i class="fa fa-check"></i> Approve Selected</button>
                    <button class="reject" onclick="rejectSelectedHosts()"><i class="fa fa-times"></i> Reject Selected</button>
                </div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    `;
    
    document.body.appendChild(modalOverlay);
    
    modalOverlay.closeModal = function() {
        this.remove();
    };
    
    // Define changePage function in the global scope
    window.changeHostPage = function(page) {
        currentPage = page;
        window.currentPage = page; // Update global currentPage
        
        // Clear selection when changing pages
        const selectAllCheckbox = document.getElementById('hostSelectAllCheckbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
        }
        
        fetchHosts(page, searchTerm);
    };
    
    // Add event listener for the search input with debounce
    const searchInput = document.getElementById('pendingHostSearch');
    const clearSearchButton = document.getElementById('clearPendingHostSearch');
    let searchTimeout = null;
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            if (clearSearchButton) {
                clearSearchButton.style.display = this.value ? 'inline-block' : 'none';
            }
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchTerm = this.value.toLowerCase();
                currentPage = 1;
                window.currentPage = 1;
                
                // Clear selection when searching
                const selectAllCheckbox = document.getElementById('hostSelectAllCheckbox');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
                
                fetchHosts(currentPage, searchTerm);
            }, 500);
        });
    }

    if (clearSearchButton) {
        clearSearchButton.addEventListener('click', function() {
            if (searchInput) {
                searchInput.value = '';
            }
            searchTerm = '';
            currentPage = 1;
            window.currentPage = 1;
            
            // Clear selection when clearing search
            const selectAllCheckbox = document.getElementById('hostSelectAllCheckbox');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
            }
            
            // Directly hide the clear button
            if (clearSearchButton) {
                clearSearchButton.style.display = 'none';
            }
            // Directly hide the search results info
            const searchResultsInfo = document.getElementById('searchResultsInfo');
            if (searchResultsInfo) {
                 searchResultsInfo.style.display = 'none';
            }
            // Fetch hosts once after clearing
            fetchHosts(currentPage, searchTerm);
        });
    }
    
    // Initial fetch
    fetchHosts(currentPage, searchTerm);
}

function closeHostApprovalModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.closeModal();
        setTimeout(() => {
            if (hostsAdded) {
                location.reload();
            }
        }, 100);
    }
}



// Function to refresh the hosts table with latest data
function refreshHostsTable() {
  // Get current page and search term if available
  const currentPage = window.currentPage || 1;
  const searchTerm = document.getElementById('pendingHostSearch')?.value || '';
  
  // Use the changePage function with current values
  if (typeof window.changeHostPage === 'function') {
      window.changeHostPage(currentPage);
  } else {
      // Fallback if changePage not defined yet
      const url = `host_approval.php?subnet=${urlParams.get('subnet')}&list=pending&page=${currentPage}${searchTerm ? '&search=' + encodeURIComponent(searchTerm) : ''}${currentInfra ? '&infra=' + encodeURIComponent(currentInfra) : ''}`;
      const tableBody = document.getElementById('pendingHostsTableBody');
      
      if (!tableBody) return;
      
      // Show loading indicator in the table
      tableBody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="fa fa-spinner fa-spin"></i> Refreshing...</td></tr>';
      
      // Fetch the latest host data
      fetch(url)
        .then(response => response.json())
        .then(data => {
          const hosts = data.hosts || [];
          
          if (hosts.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center">No hosts pending approval</td></tr>';
            
            // If no hosts left, close the modal
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
              setTimeout(() => {
                modal.remove();
                checkPendingHosts();
              }, 1000);
            }
            return;
          }
          
          // Generate table rows for the hosts
          tableBody.innerHTML = hosts.map(host => `
            <tr data-ip="${host.ip}">
              <td><input type="checkbox" class="host-checkbox" data-ip="${host.ip}" data-infra="${host.infra}"></td>
              <td><span class="os-icon-placeholder"></span></td>
              <td>${host.hostname || host.ip}</td>
              <td>${host.ip}</td>
              <td>${host.subnet}</td>
              <td>${host.infra}</td>
              <td class="host-service-action-cell">
                <button class="approve" onclick="approveHost('${host.ip}', '${host.infra}'); this.closest('tr').remove();"><i class="fa fa-check"></i></button>
                <button class="reject" onclick="rejectHost('${host.ip}', '${host.infra}'); this.closest('tr').remove();"><i class="fa fa-times"></i></button>
              </td>
            </tr>
          `).join('');
          
          // Fetch OS for hosts
          hosts.forEach(host => {
            fetchHostOS(host.ip);
          });
          
          // Update pagination if needed
          if (data.total_pages && data.total) {
            const paginationContainer = document.getElementById('hostsPagination');
            if (paginationContainer) {
              window.updateHostPagination(paginationContainer, currentPage, data.total_pages, data.total);
            }
          }
        })
        .catch(error => {
          console.error('Error refreshing hosts table:', error);
          tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Error refreshing hosts. Please try again.</td></tr>';
        });
  }
}

// Clean up on page unload
function cleanupHostApproval() {
    if (notificationInterval) {
        clearInterval(notificationInterval);
        notificationInterval = null;
    }
}

// Initialize when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Get the current infrastructure from the URL or page context
    const urlParams = new URLSearchParams(window.location.search);
    const infraParam = urlParams.get('infra');
    initHostApproval(infraParam);
    
    // Clean up when leaving the page
    window.addEventListener('beforeunload', cleanupHostApproval);
});

// Fetch OS information for a specific host IP and update the DOM
function fetchHostOS(ip) {
    const url = `detect_os.php?ip=${encodeURIComponent(ip)}`;
    const placeholders = document.querySelectorAll(`.host-item[data-ip="${ip}"] .os-icon-placeholder, .host-table tr[data-ip="${ip}"] .os-icon-placeholder`);

    // Add loading indicator
    placeholders.forEach(placeholder => {
        placeholder.innerHTML = '<i class="fa fa-spinner fa-spin" style="margin-right: 5px;"></i>'; // Simple spinner
    });

    fetch(url)
        .then(response => response.json())
        .then(data => {
            // Update the OS icon placeholder
            placeholders.forEach(placeholder => {
                // Create container for icon and deep scan button
                let html = `<img src="${data.svg}" alt="${data.alt}" title="${data.alt}" class="os-icon" style="width: 16px; height: 16px; vertical-align: middle; margin-right: 5px;">`;
                
                // Add deep scan button if supported
                if (data.can_deep_scan) {
                    html += `<button class="deep-scan-btn" onclick="runDeepScan('${ip}', this)" title="Run deep scan" style="font-size: 10px; padding: 0 3px; margin-right: 5px; cursor: pointer; background: none; border: 1px solid #ccc; border-radius: 3px;"><i class="fa fa-search-plus"></i></button>`;
                }
                
                placeholder.innerHTML = html;
            });
        })
        .catch(error => {
            console.error(`Error fetching OS for ${ip}:`, error);
            // Remove loading indicator on error, maybe show an error icon
            placeholders.forEach(placeholder => {
                placeholder.innerHTML = '<i class="fa fa-exclamation-triangle" title="Error fetching OS" style="color: red; margin-right: 5px;"></i>';
            });
        });
}

// Run a deep scan for a specific host IP
function runDeepScan(ip, buttonElement) {
    // Change the button to a loading spinner
    if (buttonElement) {
        buttonElement.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        buttonElement.disabled = true;
    }
    
    // Get all placeholders for this IP
    const placeholders = document.querySelectorAll(`.host-item[data-ip="${ip}"] .os-icon-placeholder, .host-table tr[data-ip="${ip}"] .os-icon-placeholder`);
    
    // Run the deep scan
    const url = `detect_os.php?ip=${encodeURIComponent(ip)}&deepscan=true`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            // Update all placeholders with the new OS information, keeping the deep scan button
            placeholders.forEach(placeholder => {
                // Create container for icon with updated info
                let html = `<img src="${data.svg}" alt="${data.alt}" title="${data.alt} (Deep scan)" class="os-icon" style="width: 16px; height: 16px; vertical-align: middle; margin-right: 5px;">`;
                // Add the deep scan button back
                html += `<button class="deep-scan-btn" onclick="runDeepScan('${ip}', this)" title="Run deep scan again" style="font-size: 10px; padding: 0 3px; margin-right: 5px; cursor: pointer; background: none; border: 1px solid #ccc; border-radius: 3px;"><i class="fa fa-search-plus"></i></button>`;
                
                placeholder.innerHTML = html;
            });
            
            // Re-enable the button
            if (buttonElement) {
                buttonElement.innerHTML = '<i class="fa fa-search-plus"></i>';
                buttonElement.disabled = false;
            }
        })
        .catch(error => {
            console.error(`Error running deep scan for ${ip}:`, error);
            // Reset the button on error
            if (buttonElement) {
                buttonElement.innerHTML = '<i class="fa fa-search-plus"></i>';
                buttonElement.disabled = false;
            }
            
            // Show error in placeholders
            placeholders.forEach(placeholder => {
                const currentContent = placeholder.innerHTML;
                if (currentContent.includes('fa-exclamation-triangle')) return; // Already showing error
                
                placeholder.innerHTML = currentContent + ' <i class="fa fa-exclamation-triangle" title="Deep scan failed" style="color: red; margin-right: 5px;"></i>';
            });
        });
}

// Function to approve all hosts globally (not just the current page)
function approveAllHostsGlobal() {
    if (confirm(`Are you sure you want to approve ALL pending hosts? This will affect all hosts, not just the ones on the current page.`)) {
        const button = event.target;
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }
        
        fetch(`host_approval.php?action=approve_all${currentInfra ? '&infra=' + encodeURIComponent(currentInfra) : ''}`)
            .then(response => response.text())
            .then(result => {
                console.log(result);
                // Update the underlying pending hosts data
                hostsAdded = true;
                
                // Refresh the table with the latest data
                refreshHostsTable();
                
                // Enable the button again
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error approving all hosts:', error);
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
                alert('An error occurred while approving all hosts. Please try again.');
            });
    }
}

// Function to reject all hosts globally (not just the current page)
function rejectAllHostsGlobal() {
    if (confirm(`Are you sure you want to reject ALL pending hosts? This will affect all hosts, not just the ones on the current page.`)) {
        const button = event.target;
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }
        
        fetch(`host_approval.php?action=reject_all${currentInfra ? '&infra=' + encodeURIComponent(currentInfra) : ''}`)
            .then(response => response.text())
            .then(result => {
                console.log(result);
                // Update the underlying pending hosts data
                hostsAdded = true;
                
                // Refresh the table with the latest data
                refreshHostsTable();
                
                // Enable the button again
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error rejecting all hosts:', error);
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
                alert('An error occurred while rejecting all hosts. Please try again.');
            });
    }
}

// Multi-select functionality
let multiSelectMode = true; // Always enabled now

function toggleHostSelectAll() {
    const selectAllCheckbox = document.getElementById('hostSelectAllCheckbox');
    const checkboxes = document.querySelectorAll('.host-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateHostSelectedCount();
}

function updateHostSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.host-checkbox:checked');
    const selectedCount = document.getElementById('hostSelectedCount');
    const multiSelectControls = document.querySelector('.modal-multi-select-controls');
    
    if (selectedCount) {
        selectedCount.textContent = selectedCheckboxes.length;
    }
    
    // Show/hide multi-select controls based on selection
    if (multiSelectControls) {
        if (selectedCheckboxes.length > 0) {
            multiSelectControls.style.display = 'flex';
        } else {
            multiSelectControls.style.display = 'none';
        }
    }
}

function approveSelectedHosts() {
    const selectedCheckboxes = document.querySelectorAll('.host-checkbox:checked');
    const selectedHosts = Array.from(selectedCheckboxes).map(checkbox => ({
        ip: checkbox.getAttribute('data-ip'),
        infra: checkbox.getAttribute('data-infra')
    }));
    
    if (selectedHosts.length === 0) {
        alert('No hosts selected');
        return;
    }
    
    if (confirm(`Are you sure you want to approve ${selectedHosts.length} selected hosts?`)) {
        const button = event.target;
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }
        
        const promises = selectedHosts.map(host => 
            fetch(`host_approval.php?ip=${encodeURIComponent(host.ip)}&infra=${encodeURIComponent(host.infra)}&action=approve`)
                .then(response => response.text())
        );
        
        Promise.all(promises)
            .then(() => {
                hostsAdded = true;
                refreshHostsTable();
                
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
                
                // Exit multi-select mode
                toggleMultiSelect();
            })
            .catch(error => {
                console.error('Error approving selected hosts:', error);
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            });
    }
}

function rejectSelectedHosts() {
    const selectedCheckboxes = document.querySelectorAll('.host-checkbox:checked');
    const selectedHosts = Array.from(selectedCheckboxes).map(checkbox => ({
        ip: checkbox.getAttribute('data-ip'),
        infra: checkbox.getAttribute('data-infra')
    }));
    
    if (selectedHosts.length === 0) {
        alert('No hosts selected');
        return;
    }
    
    if (confirm(`Are you sure you want to reject ${selectedHosts.length} selected hosts?`)) {
        const button = event.target;
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }
        
        const promises = selectedHosts.map(host => 
            fetch(`host_approval.php?ip=${encodeURIComponent(host.ip)}&infra=${encodeURIComponent(host.infra)}&action=reject`)
                .then(response => response.text())
        );
        
        Promise.all(promises)
            .then(() => {
                hostsAdded = true;
                refreshHostsTable();
                
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
                
                // Exit multi-select mode
                toggleMultiSelect();
            })
            .catch(error => {
                console.error('Error rejecting selected hosts:', error);
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            });
    }
}



// Add event listener for checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('host-checkbox')) {
        updateHostSelectedCount();
    }
});

// Toggle host dropdown menu
function toggleHostDropdown() {
    const dropdownMenu = document.getElementById('hostDropdownMenu');
    if (dropdownMenu) {
        dropdownMenu.style.display = dropdownMenu.style.display === 'none' ? 'block' : 'none';
    }
}

// Close dropdown when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('.dropdown')) {
        const dropdownMenu = document.getElementById('hostDropdownMenu');
        if (dropdownMenu) {
            dropdownMenu.style.display = 'none';
        }
    }
});


