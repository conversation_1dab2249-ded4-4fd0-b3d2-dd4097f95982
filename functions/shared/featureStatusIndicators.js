/**
 * Shared Feature Status Indicators module
 * Unifies logic across pages and adds a popover search bar.
 */

// Expose combined feature data globally for other modules if needed
window.combinedFeatureData = null;

/**
 * Initialize feature status indicators: styles, handlers, and periodic updates.
 */
function initFeatureStatusIndicators() {
    injectFeatureIndicatorStyles();
    setupIndicatorClickHandlers();
    updateFeatureStatusIndicators();
    setInterval(updateFeatureStatusIndicators, 30000);
    setupPopoverCloseHandlers();
}

/**
 * Inject styles for indicators and popover.
 */
function injectFeatureIndicatorStyles() {
    if (document.getElementById('featureIndicatorsStyles')) return;
    const style = document.createElement('style');
    style.id = 'featureIndicatorsStyles';
    style.innerHTML = `
        .hostlist-status-indicators { display: flex; background-color: transparent; padding: 0; gap: 10px; align-items: center; flex-wrap: wrap; margin-left: 15px; height: 26px; }
        .status-indicator { position: relative; display: flex; align-items: center; justify-content: center; width: 26px; height: 26px; border-radius: 50%; cursor: pointer; transition: background-color 0.2s, box-shadow 0.2s; background-color: rgba(255,255,255,0.08); box-shadow: 0 0 0 1px rgba(255,255,255,0.15); }
        .status-indicator:hover { background-color: rgba(255,255,255,0.18); box-shadow: 0 0 0 1px rgba(255,255,255,0.25); }
        .status-indicator i { color: #ccc; font-size: 13px; line-height: 26px; }
        .indicator-badge { position: absolute; top: 0px; right: 0px; transform: translate(40%, -40%); background-color: #F44336; color: white; border-radius: 50%; font-size: 9px; font-weight: bold; display: none; align-items: center; justify-content: center; width: 15px; height: 15px; text-align: center; border: 1px solid #403c3c; box-sizing: border-box; }
        .status-indicator.active i { color: #b4ca45; }
        .status-indicator.inactive i { color: #F44336; }
        .status-indicator.warning i { color: #ffc107; }
        .status-indicator.inactive .indicator-badge { background-color: #F44336; display: flex; }
        .status-indicator.warning .indicator-badge { background-color: #ffc107; color: #333; display: flex; }
        .status-hosts-popover { position: absolute; background-color: #3a3a3a; color: #f0f0f0; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.3); padding: 8px 12px; min-width: 240px; max-width: 360px; max-height: 320px; overflow-y: auto; z-index: 9999; display: none; border: 1px solid #555; }
        .feature-popover-header { position: sticky; top: -8px; z-index: 20; background-color: #3a3a3a; border-bottom: 1px solid #555; padding: 8px 10px 8px 10px; box-shadow: 0 2px 0 rgba(0,0,0,0.2); }
        .feature-popover-header h4 { position: relative; margin: 0 0 6px 0; padding: 0 28px 0 0; font-size: 13px; color: #ffffff; text-align: center; line-height: 1.2; }
        .status-hosts-popover ul { list-style-type: none; padding: 0; margin: 0; }
        .status-hosts-popover li { padding: 8px 10px; border-radius: 3px; cursor: pointer; transition: background-color 0.15s ease, color 0.15s ease; font-size: 12px; color: #e0e0e0; margin-bottom: 2px; border-bottom: 1px solid rgba(255,255,255,0.05); }
        .status-hosts-popover li:last-child { border-bottom: none; }
        .status-hosts-popover li:hover { background-color: rgba(255,255,255,0.1); color: #ffffff; }
        /* No close button */
        .feature-popover-search { margin: 0; display: flex; }
        .feature-popover-search input { width: 100%; padding: 6px 8px; border-radius: 4px; border: 1px solid #555; background: #2d2d2d; color: #eee; font-size: 12px; }
        @media (max-width: 768px) { .hostlist-status-indicators { margin-top: 5px; gap: 4px; flex-wrap: wrap; justify-content: flex-start; } .status-indicator { width: 18px !important; height: 18px !important; padding: 2px 4px !important; } .status-indicator i { font-size: 11px !important; } .mobile-status-container .hostlist-status-indicators { margin-left: 0; margin-top: 5px; width: 100%; justify-content: center; } }
    `;
    document.head.appendChild(style);
}

/**
 * Attach click handlers to indicators.
 */
function setupIndicatorClickHandlers() {
    const indicators = [
        'feature-flap-detection-status',
        'feature-notifications-status',
        'feature-event-handlers-status',
        'feature-active-checks-status',
        'feature-passive-checks-status',
        'feature-acknowledged-problems-status'
    ];
    indicators.forEach(baseId => {
        const el = document.getElementById(baseId);
        if (el) el.addEventListener('click', (e) => { e.stopPropagation(); showFeatureIssuePopover(baseId.replace('feature-','').replace('-status',''), el); });
        const mobileEl = document.getElementById(baseId + '-mobile');
        if (mobileEl) mobileEl.addEventListener('click', (e) => { e.stopPropagation(); showFeatureIssuePopover(baseId.replace('feature-','').replace('-status',''), mobileEl); });
    });
}

/**
 * Close popover handlers.
 */
function setupPopoverCloseHandlers() {
    // Ensure header structure is correct on init
    const popover = document.getElementById('feature-issue-popover');
    if (popover) ensureHeaderStructure(popover);
    const closeBtn = document.querySelector('.close-popover');
    if (closeBtn) closeBtn.addEventListener('click', () => { const p = document.getElementById('feature-issue-popover'); if (p) p.style.display = 'none'; });
    document.addEventListener('click', (e) => {
        const popover = document.getElementById('feature-issue-popover');
        if (!popover) return;
        const clickedIndicator = !!e.target.closest('.status-indicator');
        if (!popover.contains(e.target) && !clickedIndicator) popover.style.display = 'none';
    });
}

// Backward-compatibility alias for older init scripts
if (typeof window !== 'undefined' && typeof window.setupPopoverHandlers !== 'function') {
    window.setupPopoverHandlers = setupPopoverCloseHandlers;
}

/**
 * Fetch host object data (optionally scoped by hostgroup).
 */
async function fetchHostObjectData(selectedHostgroup = 'all') {
    const base = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true`;
    const url = selectedHostgroup && selectedHostgroup !== 'all' ? `${base}&hostgroup=${encodeURIComponent(selectedHostgroup)}` : base;
    const response = await fetch(url);
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    const data = await response.json();
    if (data.result.type_code !== 0) throw new Error(`API error: ${data.result.message}`);
    return data;
}

/**
 * Update indicators by aggregating host and service data.
 */
async function updateFeatureStatusIndicators() {
    try {
        const hostgroupSelect = document.getElementById('hostgroup-filter');
        const selectedHostgroup = hostgroupSelect ? hostgroupSelect.value : 'all';
        let hostStatusUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=hostlist&details=true`;
        let serviceStatusUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=servicelist&details=true`;
        if (selectedHostgroup && selectedHostgroup !== 'all') {
            const param = `&hostgroup=${encodeURIComponent(selectedHostgroup)}`;
            hostStatusUrl += param;
            serviceStatusUrl += param;
        }
        const [hostObjectData, hostStatusResponse, serviceStatusResponse] = await Promise.all([
            fetchHostObjectData(selectedHostgroup),
            fetch(hostStatusUrl),
            fetch(serviceStatusUrl)
        ]);
        if (!hostStatusResponse.ok) throw new Error(`HTTP error (host status): ${hostStatusResponse.status}`);
        if (!serviceStatusResponse.ok) throw new Error(`HTTP error (service status): ${serviceStatusResponse.status}`);
        const hostStatusDataAll = await hostStatusResponse.json();
        const serviceStatusDataAll = await serviceStatusResponse.json();
        if (hostStatusDataAll.result.type_code !== 0) throw new Error(`API error (host status): ${hostStatusDataAll.result.message}`);
        if (serviceStatusDataAll.result.type_code !== 0) throw new Error(`API error (service status): ${serviceStatusDataAll.result.message}`);
        const combined = {
            'flap-detection': { enabled: [], disabled: [], flapping: [], total_relevant: 0 },
            'notifications': { enabled: [], disabled: [], total_relevant: 0 },
            'event-handlers': { enabled: [], disabled: [], total_relevant: 0 },
            'active-checks': { enabled: [], disabled: [], total_relevant: 0 },
            'passive-checks': { enabled: [], disabled: [], total_relevant: 0 },
            'acknowledged-problems': { acknowledged: [], total_relevant: 0 }
        };
        if (hostStatusDataAll.data && hostStatusDataAll.data.hostlist) {
            for (const [hostName, hostStatus] of Object.entries(hostStatusDataAll.data.hostlist)) {
                const hostDetails = hostObjectData.data.hostlist ? hostObjectData.data.hostlist[hostName] : null;
                const hostItem = {
                    type: 'host', name: hostName, display_name: hostName, address: hostDetails?.address || hostName, subnet: hostDetails?.subnet || 'External',
                    is_flapping: Number(hostStatus.is_flapping) === 1,
                    flap_detection_enabled: Number(hostStatus.flap_detection_enabled) === 1,
                    notifications_enabled: Number(hostStatus.notifications_enabled) === 1,
                    event_handler_enabled: Number(hostStatus.event_handler_enabled) === 1,
                    active_checks_enabled: Number(hostStatus.checks_enabled) === 1,
                    passive_checks_enabled: Number(hostStatus.accept_passive_checks) === 1,
                    problem_has_been_acknowledged: Number(hostStatus.problem_has_been_acknowledged) === 1
                };
                combined['flap-detection'].total_relevant++;
                if (hostItem.flap_detection_enabled) { combined['flap-detection'].enabled.push(hostItem); if (hostItem.is_flapping) combined['flap-detection'].flapping.push(hostItem); } else combined['flap-detection'].disabled.push(hostItem);
                combined['notifications'].total_relevant++;
                if (hostItem.notifications_enabled) combined['notifications'].enabled.push(hostItem); else combined['notifications'].disabled.push(hostItem);
                combined['event-handlers'].total_relevant++;
                if (hostItem.event_handler_enabled) combined['event-handlers'].enabled.push(hostItem); else combined['event-handlers'].disabled.push(hostItem);
                combined['active-checks'].total_relevant++;
                if (hostItem.active_checks_enabled) combined['active-checks'].enabled.push(hostItem); else combined['active-checks'].disabled.push(hostItem);
                combined['passive-checks'].total_relevant++;
                if (hostItem.passive_checks_enabled) combined['passive-checks'].enabled.push(hostItem); else combined['passive-checks'].disabled.push(hostItem);
                combined['acknowledged-problems'].total_relevant++;
                if (hostItem.problem_has_been_acknowledged) combined['acknowledged-problems'].acknowledged.push(hostItem);
            }
        }
        if (serviceStatusDataAll.data && serviceStatusDataAll.data.servicelist) {
            for (const [hostName, services] of Object.entries(serviceStatusDataAll.data.servicelist)) {
                const hostDetails = hostObjectData.data.hostlist ? hostObjectData.data.hostlist[hostName] : null;
                for (const [serviceName, serviceStatus] of Object.entries(services)) {
                    const serviceItem = {
                        type: 'service', host_name: hostName, service_name: serviceName, display_name: `${hostName} - ${serviceName}`, host_address: hostDetails?.address || hostName, host_subnet: hostDetails?.subnet || 'External',
                        is_flapping: Number(serviceStatus.is_flapping) === 1,
                        flap_detection_enabled: Number(serviceStatus.flap_detection_enabled) === 1,
                        notifications_enabled: Number(serviceStatus.notifications_enabled) === 1,
                        event_handler_enabled: Number(serviceStatus.event_handler_enabled) === 1,
                        active_checks_enabled: Number(serviceStatus.checks_enabled) === 1,
                        passive_checks_enabled: Number(serviceStatus.accept_passive_checks) === 1,
                        problem_has_been_acknowledged: Number(serviceStatus.problem_has_been_acknowledged) === 1
                    };
                    combined['flap-detection'].total_relevant++;
                    if (serviceItem.flap_detection_enabled) { combined['flap-detection'].enabled.push(serviceItem); if (serviceItem.is_flapping) combined['flap-detection'].flapping.push(serviceItem); } else combined['flap-detection'].disabled.push(serviceItem);
                    combined['notifications'].total_relevant++;
                    if (serviceItem.notifications_enabled) combined['notifications'].enabled.push(serviceItem); else combined['notifications'].disabled.push(serviceItem);
                    combined['event-handlers'].total_relevant++;
                    if (serviceItem.event_handler_enabled) combined['event-handlers'].enabled.push(serviceItem); else combined['event-handlers'].disabled.push(serviceItem);
                    combined['active-checks'].total_relevant++;
                    if (serviceItem.active_checks_enabled) combined['active-checks'].enabled.push(serviceItem); else combined['active-checks'].disabled.push(serviceItem);
                    combined['passive-checks'].total_relevant++;
                    if (serviceItem.passive_checks_enabled) combined['passive-checks'].enabled.push(serviceItem); else combined['passive-checks'].disabled.push(serviceItem);
                    combined['acknowledged-problems'].total_relevant++;
                    if (serviceItem.problem_has_been_acknowledged) combined['acknowledged-problems'].acknowledged.push(serviceItem);
                }
            }
        }
        updateIndicator('feature-flap-detection', combined['flap-detection']);
        updateIndicator('feature-notifications', combined['notifications']);
        updateIndicator('feature-event-handlers', combined['event-handlers']);
        updateIndicator('feature-active-checks', combined['active-checks']);
        updateIndicator('feature-passive-checks', combined['passive-checks']);
        updateIndicator('feature-acknowledged-problems', combined['acknowledged-problems']);
        window.combinedFeatureData = combined;
    } catch (error) {
        console.error('Error updating feature status indicators:', error);
        document.querySelectorAll('.status-indicator[id^="feature-"]').forEach(indicator => {
            indicator.className = 'status-indicator error';
            const badge = indicator.querySelector('.indicator-badge');
            if (badge) { badge.textContent = 'ERR'; badge.style.display = 'flex'; }
        });
    }
}

/**
 * Update a given indicator across desktop and mobile.
 */
function updateIndicator(indicatorIdBase, data) {
    const indicators = [
        document.getElementById(`${indicatorIdBase}-status`),
        document.getElementById(`${indicatorIdBase}-status-mobile`)
    ];
    indicators.forEach(indicator => {
        if (!indicator) return;
        const badge = indicator.querySelector('.indicator-badge');
        indicator.className = 'status-indicator';
        if (badge) { badge.style.display = 'none'; badge.textContent = ''; }
        if (indicatorIdBase === 'feature-flap-detection') {
            const flappingCount = data.flapping ? data.flapping.length : 0;
            const disabledFlapDetectionCount = data.disabled ? data.disabled.length : 0;
            const totalFlapProblems = flappingCount + disabledFlapDetectionCount;
            if (totalFlapProblems > 0) {
                indicator.classList.add('warning');
                if (badge) { badge.textContent = totalFlapProblems; badge.style.display = 'flex'; }
                indicator.style.display = 'flex';
            } else {
                indicator.style.display = 'none';
            }
        } else if (indicatorIdBase === 'feature-acknowledged-problems') {
            const acknowledgedCount = data.acknowledged ? data.acknowledged.length : 0;
            if (acknowledgedCount > 0) {
                indicator.classList.add('warning');
                if (badge) { badge.textContent = acknowledgedCount; badge.style.display = 'flex'; }
                indicator.style.display = 'flex';
            } else {
                indicator.style.display = 'none';
            }
        } else {
            const disabledCount = data.disabled ? data.disabled.length : 0;
            if (disabledCount === 0) {
                indicator.style.display = 'none';
            } else if (data.total_relevant > 0 && disabledCount === data.total_relevant) {
                indicator.classList.add('inactive');
                if (badge) { badge.textContent = disabledCount; badge.style.display = 'flex'; }
                indicator.style.display = 'flex';
            } else if (disabledCount > 0) {
                indicator.classList.add('warning');
                if (badge) { badge.textContent = disabledCount; badge.style.display = 'flex'; }
                indicator.style.display = 'flex';
            } else if (data.total_relevant === 0) {
                indicator.style.display = 'none';
            } else {
                indicator.style.display = 'none';
            }
        }
    });
}

/**
 * Show popover with a search field and actionable list.
 */
function showFeatureIssuePopover(type, indicatorElement) {
    const popover = document.getElementById('feature-issue-popover');
    if (!popover) return;
    // Ensure header wrapper exists and contains title + close + search
    ensureHeaderStructure(popover);
    const popoverTitleEl = document.getElementById('popover-title');
    const popoverListEl = document.getElementById('popover-list');
    const badge = indicatorElement.querySelector('.indicator-badge');
    if (!badge || badge.style.display === 'none' || !badge.textContent) { popover.style.display = 'none'; return; }
    popoverListEl.innerHTML = '';
    let featureName = type;
    switch(type) { case 'flap-detection': featureName = 'Flap Detection'; break; case 'notifications': featureName = 'Notifications'; break; case 'event-handlers': featureName = 'Event Handlers'; break; case 'active-checks': featureName = 'Active Checks'; break; case 'passive-checks': featureName = 'Passive Checks'; break; case 'acknowledged-problems': featureName = 'Acknowledged Problems'; break; }
    const featureData = window.combinedFeatureData;
    if (!featureData || !featureData[type]) { popoverListEl.innerHTML = '<li>Data not available.</li>'; popoverTitleEl.textContent = `${featureName} - Error`; positionPopoverRelative(indicatorElement, popover); return; }
    const currentTypeData = featureData[type];
    const itemsToDisplay = [];
    if (type === 'flap-detection') {
        (currentTypeData.flapping || []).forEach(item => itemsToDisplay.push({ ...item, issue_type: 'Currently Flapping' }));
        (currentTypeData.disabled || []).forEach(item => itemsToDisplay.push({ ...item, issue_type: 'Flap Detection Disabled' }));
        popoverTitleEl.textContent = `Flap Detection Issues (${itemsToDisplay.length})`;
    } else if (type === 'acknowledged-problems') {
        (currentTypeData.acknowledged || []).forEach(item => itemsToDisplay.push({ ...item, issue_type: 'Problem Acknowledged' }));
        popoverTitleEl.textContent = `Acknowledged Problems (${itemsToDisplay.length})`;
    } else {
        (currentTypeData.disabled || []).forEach(item => itemsToDisplay.push({ ...item, issue_type: `${featureName} Disabled` }));
        popoverTitleEl.textContent = `Items with ${featureName} Disabled (${itemsToDisplay.length})`;
    }
    injectPopoverSearch(popover);
    if (itemsToDisplay.length === 0) {
        popoverListEl.innerHTML = `<li>No relevant items found.</li>`;
    } else {
        itemsToDisplay.forEach(item => {
            const li = document.createElement('li');
            let txt = item.type === 'host' ? `[Host] ${item.display_name} (${item.address})` : `[Service] ${item.display_name}`;
            if (item.issue_type) txt += ` - ${item.issue_type}`;
            li.textContent = txt;
            li.onclick = function() {
                if (item.type === 'host') openHostInModal(item.name, item.address, item.subnet);
                else openServiceInModal(item.host_name, item.host_address, item.host_subnet, item.service_name);
                popover.style.display = 'none';
            };
            popoverListEl.appendChild(li);
        });
    }
    positionPopoverRelative(indicatorElement, popover);
}

/**
 * Ensure the close button is appended into the H4 header so it sticks with it.
 */
function ensureHeaderStructure(popover) {
    let header = popover.querySelector('.feature-popover-header');
    const title = popover.querySelector('h4');
    const closeBtn = popover.querySelector('.close-popover');
    if (!header) {
        header = document.createElement('div');
        header.className = 'feature-popover-header';
        if (title) title.replaceWith(header);
        if (title) header.appendChild(title);
        if (closeBtn) header.appendChild(closeBtn);
    } else {
        if (title && title.parentElement !== header) header.appendChild(title);
        if (closeBtn && closeBtn.parentElement !== header) header.appendChild(closeBtn);
    }
}

/**
 * Add a search input to the popover if missing and wire up filtering.
 */
function injectPopoverSearch(popover) {
    let searchWrap = popover.querySelector('.feature-popover-search');
    if (!searchWrap) {
        const header = popover.querySelector('.feature-popover-header');
        searchWrap = document.createElement('div');
        searchWrap.className = 'feature-popover-search';
        const input = document.createElement('input');
        input.type = 'text';
        input.placeholder = 'Search...';
        input.id = 'feature-popover-search';
        searchWrap.appendChild(input);
        if (header) header.appendChild(searchWrap);
        input.addEventListener('input', () => {
            const q = input.value.toLowerCase();
            const items = popover.querySelectorAll('#popover-list li');
            items.forEach(li => { li.style.display = li.textContent.toLowerCase().includes(q) ? '' : 'none'; });
        });
    } else {
        const input = searchWrap.querySelector('input');
        if (input) { input.value = ''; const items = popover.querySelectorAll('#popover-list li'); items.forEach(li => li.style.display = ''); }
    }
}

/**
 * Position popover appropriately for mobile/desktop.
 */
function positionPopoverRelative(indicatorElement, popover) {
    const isMobile = window.innerWidth <= 768;
    if (isMobile) {
        const statusIndicatorsContainer = document.querySelector('.mobile-status-container .hostlist-status-indicators') || document.querySelector('.hostlist-status-indicators');
        if (statusIndicatorsContainer) {
            const containerRect = statusIndicatorsContainer.getBoundingClientRect();
            popover.style.position = 'fixed';
            popover.style.top = (containerRect.bottom + 10) + 'px';
            popover.style.left = '50%';
            popover.style.transform = 'translateX(-50%)';
            popover.style.maxHeight = '60vh';
            popover.style.zIndex = '10000';
            popover.style.display = 'block';
        } else {
            popover.style.position = 'fixed';
            popover.style.top = '50%';
            popover.style.left = '50%';
            popover.style.transform = 'translate(-50%, -50%)';
            popover.style.maxHeight = '80vh';
            popover.style.zIndex = '10000';
            popover.style.display = 'block';
        }
    } else {
        const rect = indicatorElement.getBoundingClientRect();
        popover.style.position = 'absolute';
        popover.style.top = (rect.bottom + window.scrollY + 5) + 'px';
        popover.style.left = (rect.left + window.scrollX) + 'px';
        popover.style.transform = 'none';
        popover.style.zIndex = '10000';
        popover.style.display = 'block';
        const checkEdges = () => {
            const pr = popover.getBoundingClientRect();
            if (pr.right > window.innerWidth) popover.style.left = (window.innerWidth - pr.width - 10) + 'px';
            if (pr.bottom > window.innerHeight) popover.style.top = (rect.top + window.scrollY - pr.height - 5) + 'px';
            if (pr.left < 0) popover.style.left = '10px';
        };
        setTimeout(checkEdges, 10);
    }
}

/**
 * Open host in the iframe modal.
 */
function openHostInModal(hostname, ip, subnet) {
    const urlParams = new URLSearchParams(window.location.search);
    const infraParam = urlParams.get('infra') || 'All';
    const url = `host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(infraParam)}&hostip=${encodeURIComponent(ip)}&subnet=${encodeURIComponent(subnet || 'External')}`;
    if (typeof window.showModal === 'function') { window.showModal(url, 'host'); return; }
    const modal = document.getElementById('infoModal');
    const iframe = document.getElementById('modal-frame') || document.getElementById('iframeModal-frame');
    if (modal && iframe) { modal.classList.remove('loaded'); iframe.style.display = 'none'; iframe.src = url; modal.style.display = 'block'; modal.classList.add('show'); iframe.onload = function() { const d = iframe.contentDocument || iframe.contentWindow.document; const nav = d.querySelector('nav'); if (nav) nav.style.display = 'none'; setTimeout(() => { try { const hostCard = d.getElementById('host-card'); if (hostCard) hostCard.click(); } catch(e) { console.error(e); } }, 500); modal.classList.add('loaded'); iframe.style.display = 'block'; }; } else { window.location.href = url; }
}

/**
 * Open service in the iframe modal and focus the service.
 */
function openServiceInModal(hostname, hostAddress, hostSubnet, serviceName) {
    const urlParams = new URLSearchParams(window.location.search);
    const infraParam = urlParams.get('infra') || 'All';
    const url = `host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(infraParam)}&hostip=${encodeURIComponent(hostAddress)}&subnet=${encodeURIComponent(hostSubnet || 'External')}`;
    if (typeof window.showModal === 'function') { window.showModal(url, 'service', serviceName); return; }
    const modal = document.getElementById('infoModal');
    const iframe = document.getElementById('modal-frame') || document.getElementById('iframeModal-frame');
    if (modal && iframe) { modal.classList.remove('loaded'); iframe.style.display = 'none'; iframe.src = url; modal.style.display = 'block'; modal.classList.add('show'); iframe.setAttribute('data-open-service-modal', 'true'); iframe.setAttribute('data-service-name', serviceName); iframe.onload = function() { const d = iframe.contentDocument || iframe.contentWindow.document; const nav = d.querySelector('nav'); if (nav) nav.style.display = 'none'; setTimeout(() => { try { const encoded = encodeURIComponent(serviceName); const card = d.querySelector(`.service-card[data-service="${encoded}"]`); if (card) card.click(); } catch(e) { console.error(e); } iframe.removeAttribute('data-open-service-modal'); iframe.removeAttribute('data-service-name'); }, 500); modal.classList.add('loaded'); iframe.style.display = 'block'; }; } else { window.location.href = url; }
}


