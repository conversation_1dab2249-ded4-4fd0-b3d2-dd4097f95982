// Service Discovery Settings Handler
document.addEventListener('DOMContentLoaded', () => {
    const serviceDiscoveryOn = document.getElementById('service-discovery-on');
    const serviceDiscoveryOff = document.getElementById('service-discovery-off');
    
    if (!serviceDiscoveryOn || !serviceDiscoveryOff) {
        return; // Elements not found, exit
    }
    
    // Ensure the line exists and load current setting
    ensureServiceDiscoveryLineExists().then(() => {
        loadServiceDiscoverySetting();
    });
    
    // Add event listeners for radio buttons
    serviceDiscoveryOn.addEventListener('change', () => {
        if (serviceDiscoveryOn.checked) {
            saveServiceDiscoverySetting('on');
        }
    });
    
    serviceDiscoveryOff.addEventListener('change', () => {
        if (serviceDiscoveryOff.checked) {
            saveServiceDiscoverySetting('off');
        }
    });
    
    // Initialize service discovery tab switching
    const serviceDiscoveryTabLinks = document.querySelectorAll('.service-discovery-tab-link');
    const serviceDiscoveryTabContents = document.querySelectorAll('.service-discovery-tab-content');
    
    serviceDiscoveryTabLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('data-target');
            
            // Activate clicked tab
            serviceDiscoveryTabLinks.forEach(function(tabLink) {
                tabLink.classList.remove('active');
            });
            this.classList.add('active');
            
            // Show target content
            serviceDiscoveryTabContents.forEach(function(content) {
                content.style.display = 'none';
            });
            const targetContent = document.getElementById(target);
            if (targetContent) {
                targetContent.style.display = 'block';
            }
            
            // Load content based on the tab
            if (target === 'service-discovery-muted-tab') {
                // Load muted hosts when muted tab is clicked
                setTimeout(function() {
                    if (document.getElementById('muted-table-container')) {
                        refreshMutedHosts();
                    }
                }, 200);
            } else if (target === 'service-discovery-exclusions-tab') {
                // Load service discovery exclusions when exclusions tab is clicked
                setTimeout(function() {
                    if (document.getElementById('do-not-scan-table-container')) {
                        refreshServiceDiscoveryExclusions();
                    }
                }, 200);
            }
        });
    });
    
    // Initialize refresh buttons for service discovery tabs
    const refreshMutedBtn = document.getElementById('refresh-muted-btn');
    if (refreshMutedBtn) {
        refreshMutedBtn.addEventListener('click', function() {
            refreshMutedHosts();
        });
    }
    
    const refreshDoNotScanBtn = document.getElementById('refresh-do-not-scan-btn');
    if (refreshDoNotScanBtn) {
        refreshDoNotScanBtn.addEventListener('click', function() {
            refreshServiceDiscoveryExclusions();
        });
    }
    
    async function ensureServiceDiscoveryLineExists() {
        try {
            // Make a GET request to trigger the PHP to add the line if it doesn't exist
            const response = await fetch('src/settingsphp/serviceDiscoverySettings.php', {
                method: 'GET'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            if (!result.success) {
                console.error('Failed to ensure service discovery line exists:', result.message);
            }
        } catch (error) {
            console.error('Error ensuring service discovery line exists:', error);
        }
    }
    
    async function loadServiceDiscoverySetting() {
        try {
            const response = await fetch('src/settingsphp/serviceDiscoverySettings.php', {
                method: 'GET'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                // Set the appropriate radio button
                if (result.setting === 'on') {
                    serviceDiscoveryOn.checked = true;
                } else {
                    serviceDiscoveryOff.checked = true;
                }
            } else {
                console.error('Failed to load service discovery setting:', result.message);
                // Default to 'on' if loading fails
                serviceDiscoveryOn.checked = true;
            }
        } catch (error) {
            console.error('Error loading service discovery setting:', error);
            // Default to 'on' if there's an error
            serviceDiscoveryOn.checked = true;
        }
    }
    
    async function saveServiceDiscoverySetting(setting) {
        try {
            const response = await fetch('src/settingsphp/serviceDiscoverySettings.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ setting: setting })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                // Show success message (optional)
                console.log('Service discovery setting updated successfully');
            } else {
                console.error('Failed to save service discovery setting:', result.message);
                // Revert the radio button selection
                if (setting === 'on') {
                    serviceDiscoveryOff.checked = true;
                } else {
                    serviceDiscoveryOn.checked = true;
                }
            }
        } catch (error) {
            console.error('Error saving service discovery setting:', error);
            // Revert the radio button selection
            if (setting === 'on') {
                serviceDiscoveryOff.checked = true;
            } else {
                serviceDiscoveryOn.checked = true;
            }
        }
    }
});
