// Host Discovery Settings Handler
document.addEventListener('DOMContentLoaded', () => {
    const hostDiscoveryOn = document.getElementById('host-discovery-on');
    const hostDiscoveryOff = document.getElementById('host-discovery-off');
    
    if (!hostDiscoveryOn || !hostDiscoveryOff) {
        return; // Elements not found, exit
    }
    
    // Ensure the line exists and load current setting
    ensureHostDiscoveryLineExists().then(() => {
        loadHostDiscoverySetting();
    });
    
    // Add event listeners for radio buttons
    hostDiscoveryOn.addEventListener('change', () => {
        if (hostDiscoveryOn.checked) {
            saveHostDiscoverySetting('on');
        }
    });
    
    hostDiscoveryOff.addEventListener('change', () => {
        if (hostDiscoveryOff.checked) {
            saveHostDiscoverySetting('off');
        }
    });
    
    // Initialize host discovery tab switching
    const hostDiscoveryTabLinks = document.querySelectorAll('.host-discovery-tab-link');
    const hostDiscoveryTabContents = document.querySelectorAll('.host-discovery-tab-content');
    
    hostDiscoveryTabLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            // Remove active class from all links and contents
            hostDiscoveryTabLinks.forEach(l => l.classList.remove('active'));
            hostDiscoveryTabContents.forEach(c => c.style.display = 'none');
            
            // Add active class to clicked link
            link.classList.add('active');
            
            // Show corresponding content
            const targetId = link.getAttribute('data-target');
            const targetContent = document.getElementById(targetId);
            if (targetContent) {
                targetContent.style.display = 'block';
                
                // Load subnet blacklist data when tab is activated
                if (targetId === 'host-discovery-subnet-blacklist-tab') {
                    loadSubnetBlacklistData();
                }
            }
        });
    });
});

// Function to ensure host discovery line exists in config
async function ensureHostDiscoveryLineExists() {
    try {
        const response = await fetch('src/settingsphp/hostDiscoverySettings.php', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        return result.success;
    } catch (error) {
        console.error('Error ensuring host discovery line exists:', error);
        return false;
    }
}

// Function to load current host discovery setting
async function loadHostDiscoverySetting() {
    try {
        const response = await fetch('src/settingsphp/hostDiscoverySettings.php', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            const hostDiscoveryOn = document.getElementById('host-discovery-on');
            const hostDiscoveryOff = document.getElementById('host-discovery-off');
            
            if (result.setting === 'on') {
                hostDiscoveryOn.checked = true;
            } else {
                hostDiscoveryOff.checked = true;
            }
        } else {
            console.error('Failed to load host discovery setting:', result.message);
        }
    } catch (error) {
        console.error('Error loading host discovery setting:', error);
    }
}

// Function to save host discovery setting
async function saveHostDiscoverySetting(setting) {
    try {
        const response = await fetch('src/settingsphp/hostDiscoverySettings.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                setting: setting
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Show success message
            showMessage('Host discovery setting updated successfully', 'success');
        } else {
            console.error('Failed to save host discovery setting:', result.message);
            showMessage('Failed to update host discovery setting: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error saving host discovery setting:', error);
        showMessage('Error updating host discovery setting', 'error');
    }
}

// Function to show status messages
function showMessage(message, type) {
    // Create or update status message
    let statusDiv = document.getElementById('host-discovery-status');
    if (!statusDiv) {
        statusDiv = document.createElement('div');
        statusDiv.id = 'host-discovery-status';
        statusDiv.className = 'message';
        statusDiv.style.marginTop = '15px';
        
        // Insert after the settings fieldset
        const settingsTab = document.getElementById('host-discovery-settings-tab');
        if (settingsTab) {
            settingsTab.appendChild(statusDiv);
        }
    }
    
    statusDiv.textContent = message;
    statusDiv.className = `message message-${type}`;
    statusDiv.style.display = 'block';
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 3000);
}

// Subnet Blacklist Functions
async function loadSubnetBlacklistData() {
    try {
        const response = await fetch('src/settingsphp/subnetBlacklistManager.php', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            populateSubnetDropdown(result.subnets, result.blacklisted);
            renderBlacklistedSubnets(result.blacklisted);
            setupSubnetBlacklistHandlers();
        } else {
            showSubnetBlacklistMessage('Failed to load subnet data', 'error');
        }
    } catch (error) {
        console.error('Error loading subnet blacklist data:', error);
        showSubnetBlacklistMessage('Error loading subnet data', 'error');
    }
}

function populateSubnetDropdown(subnets, blacklisted) {
    const select = document.getElementById('subnet-select');
    if (!select) return;
    
    select.innerHTML = '<option value="">Select a subnet...</option>';
    
    subnets.forEach(subnet => {
        if (!blacklisted.includes(subnet)) {
            const option = document.createElement('option');
            option.value = subnet;
            option.textContent = subnet;
            select.appendChild(option);
        }
    });
}

function renderBlacklistedSubnets(blacklisted) {
    const container = document.getElementById('subnet-blacklist-container');
    if (!container) return;
    
    if (blacklisted.length === 0) {
        container.innerHTML = '<div class="message message-info">No subnets are currently blacklisted.</div>';
        return;
    }
    
    let html = '<div class="service-modules-container">';
    html += '<details class="module-section" open>';
    html += '<summary class="module-header">';
    html += '<div class="module-name">Blacklisted Subnets <span class="module-status-indicator">' + blacklisted.length + ' subnet' + (blacklisted.length > 1 ? 's' : '') + '</span></div>';
    html += '</summary>';
    html += '<table class="module-table">';
    html += '<tbody>';
    
    blacklisted.forEach(subnet => {
        html += '<tr>';
        html += '<td style="padding: 10px; border-bottom: 1px solid var(--border);">' + subnet + '</td>';
        html += '<td style="padding: 10px; border-bottom: 1px solid var(--border); text-align: center;">';
        html += '<button class="btn btn-restart" onclick="removeSubnetFromBlacklist(\'' + subnet + '\')" title="Remove from blacklist">';
        html += '<i class="fa fa-undo"></i>';
        html += '</button>';
        html += '</td>';
        html += '</tr>';
    });
    
    html += '</tbody>';
    html += '</table>';
    html += '</details>';
    html += '</div>';
    
    container.innerHTML = html;
}

function setupSubnetBlacklistHandlers() {
    const addBtn = document.getElementById('add-subnet-blacklist-btn');
    const refreshBtn = document.getElementById('refresh-subnet-blacklist-btn');
    
    if (addBtn) {
        addBtn.addEventListener('click', addSubnetToBlacklist);
    }
    
    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadSubnetBlacklistData);
    }
}

async function addSubnetToBlacklist() {
    const select = document.getElementById('subnet-select');
    const subnet = select.value;
    
    if (!subnet) {
        showSubnetBlacklistMessage('Please select a subnet to blacklist', 'error');
        return;
    }
    
    if (!confirm('Are you sure you want to blacklist subnet ' + subnet + '? This will prevent all host discovery scanning on this subnet.')) {
        return;
    }
    
    try {
        const formData = new FormData();
        formData.append('action', 'add');
        formData.append('subnet', subnet);
        
        const response = await fetch('src/settingsphp/subnetBlacklistManager.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSubnetBlacklistMessage(result.message, 'success');
            loadSubnetBlacklistData(); // Refresh the data
        } else {
            showSubnetBlacklistMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('Error adding subnet to blacklist:', error);
        showSubnetBlacklistMessage('Error adding subnet to blacklist', 'error');
    }
}

async function removeSubnetFromBlacklist(subnet) {
    if (!confirm('Are you sure you want to remove subnet ' + subnet + ' from the blacklist? Host discovery scanning will resume for this subnet.')) {
        return;
    }
    
    try {
        const formData = new FormData();
        formData.append('action', 'remove');
        formData.append('subnet', subnet);
        
        const response = await fetch('src/settingsphp/subnetBlacklistManager.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSubnetBlacklistMessage(result.message, 'success');
            loadSubnetBlacklistData(); // Refresh the data
        } else {
            showSubnetBlacklistMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('Error removing subnet from blacklist:', error);
        showSubnetBlacklistMessage('Error removing subnet from blacklist', 'error');
    }
}

function showSubnetBlacklistMessage(message, type) {
    const statusDiv = document.getElementById('subnet-blacklist-status');
    if (!statusDiv) return;
    
    statusDiv.textContent = message;
    statusDiv.className = `message message-${type}`;
    statusDiv.style.display = 'block';
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 5000);
}

// Make functions globally available
window.removeSubnetFromBlacklist = removeSubnetFromBlacklist;
