 // Load configuration when detection tab is clicked
 document.querySelector('.top-tab-link[data-target="detection"]')?.addEventListener('click', function() {
    loadDetectionConfig();
});

// Function to load detection configuration
function loadDetectionConfig() {
    $.ajax({
        url: 'src/settingsphp/settingsConfigHandler.php',
        type: 'POST',
        data: { action: 'get_config' },
        dataType: 'json',
        success: function(config) {
            // Set Windows agent type
            $(`input[name="windowsAgentType"][value="${config.winmon_type}"]`).prop('checked', true);
            
            // Set Unix/Linux agent type
            $(`input[name="linuxAgentType"][value="${config.unixmon_type}"]`).prop('checked', true);
            
            // Set switch port detection
            $(`input[name="switchPortDetection"][value="${config.export_switchports}"]`).prop('checked', true);
            
            // Set checkboxes
            $('input[name="monitorSpanningTree"]').prop('checked', config.monitor_stp === 'yes');
            $('input[name="importToNCM"]').prop('checked', config.export_ncm === 'yes');
            
            // Set illegal characters
            $('#snmpIllegalChars').val(config.illegal_chars_snmp);
            $('#wmiIllegalChars').val(config.illegal_chars_wmi);
            
            // Set hostname style
            let hostnameStyle = 'short';
            if (config.shortname_dot_limit == 2) hostnameStyle = 'domain';
            else if (config.shortname_dot_limit == 99) hostnameStyle = 'full';
            $(`input[name="hostnameStyle"][value="${hostnameStyle}"]`).prop('checked', true);
        },
        error: function(xhr, status, error) {
            console.error('Failed to load config:', error);
        }
    });
}

// Save configuration when form elements change
function saveDetectionConfig() {
    const config = {
        winmon_type: $('input[name="windowsAgentType"]:checked').val(),
        unixmon_type: $('input[name="linuxAgentType"]:checked').val(),
        export_switchports: $('input[name="switchPortDetection"]:checked').val(),
        monitor_stp: $('input[name="monitorSpanningTree"]').is(':checked') ? 'yes' : 'no',
        export_ncm: $('input[name="importToNCM"]').is(':checked') ? 'yes' : 'no',
        illegal_chars_snmp: $('#snmpIllegalChars').val(),
        illegal_chars_wmi: $('#wmiIllegalChars').val(),
        shortname_dot_limit: getDotLimitFromHostnameStyle()
    };

    $.ajax({
        url: 'src/settingsphp/settingsConfigHandler.php',
        type: 'POST',
        data: {
            action: 'update_config',
            config: config
        },
        dataType: 'json',
        success: function() {
            console.log('Configuration saved successfully');
        },
        error: function(xhr, status, error) {
            console.error('Failed to save config:', error);
        }
    });
}

// Helper function to convert hostname style to dot limit
function getDotLimitFromHostnameStyle() {
    const style = $('input[name="hostnameStyle"]:checked').val();
    switch(style) {
        case 'short': return 1;
        case 'domain': return 2;
        case 'full': return 99;
        default: return 1;
    }
}

// Set up change handlers for all detection config elements
function setupDetectionConfigHandlers() {
    $('input[name="windowsAgentType"], input[name="linuxAgentType"], input[name="switchPortDetection"], input[name="hostnameStyle"]').change(saveDetectionConfig);
    $('input[name="monitorSpanningTree"], input[name="importToNCM"]').change(saveDetectionConfig);
    $('#snmpIllegalChars, #wmiIllegalChars').on('blur', saveDetectionConfig);
}

// Initialize when DOM is loaded
$(document).ready(function() {
    setupDetectionConfigHandlers();
});

// Function to handle log form submission
function handleLogFormSubmit(event) {
    event.preventDefault(); // Prevent default form submission
    const form = event.target; // The form element that triggered the submit
    const logsPane = document.getElementById('general-system-logs'); // The parent pane

    if (!logsPane) {
        console.error('Log pane #general-system-logs not found.');
        return;
    }

    // Optional: Show a loading indicator within the pane
    logsPane.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Refreshing logs...</div>';

    const formData = new FormData(form);
    fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest' // Ensure server knows this is an AJAX request
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(html => {
            // Replace the entire content of the logs pane
            logsPane.innerHTML = html;
            // IMPORTANT: Find the NEW form within the updated content and attach the listener again
            const newForm = logsPane.querySelector('#log-form');
            if (newForm) {
                newForm.addEventListener('submit', handleLogFormSubmit);
            } else {
                console.error('New log form (#log-form) not found after refresh.');
                logsPane.innerHTML += '<p class="message message-error">Error: Could not re-initialize log form.</p>'; // Add error message
            }
        })
        .catch(error => {
            // Display error within the pane
            logsPane.innerHTML = `<div class="message message-error">Failed to load logs: ${error.message}</div>`;
        });
}

document.addEventListener('DOMContentLoaded', () => {
    // Function to load the iframe
    function loadConsoleIframe() {
        const container = document.getElementById('console-container');
        if (!container.querySelector('iframe')) {
            const iframe = document.createElement('iframe');
            iframe.id = 'console-iframe';
            iframe.name = 'iframe1';
            iframe.src = '/shell'; // Shellinabox URL
            iframe.style.border = 'none';
            iframe.style.margin = '0';
            iframe.style.padding = '0';
            iframe.style.background = '#ffffff';
            iframe.style.fontSize = '10px';
            iframe.style.width = '100%';
            iframe.style.height = '400px';
            container.appendChild(iframe);
        }
    }

    // Function to unload the iframe
    function unloadConsoleIframe() {
        const container = document.getElementById('console-container');
        const iframe = container.querySelector('#console-iframe');
        if (iframe) {
            iframe.remove(); // Remove the iframe from the DOM
        }
    }

    // System Console tab click handler
    const consoleTabLink = document.querySelector('.sub-tab-link[data-target="general-system-console"]');
    consoleTabLink?.addEventListener('click', () => {
        // Load iframe when tab is clicked
        loadConsoleIframe();

        // Ensure iframe is removed when switching to another sub-tab
        const allSubTabLinks = document.querySelectorAll('.sub-tab-link');
        allSubTabLinks.forEach(link => {
            if (link !== consoleTabLink) {
                link.addEventListener('click', unloadConsoleIframe, {
                    once: true
                });
            }
        });
    });

    // Handle modal close to unload iframe
    const closeModalBtn = document.getElementById('closeModalBtn');
    closeModalBtn?.addEventListener('click', () => {
        unloadConsoleIframe();
    });
    const settingsModal = document.getElementById('settingsModal');
    settingsModal?.addEventListener('click', (event) => {
        if (event.target === settingsModal) { // Click outside modal content
            unloadConsoleIframe();
        }
    });
    
    // --- Service Tab Listener ---
    document.querySelector('.sub-tab-link[data-target="general-system-services"]')?.addEventListener('click', () => {
        const servicesPane = document.getElementById('general-system-services');
        const loadingDiv = document.getElementById('services-loading'); // Assuming this ID exists initially

        // Check if pane exists and not already loaded
        if (servicesPane && !servicesPane.dataset.loaded) {
            // Ensure loading indicator exists or add one
            if (!loadingDiv && !servicesPane.querySelector('.fa-spinner')) {
                servicesPane.innerHTML = '<div id="services-loading" style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Loading services...</div>';
            } else if (loadingDiv) {
                loadingDiv.style.display = 'block';
            }

            fetch('/bubblemaps/src/settingsphp/systemServices.php')
                .then(response => response.text())
                .then(html => {
                    servicesPane.innerHTML = html; // Replace content
                    servicesPane.dataset.loaded = true; // Mark as loaded
                    initializeServiceActions(); // Initialize actions for the loaded content
                })
                .catch(error => {
                    servicesPane.innerHTML = `<div class="message message-error">Failed to load services: ${error.message}</div>`;
                });
        } else if (servicesPane && servicesPane.dataset.loaded) {
            // Content already loaded, ensure actions are initialized (might be needed if tab is revisited)
            initializeServiceActions();
        }
    });

    // --- Logs Tab Listener ---
    document.querySelector('.sub-tab-link[data-target="general-system-logs"]')?.addEventListener('click', () => {
        const logsPane = document.getElementById('general-system-logs');
        const loadingDiv = document.getElementById('logs-loading'); // Assuming this ID exists initially

        // Check if pane exists and not already loaded
        if (logsPane && !logsPane.dataset.loaded) {
            // Ensure loading indicator exists or add one
            if (!loadingDiv && !logsPane.querySelector('.fa-spinner')) {
                logsPane.innerHTML = '<div id="logs-loading" style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Loading logs...</div>';
            } else if (loadingDiv) {
                loadingDiv.style.display = 'block';
            }

            fetch('/bubblemaps/src/settingsphp/systemLogs.php', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    logsPane.innerHTML = html; // Replace content of the pane
                    logsPane.dataset.loaded = true; // Mark as loaded
                    // Find the form that was just loaded and attach the submit handler
                    const initialForm = logsPane.querySelector('#log-form');
                    if (initialForm) {
                        initialForm.addEventListener('submit', handleLogFormSubmit);
                    } else {
                        console.error('Initial log form (#log-form) not found after load.');
                        logsPane.innerHTML += '<p class="message message-error">Error: Could not initialize log form.</p>'; // Add error message
                    }
                })
                .catch(error => {
                    logsPane.innerHTML = `<div class="message message-error">Failed to load logs: ${error.message}</div>`;
                });
        }
    });
}); // End DOMContentLoaded

    // --- Blacklist Tab Functionality ---
    const blacklistTabLink = document.querySelector('.sub-tab-link[data-target="detection-host-discovery"]');
    const blacklistPane = document.getElementById('detection-host-discovery');
    const blacklistContainer = document.getElementById('blacklist-table-container');
    const refreshBlacklistBtn = document.getElementById('refresh-blacklist-btn');
    
    // Host Discovery Blacklist Tab
    const hostDiscoveryBlacklistTabLink = document.querySelector('[data-target="host-discovery-blacklist-tab"]');

    function loadBlacklist() {
        if (!blacklistContainer) return;
        // Show loading indicator
        blacklistContainer.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Loading blacklist...</div>';

        fetch('/bubblemaps/src/settingsphp/blacklistManager.php', {
                headers: { 'X-Requested-With': 'XMLHttpRequest' } // Good practice for backend differentiation
            })
            .then(response => {
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                return response.text();
            })
            .then(html => {
                blacklistContainer.innerHTML = html;
                // Re-attach listeners if needed, though delegation handles button clicks
            })
            .catch(error => {
                blacklistContainer.innerHTML = `<div class="message message-error">Failed to load blacklist: ${error.message}</div>`;
            });
    }

    if (blacklistTabLink && blacklistPane) {
        blacklistTabLink.addEventListener('click', () => {
            // Load content only if the pane is active and not already loaded (or force reload)
            // Using a simple check here, could add a data-loaded attribute if needed
            if (!blacklistContainer.querySelector('#blacklist-table')) {
                 loadBlacklist();
            }
        });
    }
    
    // Host Discovery Blacklist Tab event listener
    if (hostDiscoveryBlacklistTabLink) {
        hostDiscoveryBlacklistTabLink.addEventListener('click', () => {
            // Small delay to ensure the tab content is visible
            setTimeout(() => {
                if (!blacklistContainer.querySelector('.blacklist-manager')) {
                    loadBlacklist();
                }
            }, 100);
        });
    }

    // Event delegation for remove buttons
    if (blacklistContainer) {
        blacklistContainer.addEventListener('click', (event) => {
            if (event.target.closest('.btn-remove-blacklist')) {
                const button = event.target.closest('.btn-remove-blacklist');
                const ip = button.dataset.ip;
                const infra = button.dataset.infra;

                if (!ip || !infra) {
                    alert('Error: Could not get IP or Infrastructure for removal.');
                    return;
                }

                if (!confirm(`Are you sure you want to remove host ${ip} (${infra}) from the blacklist?`)) {
                    return;
                }

                // Disable button temporarily
                button.disabled = true;
                button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                window.blacklistChanged = true;
                const formData = new FormData();
                formData.append('action', 'remove_blacklist');
                formData.append('ip', ip);
                formData.append('infra', infra);

                fetch('/bubblemaps/src/settingsphp/blacklistManager.php', {
                        method: 'POST',
                        body: formData,
                        headers: { 'X-Requested-With': 'XMLHttpRequest' }
                    })
                    .then(response => response.json()) // Expecting JSON response from POST
                    .then(data => {
                        if (data.success) {
                            // Refresh the list after successful removal
                            loadBlacklist();
                        } else {
                            alert(`Failed to remove host: ${data.message || 'Unknown error'}`);
                            // Re-enable button on failure
                            button.disabled = false;
                            button.innerHTML = '<i class="fa fa-undo"></i>';
                        }
                    })
                    .catch(error => {
                        console.error('Error removing from blacklist:', error);
                        alert(`An error occurred: ${error.message}`);
                        // Re-enable button on fetch error
                        button.disabled = false;
                        button.innerHTML = '<i class="fa fa-undo"></i>';
                    });
            }
        });
    }
    
    // Refresh button listener
    if (refreshBlacklistBtn) {
        refreshBlacklistBtn.addEventListener('click', loadBlacklist);
    }

    // --- System Update Button Listener ---
    const updateButton = document.getElementById('run-dnf-update-btn');
    const updateStatusDiv = document.getElementById('update-status');

    if (updateButton && updateStatusDiv) {
        updateButton.addEventListener('click', () => {
            updateButton.disabled = true;
            updateButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i>&nbsp;Updating...';
            updateStatusDiv.style.display = 'block';
            updateStatusDiv.innerHTML = '<div class="message message-info"><i class="fa fa-spinner fa-spin"></i> Starting system update... Please wait. This may take several minutes.</div>'; // Use message-info class

            fetch('/bubblemaps/src/settingsphp/runSystemUpdate.php', { // Path to the new backend script
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        // Try to get error text, otherwise use status text
                        return response.text().then(text => {
                            throw new Error(text || `HTTP error! Status: ${response.status}`);
                        });
                    }
                    return response.text(); // Expecting plain text or HTML output
                })
                .then(data => {
                    // Display the raw output from the command
                    updateStatusDiv.innerHTML = `<div class="message message-success">Update process finished. Output:</div><pre class="update-output">${data}</pre>`; // Added class, removed inline style
                })
                .catch(error => {
                    console.error('Error running system update:', error);
                    updateStatusDiv.innerHTML = `<div class="message message-error">Update failed: ${error.message}</div>`; // Ensure message-error class is used
                })
                .finally(() => {
                    // Re-enable the button regardless of success or failure
                    updateButton.disabled = false;
                    updateButton.innerHTML = '<i class="fa fa-arrow-circle-o-up"></i>&nbsp;Update Now';
                });
        });
    }

    // --- Automatic Updates Radio Button Listener ---
    const autoUpdatesOn = document.getElementById('auto-updates-on');
    const autoUpdatesOff = document.getElementById('auto-updates-off');

    if (autoUpdatesOn && autoUpdatesOff) {
        // Check current status on load
        fetch('/bubblemaps/src/settingsphp/autoUpdatesHandler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({action: 'check'})
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to check service status');
            }
            return response.json();
        })
        .then(data => {
            if (data.isActive) {
                autoUpdatesOn.checked = true;
            } else {
                autoUpdatesOff.checked = true;
            }
        })
        .catch(error => {
            console.error('Error checking update service status:', error);
            // Default to off if check fails
            autoUpdatesOff.checked = true;
        })
        .catch(() => {
            // Default to off if check fails
            autoUpdatesOff.checked = true;
        });

        // Handle changes
        autoUpdatesOn.addEventListener('change', (e) => {
            if (e.target.checked) {
                updateAutoUpdates('enable');
            }
        });

        autoUpdatesOff.addEventListener('change', (e) => {
            if (e.target.checked) {
                updateAutoUpdates('disable');
            }
        });

        function updateAutoUpdates(action) {
            const statusDiv = document.getElementById('update-status');
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = `<div class="message message-info"><i class="fa fa-spinner fa-spin"></i> Updating automatic updates setting...</div>`;

            fetch('/bubblemaps/src/settingsphp/autoUpdatesHandler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({action})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusDiv.innerHTML = `<div class="message message-success">${data.message}</div>`;
                } else {
                    statusDiv.innerHTML = `<div class="message message-error">Failed to update setting: ${data.error || 'Unknown error'}</div>`;
                    // Revert radio button state on failure
                    if (action === 'enable') {
                        autoUpdatesOff.checked = true;
                    } else {
                        autoUpdatesOn.checked = true;
                    }
                }
            })
            .catch(error => {
                statusDiv.innerHTML = `<div class="message message-error">Error: ${error.message}</div>`;
                // Revert radio button state on error
                if (action === 'enable') {
                    autoUpdatesOff.checked = true;
                } else {
                    autoUpdatesOn.checked = true;
                }
            });
        }
    }
