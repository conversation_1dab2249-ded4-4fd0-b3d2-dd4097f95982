// Context Menu and Submenu
const setupContextMenu = () => {
    const contextMenu = document.getElementById('context-menu');
    const contextMenuTitle = contextMenu.querySelector('.context-menu-title');

    // Function to generate hostgroup context menu items
    const generateHostgroupMenuItems = (hostgroup) => {
        const baseUrl = `https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi`;
        return `
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=84&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-moon-o fa-lg"></i> Schedule downtime for all hosts</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=85&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-moon-o fa-lg"></i> Schedule downtime for all services</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=65&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-microphone-slash fa-lg"></i> Enable notifications for all hosts</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=66&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-microphone fa-lg"></i> Disable notifications for all hosts</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=63&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-microphone-slash fa-lg"></i> Enable notifications for all services</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=64&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-microphone fa-lg"></i> Disable notifications for all services</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=67&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-toggle-off fa-lg"></i> Enable active checks of all services</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=68&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-toggle-on fa-lg"></i> Disable active checks of all services</a></li>
            <li><a href="#" onclick="renameHostgroup('${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-pencil fa-lg"></i> Rename hostgroup</a></li>
            <li><a href="#" onclick="addNewHostgroup(); return false;"><i class="fa fa-plus fa-lg"></i> Add Hostgroup</a></li>
        `;
    };

    document.addEventListener('contextmenu', (event) => {
        contextMenu.style.display = 'none'; // Reset display

        const ul = contextMenu.querySelector('ul');
        if (event.target.classList.contains('host-bubble')) {
            event.preventDefault();
            currentBubble = event.target;
            const bubbleData = d3.select(currentBubble).datum();
            
            // Check if we're in multi-select mode and this host is selected
            const selectedHosts = window.selectedHosts || new Set();
            const isSelected = selectedHosts.has(bubbleData.id);
            const hasMultipleSelected = selectedHosts.size > 1;
            
            if (isSelected && hasMultipleSelected) {
                // Multi-select context menu
                contextMenuTitle.textContent = `${selectedHosts.size} hosts selected`;
                ul.innerHTML = `
                    <li id="move-to"><i class="fa fa-arrows fa-lg"></i> Move all selected hosts to</li>
                    <li id="delete" style="color: #ff4444;"><i class="fa fa-trash fa-lg"></i> Delete selected hosts</li>
                    <li id="clear-selection"><i class="fa fa-times fa-lg"></i> Clear selection</li>
                `;
            } else {
                // Regular single host context menu
                contextMenuTitle.textContent = `Host: ${bubbleData.hostname}`;
                ul.innerHTML = `
                    <li id="view"><i class="fa fa-eye fa-lg"></i> Open Details</li>
                    <li id="parent-options"><i class="fa fa-sitemap fa-lg"></i> Parent Options</li>
                    <li id="show-links"><i class="fa fa-filter fa-lg"></i> Filter by Connections</li>
                    <li id="move-to"><i class="fa fa-arrows fa-lg"></i> Relocate to</li>
                    <li id="rename" style="color: #007bff;"><i class="fa fa-pencil fa-lg"></i> Rename Host</li>
                    <li id="delete" style="color: #ff4444;"><i class="fa fa-trash fa-lg"></i> Delete Host</li>
                `;
            }
            
            positionContextMenu(event, contextMenu);
        } else if (event.target.classList.contains('group-bubble')) {
            event.preventDefault();
            const groupData = d3.select(event.target).datum();
            const hostgroup = groupData.hostgroup;
            contextMenuTitle.textContent = `Hostgroup: ${hostgroup}`;
            ul.innerHTML = generateHostgroupMenuItems(hostgroup);
            positionContextMenu(event, contextMenu);
        }
    });

    document.addEventListener('click', (event) => {
        if (!currentBubble && !event.target.closest('.group-bubble')) {
            contextMenu.style.display = 'none';
            return;
        }

        if (event.target.classList.contains('host-bubble') || contextMenu.contains(event.target)) {
            const bubbleData = currentBubble ? d3.select(currentBubble).datum() : null;
            const isClickInsideMenu = contextMenu.contains(event.target);

            if (event.target.id === 'rename' && bubbleData) {
                const newName = prompt('Enter new name:', bubbleData.hostname);
                if (newName && newName.trim() !== '') {
                    // Show loading indicator
                    const loadingDiv = document.createElement('div');
                    loadingDiv.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.8);color:white;padding:20px;border-radius:8px;z-index:10000;';
                    loadingDiv.innerHTML = `Updating hostname... <div style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:30px;height:30px;animation:spin 1s linear infinite;margin:10px auto;"></div>`;
                    document.body.appendChild(loadingDiv);
                    
                    // Add spin animation if not already present
                    if (!document.getElementById('spinner-styles')) {
                        const style = document.createElement('style');
                        style.id = 'spinner-styles';
                        style.textContent = `
                            @keyframes spin {
                                0% { transform: rotate(0deg); }
                                100% { transform: rotate(360deg); }
                            }
                        `;
                        document.head.appendChild(style);
                    }
                    
                    // Make the API call
                    fetch('update_host_name.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `id=${encodeURIComponent(bubbleData.id)}&newName=${encodeURIComponent(newName.trim())}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        document.body.removeChild(loadingDiv);
                        
                        if (data.success) {
                            // Update the bubble data and display
                            bubbleData.hostname = newName.trim();
                            d3.select(currentBubble).datum(bubbleData);
                            d3.selectAll('#map g .bubble-text')
                                .filter(d => d.id === bubbleData.id)
                                .each(d => d.hostname = newName.trim())
                                .text(newName.trim());
                            
                            // Show success message
                            const successMsg = document.createElement('div');
                            successMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:#4CAF50;color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
                            successMsg.textContent = `Hostname updated successfully: ${data.data.oldName} → ${data.data.newName}`;
                            document.body.appendChild(successMsg);
                            setTimeout(() => successMsg.remove(), 5000);
                            
                            // Log additional details if available
                            if (data.data.doitOutput) {
                                console.log('doit.php output:', data.data.doitOutput);
                            }
                        } else {
                            // Show error message
                            const errorMsg = document.createElement('div');
                            errorMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:#F44336;color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
                            errorMsg.textContent = `Error: ${data.message}`;
                            document.body.appendChild(errorMsg);
                            setTimeout(() => errorMsg.remove(), 5000);
                        }
                    })
                    .catch(error => {
                        document.body.removeChild(loadingDiv);
                        console.error('Error updating hostname:', error);
                        
                        // Show error message
                        const errorMsg = document.createElement('div');
                        errorMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:#F44336;color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
                        errorMsg.textContent = 'Error updating hostname. Please try again.';
                        document.body.appendChild(errorMsg);
                        setTimeout(() => errorMsg.remove(), 5000);
                    });
                }
                contextMenu.style.display = 'none';
            } else if (event.target.id === 'view' && bubbleData) {
                event.preventDefault();
                (async () => {
                    const {
                        ip,
                        subnet,
                        hostname
                    } = bubbleData;
                    const realHostName = (await getHostnameByIP(ip)) ?? hostname;
                    const isAllHostsView = urlParams.get('subnet') === 'all' && urlParams.get('subnetNickname') === 'All Hosts';
                    const viewParam = isAllHostsView ? '' : '&subnet=true';
                    showModal(`host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(realHostName)}&infra=${encodeURIComponent(urlParams.get('infra'))}&hostip=${encodeURIComponent(ip)}&subnet=${subnet}${viewParam}`);
                    contextMenu.style.display = 'none';
                })();
            } else if (event.target.id === 'delete') {
                // Handle single or bulk removal (called "Delete Host" in UI)
                const selectedHosts = window.selectedHosts || new Set();
                const hasMultipleSelected = selectedHosts.size > 1;
                if (hasMultipleSelected) {
                    // Gather selected hosts' data
                    const items = [];
                    d3.selectAll('.host-bubble').each(function(d) {
                        if (selectedHosts.has(d.id)) {
                            items.push({ ip: d.ip, currentBubble: this, bubbleData: d });
                        }
                    });
                    if (items.length > 0) {
                        bulkBlacklistHosts(items);
                    }
                    contextMenu.style.display = 'none';
                } else if (bubbleData) {
                    blacklistHost(bubbleData.ip, currentBubble, bubbleData);
                    contextMenu.style.display = 'none';
                } else {
                    contextMenu.style.display = 'none';
                }
            } else if (event.target.id === 'show-links' && bubbleData) {
                const linkedIds = new Set([bubbleData.id]);
                const linkedIps = new Set([bubbleData.ip]);

                d3.selectAll('.parent-child-arrow').each(function () {
                    const arrowData = d3.select(this).datum();
                    if (arrowData.parentId === bubbleData.id) {
                        linkedIps.add(arrowData.childIp);
                    } else if (arrowData.childIp === bubbleData.ip) {
                        linkedIds.add(arrowData.parentId);
                    }
                });

                d3.selectAll('.host-bubble').each(function (d) {
                    const shouldDisplay = linkedIds.has(d.id) || linkedIps.has(d.ip);
                    d3.select(this).style('display', shouldDisplay ? 'block' : 'none');
                    d3.selectAll('.bubble-text')
                        .filter(text => text.id === d.id)
                        .style('display', shouldDisplay ? 'block' : 'none');
                    d3.select(`.badge-${d.id}`).style('display', shouldDisplay ? 'block' : 'none');
                });

                d3.selectAll('.parent-child-arrow').each(function () {
                    const arrowData = d3.select(this).datum();
                    const shouldDisplay = linkedIds.has(arrowData.parentId) && linkedIps.has(arrowData.childIp);
                    d3.select(this).style('display', shouldDisplay ? 'block' : 'none');
                });

                d3.selectAll('.speed-label').each(function () {
                    const arrowData = d3.select(this).datum();
                    const shouldDisplay = linkedIds.has(arrowData.parentId) && linkedIps.has(arrowData.childIp);
                    d3.select(this).style('display', shouldDisplay ? 'block' : 'none');
                });

                d3.selectAll('.group-bubble').each(function (groupData) {
                    const groupBubble = d3.select(this);
                    const hostgroup = groupData.hostgroup;
                    
                    // Always hide group bubble for "No Hostgroup" or "unknown"
                    if (hostgroup === "No Hostgroup" || hostgroup.toLowerCase() === "unknown") {
                        groupBubble.style('display', 'none');
                        d3.selectAll('.group-text')
                            .filter(d => d.hostgroup === hostgroup)
                            .style('display', 'none');
                        return;
                    }

                    const groupClassName = hostgroup.toLowerCase().replace(/\s+/g, '-');
                    const allHostBubbles = d3.selectAll('.host-bubble');
                    let totalHostBubbles = 0;
                    let visibleHostBubbles = 0;

                    allHostBubbles.each(function (hostData) {
                        if (hostData.hostgroups && hostData.hostgroups[0].toLowerCase().replace(/\s+/g, '-') === groupClassName) {
                            totalHostBubbles++;
                            if (d3.select(this).style('display') !== 'none') {
                                visibleHostBubbles++;
                            }
                        }
                    });

                    const shouldHideGroup = totalHostBubbles > 0 && visibleHostBubbles === 0;
                    groupBubble.style('display', shouldHideGroup ? 'none' : 'block');
                    d3.selectAll('.group-text')
                        .filter(d => d.hostgroup === hostgroup)
                        .style('display', shouldHideGroup ? 'none' : 'block');
                });

                contextMenu.style.display = 'none';
            } else if (event.target.id === 'parent-options' && bubbleData) {
                event.preventDefault();
                (async () => {
                    const { ip, hostname } = bubbleData;
                    const realHostName = (await getHostnameByIP(ip)) ?? hostname;
                    const infra = encodeURIComponent(urlParams.get('infra') || '');
                    showModal(`checkCommandHost.php?hostname=${encodeURIComponent(realHostName)}&infra=${infra}&section=parents`);
                    contextMenu.style.display = 'none';
                })();
            } else if (event.target.id === 'move-to') {
                showHostGroupModal();
                contextMenu.style.display = 'none';
            } else if (event.target.id === 'clear-selection') {
                // Clear the multi-selection
                if (window.clearHostSelection) {
                    window.clearHostSelection();
                }
                contextMenu.style.display = 'none';
            } else if (!isClickInsideMenu) {
                contextMenu.style.display = 'none';
            }
        } else {
            contextMenu.style.display = 'none';
        }
    });

    // Helper function to position the context menu
    const positionContextMenu = (event, menu) => {
        // Show the menu temporarily to get accurate dimensions
        menu.style.display = 'block';

        // Use actual dimensions, with fallbacks
        const menuHeight = menu.offsetHeight || 200;
        const menuWidth = menu.offsetWidth || 150;
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        const buffer = 10; // Small buffer from viewport edges

        let top = event.clientY;
        let left = event.clientX;

        // Adjust if menu would go beyond bottom edge
        if (top + menuHeight + buffer > viewportHeight) {
            top = event.clientY - menuHeight; // Flip above the click point
        }
        // Adjust if menu would go beyond right edge
        if (left + menuWidth + buffer > viewportWidth) {
            left = event.clientX - menuWidth; // Flip left of the click point
        }

        // Ensure it doesn't go off the top or left edge
        top = Math.max(buffer, top); // Keep a small buffer from top
        left = Math.max(buffer, left); // Keep a small buffer from left

        menu.style.top = `${top}px`;
        menu.style.left = `${left}px`;
    };
};

// Compact host group selection modal
const showHostGroupModal = async () => {
    try {
        const hostGroups = await fetchAllHostGroups();
        
        // Get context information
        let contextInfo = '';
        const selectedHosts = window.selectedHosts || new Set();
        const hasMultipleSelected = selectedHosts.size > 1;
        
        if (hasMultipleSelected) {
            contextInfo = `Moving ${selectedHosts.size} selected host(s)`;
        } else if (currentBubble) {
            const bubbleData = d3.select(currentBubble).datum();
            const hostname = bubbleData.hostname;
            const ip = bubbleData.ip;
            contextInfo = `Moving host: ${hostname}${ip !== hostname ? ` (${ip})` : ''}`;
        }
        
        // Create modal HTML
        const modalHTML = `
            <div id="hostgroup-modal" class="formModal show">
                <div class="formModal-content" style="max-width: 400px; max-height: 500px;">
                    <span class="formModal-close" onclick="closeHostGroupModal()">&times;</span>
                    <h3 style="margin: 0 0 15px 0;">Select Host Group</h3>
                    ${contextInfo ? `<div class="context-info">${contextInfo}</div>` : ''}
                    <div style="display: flex; gap: 8px; margin-bottom: 15px;">
                        <input type="text" id="hostgroup-search" placeholder="Search host groups..." style="flex: 1; padding: 8px 12px; border: 1px solid rgba(255,255,255,0.2); border-radius: 4px; background: rgba(255,255,255,0.1); color: inherit;">
                        <button id="add-hostgroup-btn" style="padding: 8px 12px; border: 1px solid rgba(255,255,255,0.2); background: rgba(255,255,255,0.1); color: inherit; border-radius: 4px; cursor: pointer;">+</button>
                    </div>
                    <div id="hostgroup-list" style="max-height: 300px; overflow-y: auto; border: 1px solid rgba(255,255,255,0.1); border-radius: 4px; background: rgba(255,255,255,0.05);"></div>
                </div>
            </div>
        `;
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Setup event listeners
        const modal = document.getElementById('hostgroup-modal');
        const searchInput = document.getElementById('hostgroup-search');
        const addBtn = document.getElementById('add-hostgroup-btn');
        const listContainer = document.getElementById('hostgroup-list');
        
        // Render host groups
        const renderGroups = (filter = '') => {
            const filteredGroups = hostGroups.filter(group => {
                const [, alias] = group.split('. ', 2);
                return alias.toLowerCase().includes(filter.toLowerCase());
            });
            
            listContainer.innerHTML = filteredGroups.length ? 
                filteredGroups.map(group => {
                    const [id, alias] = group.split('. ', 2);
                    return `<div class="hostgroup-item" data-id="${id}" style="padding: 8px 12px; cursor: pointer; border-bottom: 1px solid rgba(255,255,255,0.1); transition: background 0.2s;" onmouseover="this.style.background='rgba(255,255,255,0.1)'" onmouseout="this.style.background='transparent'">${alias}</div>`;
                }).join('') :
                '<div style="padding: 8px 12px; color: rgba(255,255,255,0.6);">No host groups found</div>';
        };
        
        // Search functionality
        searchInput.addEventListener('input', (e) => renderGroups(e.target.value));
        
        // Add hostgroup functionality
        addBtn.addEventListener('click', async () => {
            const name = prompt('Enter new hostgroup name:');
            if (name) {
                try {
                    const response = await fetch('add_hostgroup.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `hostgroup_name=${encodeURIComponent(name)}&alias=${encodeURIComponent(name)}&config_id=1`
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        // Refresh the host groups list instead of reloading the page
                        const newHostGroups = await fetchAllHostGroups();
                        hostGroups.length = 0; // Clear the array
                        hostGroups.push(...newHostGroups); // Add new data
                        renderGroups(searchInput.value); // Re-render with current filter
                        
                        // Show success message
                        const successMsg = document.createElement('div');
                        successMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:#4CAF50;color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
                        successMsg.textContent = `Hostgroup "${name}" added successfully!`;
                        document.body.appendChild(successMsg);
                        setTimeout(() => successMsg.remove(), 3000);
                    } else {
                        alert(`Error: ${data.message}`);
                    }
                } catch (error) {
                    console.error('Error adding hostgroup:', error);
                    alert('Error adding hostgroup');
                }
            }
        });
        
        // Host group selection
        listContainer.addEventListener('click', async (e) => {
            if (e.target.classList.contains('hostgroup-item')) {
                const hostGroupId = e.target.dataset.id;
                const hostGroupName = e.target.textContent;
                
                // Check for multi-select
                const selectedHosts = window.selectedHosts || new Set();
                const hasMultipleSelected = selectedHosts.size > 1;
                
                if (hasMultipleSelected) {
                    const confirmed = confirm(`Move ${selectedHosts.size} selected host(s) to hostgroup "${hostGroupName}"?`);
                    if (!confirmed) return;
                    
                    const selectedHostsData = [];
                    d3.selectAll('.host-bubble').each(function(d) {
                        if (selectedHosts.has(d.id)) selectedHostsData.push(d);
                    });
                    
                    const ips = selectedHostsData.map(h => h.ip);
                    await moveHostsToGroup(ips, hostGroupId);
                } else if (currentBubble) {
                    const bubbleData = d3.select(currentBubble).datum();
                    await moveHostsToGroup([bubbleData.ip], hostGroupId);
                }
                
                closeHostGroupModal();
            }
        });
        
        // Close on outside click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeHostGroupModal();
        });
        
        // Initial render
        renderGroups();
        searchInput.focus();
        
    } catch (error) {
        console.error('Error showing host group modal:', error);
        alert('Error loading host groups');
    }
};

// Close host group modal
const closeHostGroupModal = () => {
    const modal = document.getElementById('hostgroup-modal');
    if (modal) modal.remove();
};

// Move hosts to group helper
const moveHostsToGroup = async (ips, hostGroupId) => {
    const loadingDiv = document.createElement('div');
    loadingDiv.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.8);color:white;padding:20px;border-radius:8px;z-index:10000;';
    loadingDiv.innerHTML = `Moving host(s)... <div style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:30px;height:30px;animation:spin 1s linear infinite;margin:10px auto;"></div>`;
    document.body.appendChild(loadingDiv);
    
    try {
        const response = await fetch('move_host_to_group.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `ips=${encodeURIComponent(JSON.stringify(ips))}&hostGroupId=${encodeURIComponent(hostGroupId)}`
        });
        
        const result = await response.json();
        if (result.success) {
            if (result.details?.errorCount > 0) {
                alert(`${result.message}\n\nErrors:\n${result.details.errors.join('\n')}`);
            }
            window.location.reload();
        } else {
            alert(`Error: ${result.message}`);
        }
    } catch (error) {
        console.error('Error moving hosts:', error);
        alert('An error occurred while moving the hosts');
    } finally {
        document.body.removeChild(loadingDiv);
    }
};

// Helper function to rename a hostgroup
async function renameHostgroup(oldNameEncoded) {
    try {
        const oldName = decodeURIComponent(oldNameEncoded);
        const newName = prompt('Enter a new name for hostgroup:', oldName);
        if (!newName || newName.trim() === '' || newName.trim() === oldName) return;

        // Show loading overlay
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loading-animation-rename';
        loadingDiv.style.position = 'fixed';
        loadingDiv.style.top = '50%';
        loadingDiv.style.left = '50%';
        loadingDiv.style.transform = 'translate(-50%, -50%)';
        loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        loadingDiv.style.color = 'white';
        loadingDiv.style.padding = '20px';
        loadingDiv.style.borderRadius = '8px';
        loadingDiv.style.zIndex = '1000';
        loadingDiv.innerHTML = 'Renaming hostgroup... <div class="loader" style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 10px auto;"></div>';

        // Add spin animation style
        const style = document.createElement('style');
        style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
        document.head.appendChild(style);
        document.body.appendChild(loadingDiv);

        const response = await fetch('rename_hostgroup.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `oldName=${encodeURIComponent(oldName)}&newName=${encodeURIComponent(newName.trim())}`
        });
        const result = await response.json();
        if (result.success) {
            window.location.reload();
        } else {
            alert(`Error: ${result.message}`);
        }
    } catch (error) {
        console.error('Error renaming hostgroup:', error);
        alert('An unexpected error occurred while renaming the hostgroup');
    } finally {
        const loadingDiv = document.getElementById('loading-animation-rename');
        if (loadingDiv) document.body.removeChild(loadingDiv);
        const spinStyle = Array.from(document.head.querySelectorAll('style')).find(s => s.textContent.includes('@keyframes spin'));
        if (spinStyle) document.head.removeChild(spinStyle);
    }
}

// Helper function to add a new hostgroup
async function addNewHostgroup() {
    try {
        const name = prompt('Enter new hostgroup name:');
        if (!name || name.trim() === '') return;

        const response = await fetch('add_hostgroup.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `hostgroup_name=${encodeURIComponent(name.trim())}&alias=${encodeURIComponent(name.trim())}&config_id=1`
        });
        
        const data = await response.json();
        if (data.success) {
            // Show success message
            const successMsg = document.createElement('div');
            successMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:#4CAF50;color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
            successMsg.textContent = `Hostgroup "${name}" added successfully!`;
            document.body.appendChild(successMsg);
            setTimeout(() => successMsg.remove(), 3000);
            
            // Close the context menu
            const contextMenu = document.getElementById('context-menu');
            if (contextMenu) {
                contextMenu.style.display = 'none';
            }
        } else {
            alert(`Error: ${data.message}`);
        }
    } catch (error) {
        console.error('Error adding hostgroup:', error);
        alert('Error adding hostgroup');
    }
}