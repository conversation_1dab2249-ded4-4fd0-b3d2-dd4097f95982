// Global variable to store the search interval
let searchInterval = null;

function filterBubbles() {
    const searchValue = document.getElementById('searchBar').value.toLowerCase();
    const searchMode = document.getElementById('searchMode').value;

    // Clear any existing search interval
    if (searchInterval) {
        clearInterval(searchInterval);
    }

    // Run initial search
    performSearch(searchValue, searchMode);

    // Set up continuous search interval
    searchInterval = setInterval(() => {
        const currentSearchValue = document.getElementById('searchBar').value.toLowerCase();
        const currentSearchMode = document.getElementById('searchMode').value;
        performSearch(currentSearchValue, currentSearchMode);
    }, 1);
}

// Separate function to perform the actual search
function performSearch(searchValue, searchMode) {
    // Check if search is for exact match (wrapped in quotes)
    const isExactMatch = (searchValue.startsWith('"') && searchValue.endsWith('"') || searchValue.startsWith("'") && searchValue.endsWith("'")) && searchValue.length > 2;
    // Extract the term inside quotes if it's an exact match search
    const exactTerm = isExactMatch ? searchValue.substring(1, searchValue.length - 1).toLowerCase() : '';
    // Use the appropriate search value based on whether it's an exact match
    const termToSearch = isExactMatch ? exactTerm : searchValue;

    if (searchMode === 'hosts') {
        // Search by Hosts
        d3.selectAll('.host-bubble').each(function(d) {
            let visibleByHostname, visibleByIp;
            
            if (isExactMatch) {
                // For exact match, compare the whole string
                visibleByHostname = d.hostname.toLowerCase() === exactTerm;
                visibleByIp = d.ip && d.ip.toLowerCase() === exactTerm;
            } else {
                // For partial match, use includes
                visibleByHostname = d.hostname.toLowerCase().includes(termToSearch);
                visibleByIp = d.ip && d.ip.toLowerCase().includes(termToSearch);
            }
            
            const visible = visibleByHostname || visibleByIp;
            
            d3.select(this).style('display', visible ? 'block' : 'none');
            d3.select(this.parentNode)
                .select(`.badge-${d.id}`)
                .style('display', visible ? 'block' : 'none');
        });

        d3.selectAll('.bubble-text').each(function(d) {
            let visibleByHostname, visibleByIp;
            
            if (isExactMatch) {
                // For exact match, compare the whole string
                visibleByHostname = d.hostname.toLowerCase() === exactTerm;
                visibleByIp = d.ip && d.ip.toLowerCase() === exactTerm;
            } else {
                // For partial match, use includes
                visibleByHostname = d.hostname.toLowerCase().includes(termToSearch);
                visibleByIp = d.ip && d.ip.toLowerCase().includes(termToSearch);
            }
            
            const visible = visibleByHostname || visibleByIp;
            
            d3.select(this).style('display', visible ? 'block' : 'none');
        });

        // Update group bubbles display based on their child hosts
        d3.selectAll('.group-bubble').each(function(groupData) {            
            const groupBubble = d3.select(this);
            const hostgroup = groupData.hostgroup;
            
            // Skip showing group bubble for "No Hostgroup" or "unknown"
            if (hostgroup === "No Hostgroup" || hostgroup.toLowerCase() === "unknown") {
                groupBubble.style('display', 'none');
                d3.selectAll('.group-text')
                    .filter(d => d.hostgroup === hostgroup)
                    .style('display', 'none');
                return;
            }

            const groupClassName = hostgroup.toLowerCase().replace(/\s+/g, '-');
            const allHostBubbles = d3.selectAll('.host-bubble');
            let totalHostBubbles = 0;
            let visibleHostBubbles = 0;

            allHostBubbles.each(function(hostData) {
                if (hostData.hostgroups && hostData.hostgroups[0].toLowerCase().replace(/\s+/g, '-') === groupClassName) {
                    totalHostBubbles++;
                    if (d3.select(this).style('display') !== 'none') {
                        visibleHostBubbles++;
                    }
                }
            });

            const shouldHideGroup = totalHostBubbles > 0 && visibleHostBubbles === 0;
            groupBubble.style('display', shouldHideGroup ? 'none' : 'block');
            d3.selectAll('.group-text')
                .filter(d => d.hostgroup === hostgroup)
                .style('display', shouldHideGroup ? 'none' : 'block');
        });
    } else if (searchMode === 'hostgroups') {
        // Search by Hostgroups
        d3.selectAll('.group-bubble').each(function(groupData) {
            const groupBubble = d3.select(this);
            const hostgroup = groupData.hostgroup;
            
            // Completely exclude "No Hostgroup" and "unknown" from hostgroups search
            if (hostgroup === "No Hostgroup" || hostgroup.toLowerCase() === "unknown") {
                groupBubble.style('display', 'none');
                d3.selectAll('.group-text')
                    .filter(d => d.hostgroup === hostgroup)
                    .style('display', 'none');
                
                // Hide all hosts that belong to these groups
                const groupClassName = hostgroup.toLowerCase().replace(/\s+/g, '-');
                d3.selectAll('.host-bubble').each(function(hostData) {
                    if (hostData.hostgroups && hostData.hostgroups[0].toLowerCase().replace(/\s+/g, '-') === groupClassName) {
                        d3.select(this).style('display', 'none');
                        d3.select(this.parentNode)
                            .select(`.badge-${hostData.id}`)
                            .style('display', 'none');
                    }
                });

                d3.selectAll('.bubble-text').each(function(hostData) {
                    if (hostData.hostgroups && hostData.hostgroups[0].toLowerCase().replace(/\s+/g, '-') === groupClassName) {
                        d3.select(this).style('display', 'none');
                    }
                });
                return;
            }

            // Only show groups that match search term (if there is one)
            let visible;
            
            if (isExactMatch) {
                visible = termToSearch ? hostgroup.toLowerCase() === exactTerm : true;
            } else {
                visible = termToSearch ? hostgroup.toLowerCase().includes(termToSearch) : true;
            }
            
            groupBubble.style('display', visible ? 'block' : 'none');
            d3.selectAll('.group-text')
                .filter(d => d.hostgroup === hostgroup)
                .style('display', visible ? 'block' : 'none');

            const groupClassName = hostgroup.toLowerCase().replace(/\s+/g, '-');
            d3.selectAll('.host-bubble').each(function(hostData) {
                if (hostData.hostgroups && hostData.hostgroups[0].toLowerCase().replace(/\s+/g, '-') === groupClassName) {
                    d3.select(this).style('display', visible ? 'block' : 'none');
                    d3.select(this.parentNode)
                        .select(`.badge-${hostData.id}`)
                        .style('display', visible ? 'block' : 'none');
                }
            });

            d3.selectAll('.bubble-text').each(function(hostData) {
                if (hostData.hostgroups && hostData.hostgroups[0].toLowerCase().replace(/\s+/g, '-') === groupClassName) {
                    d3.select(this).style('display', visible ? 'block' : 'none');
                }
            });
        });
    }

    // Filter arrows: show only if both parent and child are visible
    d3.selectAll('.parent-child-arrow').each(function() {
        const arrowData = d3.select(this).datum(); // Get arrow data (parentId, childIp)
        const parentBubble = d3.selectAll('.host-bubble').filter(d => d.id === arrowData.parentId).node();
        const childBubble = d3.selectAll('.host-bubble').filter(d => d.ip === arrowData.childIp).node();

        // Check if both parent and child bubbles are visible
        const parentVisible = parentBubble && d3.select(parentBubble).style('display') !== 'none';
        const childVisible = childBubble && d3.select(childBubble).style('display') !== 'none';
        const shouldDisplay = parentVisible && childVisible;

        d3.select(this).style('display', shouldDisplay ? 'block' : 'none');
    });

    // Filter speed labels: show only if both parent and child are visible
    d3.selectAll('.speed-label').each(function() {
        const arrowData = d3.select(this).datum(); // Get arrow data (parentId, childIp)
        const parentBubble = d3.selectAll('.host-bubble').filter(d => d.id === arrowData.parentId).node();
        const childBubble = d3.selectAll('.host-bubble').filter(d => d.ip === arrowData.childIp).node();

        // Check if both parent and child bubbles are visible
        const parentVisible = parentBubble && d3.select(parentBubble).style('display') !== 'none';
        const childVisible = childBubble && d3.select(childBubble).style('display') !== 'none';
        const shouldDisplay = parentVisible && childVisible;

        d3.select(this).style('display', shouldDisplay ? 'block' : 'none');
    });

    // Force a simulation tick to update arrow positions immediately
    if (window.simulation) {
        window.simulation.alpha(0.1).restart();
    }
}

// Need to add event listeners for clearing the search interval
document.addEventListener('DOMContentLoaded', () => {
    // Add event listener to stop the search interval when the search is cleared
    const searchBar = document.getElementById('searchBar');
    if (searchBar) {
        searchBar.addEventListener('input', () => {
            if (searchBar.value === '') {
                // Clear any existing search interval
                if (searchInterval) {
                    clearInterval(searchInterval);
                    searchInterval = null;
                }
                // Call resetStatusFilters when search is cleared, but preserve search mode
                if (typeof resetStatusFilters === 'function') {
                    resetStatusFilters(true); // Preserve search mode
                }
            }
        });
    }

    // Also clear interval when switching search modes
    const searchMode = document.getElementById('searchMode');
    if (searchMode) {
        searchMode.addEventListener('change', () => {
            // Clear any existing search interval
            if (searchInterval) {
                clearInterval(searchInterval);
                searchInterval = null;
            }
            
            // Check if search bar is empty
            const searchBar = document.getElementById('searchBar');
            if (searchBar && searchBar.value === '') {
                // When switching modes with empty search, just reset status filters without changing search mode
                if (typeof resetStatusFilters === 'function') {
                    resetStatusFilters(true); // Preserve search mode
                }
            } else {
                // Only run filterBubbles if there's a search term
                filterBubbles();
            }
        });
    }
});