(function() {
    // Initialize UI elements
    async function initializeUI() {
        // Set default time range (yesterday 00:00 to now)
        const now = new Date();
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        yesterday.setHours(0, 0, 0, 0); // Set to 00:00:00
        
        const startInput = document.getElementById('notification-start');
        const endInput = document.getElementById('notification-end');
        
        if (startInput && endInput) {
            startInput.value = window.notificationsCore.toLocalIso(yesterday);
            endInput.value = window.notificationsCore.toLocalIso(now);
        }
        
        // Set up event listeners
        setupEventListeners();
        
        // Wait for everything to be ready before generating initial notifications
        await waitForReady();
        
        // Generate initial notifications automatically
        if (window.notificationsCore && window.notificationsCore.generateNotifications) {
            window.notificationsCore.generateNotifications();
        }
    }

    // Wait for all necessary elements and modules to be ready
    function waitForReady() {
        return new Promise((resolve) => {
            const checkReady = () => {
                // Check if all required elements exist
                const requiredElements = [
                    'notification-start',
                    'notification-end', 
                    'notification-type-filter',
                    'notifications-content'
                ];
                
                const allElementsExist = requiredElements.every(id => 
                    document.getElementById(id) !== null
                );
                
                // Check if date inputs have valid values
                const startInput = document.getElementById('notification-start');
                const endInput = document.getElementById('notification-end');
                const datesValid = startInput && endInput && 
                                 startInput.value && endInput.value &&
                                 startInput.value.trim() !== '' && endInput.value.trim() !== '';
                
                // Check if required modules are loaded
                const modulesReady = window.notificationsCore && 
                                   window.notificationsCore.generateNotifications &&
                                   window.notificationsRenderer &&
                                   window.notificationsFilters;
                
                if (allElementsExist && datesValid && modulesReady) {
                    resolve();
                } else {
                    // Check again in a short interval
                    setTimeout(checkReady, 50);
                }
            };
            
            // Start checking
            checkReady();
        });
    }

    // Set up event listeners
    function setupEventListeners() {
        // Search input with debouncing
        const searchInput = document.getElementById('notification-search');
        const searchClearBtn = document.getElementById('notification-search-clear');
        
        if (searchInput) {
            // Enter key on search input
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    window.notificationsCore.generateNotifications();
                }
            });
            
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    window.notificationsCore.generateNotifications();
                }, 300); // 300ms delay for better performance
                
                // Show/hide clear button based on input value
                if (searchClearBtn) {
                    if (searchInput.value.trim() !== '') {
                        searchClearBtn.classList.add('visible');
                    } else {
                        searchClearBtn.classList.remove('visible');
                    }
                }
            });
            
            // Clear button functionality
            if (searchClearBtn) {
                searchClearBtn.addEventListener('click', () => {
                    searchInput.value = '';
                    searchInput.focus();
                    searchClearBtn.classList.remove('visible');
                    window.notificationsCore.generateNotifications();
                });
            }
            
            // Initial visibility check
            if (searchClearBtn) {
                if (searchInput.value.trim() !== '') {
                    searchClearBtn.classList.add('visible');
                } else {
                    searchClearBtn.classList.remove('visible');
                }
            }
        }

        // Notification type filter
        const typeFilter = document.getElementById('notification-type-filter');
        if (typeFilter) {
            typeFilter.addEventListener('change', () => {
                window.notificationsCore.generateNotifications();
            });
        }
    }

    // Show loading state
    function showLoading() {
        const content = document.getElementById('notifications-content');
        if (content) {
            content.innerHTML = '<div class="notifications-loading"><i class="fa fa-spinner fa-spin"></i> Loading...</div>';
        }
    }

    // Show error state
    function showError(message) {
        const content = document.getElementById('notifications-content');
        if (content) {
            content.innerHTML = `
                <div class="notifications-error">
                    <i class="fa fa-exclamation-triangle"></i>
                    <div>${message}</div>
                </div>
            `;
        }
    }

    // Show empty state
    function showEmpty(timeRangeText = '') {
        const content = document.getElementById('notifications-content');
        if (content) {
            content.innerHTML = `
                <div class="notifications-empty">
                    <i class="fa fa-bell-slash"></i>
                    <div>No notifications found for the selected criteria</div>
                    ${timeRangeText ? `<div class="empty-time-range">${timeRangeText}</div>` : ''}
                </div>
            `;
        }
    }

    // Getter methods for external access
    function getStartInput() {
        return document.getElementById('notification-start');
    }

    function getEndInput() {
        return document.getElementById('notification-end');
    }

    function getTypeFilter() {
        const filter = document.getElementById('notification-type-filter');
        return filter ? filter.value : 'all';
    }

    function getSearchTerm() {
        const searchInput = document.getElementById('notification-search');
        return searchInput ? searchInput.value : '';
    }

    // Export to global scope
    window.notificationsUI = {
        initializeUI,
        showLoading,
        showError,
        showEmpty,
        getStartInput,
        getEndInput,
        getTypeFilter,
        getSearchTerm
    };
})();
