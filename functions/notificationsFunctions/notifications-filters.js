(function() {
    // Get filtered notifications
    function getFilteredNotifications() {
        const visibleRows = document.querySelectorAll('.notification-table tbody tr');
        const notifications = [];
        
        visibleRows.forEach(row => {
            const host = row.querySelector('td:nth-child(1)')?.textContent || '';
            const service = row.querySelector('td:nth-child(2)')?.textContent || '';
            const type = row.querySelector('td:nth-child(3) span')?.textContent || '';
            const timestamp = row.querySelector('td:nth-child(4)')?.textContent || '';
            const contact = row.querySelector('td:nth-child(5)')?.textContent || '';
            const message = row.querySelector('td:nth-child(6)')?.textContent || '';
            
            notifications.push({
                host,
                service,
                type,
                timestamp,
                contact,
                message
            });
        });
        
        return notifications;
    }

    // Get search term from UI
    function getCurrentSearchTerm() {
        return window.notificationsUI ? window.notificationsUI.getSearchTerm() : '';
    }

    // Export functions to global scope
    window.notificationsFilters = {
        getFilteredNotifications,
        getCurrentSearchTerm
    };
})();
