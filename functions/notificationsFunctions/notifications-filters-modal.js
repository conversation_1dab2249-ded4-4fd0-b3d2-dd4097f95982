(function() {
    // Filters modal functionality
    let filtersModal, filtersForm, filtersCancel;
    let filterStart, filterEnd, filterType;

    // Initialize elements
    function initializeElements() {
        filtersModal = document.getElementById('filtersModal');
        filtersForm = document.getElementById('filters-form');
        filtersCancel = document.getElementById('filters-cancel');
        
        filterStart = document.getElementById('filter-start');
        filterEnd = document.getElementById('filter-end');
        filterType = document.getElementById('filter-type');
    }

    // Open filters modal
    function openFiltersModal() {
        if (!filtersModal) {
            console.error('Filters modal not found');
            return;
        }
        // Sync current values to modal
        syncCurrentValuesToModal();

        // Show modal
        filtersModal.style.display = 'flex';

        // Update time shortcut active state
        updateTimeShortcutActiveState();
    }

    // Sync current filter values to modal inputs
    function syncCurrentValuesToModal() {
        const notificationStart = document.getElementById('notification-start');
        const notificationEnd = document.getElementById('notification-end');
        const notificationTypeFilter = document.getElementById('notification-type-filter');

        if (notificationStart && filterStart) filterStart.value = notificationStart.value;
        if (notificationEnd && filterEnd) filterEnd.value = notificationEnd.value;
        if (notificationTypeFilter && filterType) filterType.value = notificationTypeFilter.value;
    }
    
    // Update time shortcut active state based on current values
    function updateTimeShortcutActiveState() {
        if (!filterStart || !filterEnd) return;
        
        const startDate = new Date(filterStart.value);
        const endDate = new Date(filterEnd.value);
        
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return;
        
        const diffMs = endDate.getTime() - startDate.getTime();
        const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
        
        // Clear all active states
        const shortcutButtons = document.querySelectorAll('.time-shortcut-btn');
        shortcutButtons.forEach(btn => btn.classList.remove('active'));
        
        // Set active state for matching shortcut
        shortcutButtons.forEach(btn => {
            const btnDays = parseInt(btn.dataset.days);
            if (btnDays === diffDays) {
                btn.classList.add('active');
            }
        });
    }

    // Apply filters
    function applyFilters() {
        const notificationStart = document.getElementById('notification-start');
        const notificationEnd = document.getElementById('notification-end');
        const notificationTypeFilter = document.getElementById('notification-type-filter');
        
        // Update main form values
        if (notificationStart && filterStart) notificationStart.value = filterStart.value;
        if (notificationEnd && filterEnd) notificationEnd.value = filterEnd.value;
        if (notificationTypeFilter && filterType) notificationTypeFilter.value = filterType.value;
        
        // Close modal
        filtersModal.style.display = 'none';

        // Generate notifications with new filters
        if (window.notificationsCore && window.notificationsCore.generateNotifications) {
            window.notificationsCore.generateNotifications();
        }
    }

    // Setup event listeners
    function setupEventListeners() {
        // Filters button
        const filtersBtn = document.getElementById('notification-filters');
        if (filtersBtn) {
            filtersBtn.addEventListener('click', openFiltersModal);
        }
        
        // Modal close handlers
        if (filtersCancel) {
            filtersCancel.addEventListener('click', () => {
                filtersModal.style.display = 'none';
            });
        }
        
        // Close modal when clicking outside
        if (filtersModal) {
            filtersModal.addEventListener('click', (e) => {
                if (e.target === filtersModal) {
                    filtersModal.style.display = 'none';
                }
            });
        }
        
        // Form submit
        if (filtersForm) {
            filtersForm.addEventListener('submit', (e) => {
                e.preventDefault();
                applyFilters();
            });
        }
        
        // Time shortcuts handlers
        setupTimeShortcuts();
    }
    
    // Setup time shortcuts functionality
    function setupTimeShortcuts() {
        const shortcutButtons = document.querySelectorAll('.time-shortcut-btn');
        
        shortcutButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const days = parseInt(btn.dataset.days);
                if (isNaN(days)) return;
                
                // Calculate start and end dates
                const endDate = new Date();
                const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));
                
                // Update the datetime inputs
                if (filterStart) {
                    filterStart.value = window.notificationsCore.toLocalIso(startDate);
                }
                if (filterEnd) {
                    filterEnd.value = window.notificationsCore.toLocalIso(endDate);
                }
                
                // Update active state
                shortcutButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
        
        // Also update active state when manually changing dates
        if (filterStart) {
            filterStart.addEventListener('change', updateTimeShortcutActiveState);
        }
        if (filterEnd) {
            filterEnd.addEventListener('change', updateTimeShortcutActiveState);
        }
    }

    // Initialize
    function init() {
        initializeElements();
        setupEventListeners();
    }

    // Export to global scope
    window.notificationsFiltersModal = {
        init,
        openFiltersModal
    };
})();
