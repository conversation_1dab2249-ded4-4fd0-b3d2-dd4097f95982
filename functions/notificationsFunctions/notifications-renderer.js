(function() {
    // Render notification table
    function renderTable(data, hostIpMap, timeRangeText = '') {
        const notifications = data?.data?.notificationlist || [];
        const content = document.getElementById('notifications-content');
        
        if (!content) return;

        if (!notifications.length) {
            window.notificationsUI.showEmpty(timeRangeText);
            return;
        }

        // Calculate summary statistics
        const summary = window.notificationsCore.calculateSummary(data);
        
        // Store host IP map for export
        window.lastNotificationData = {
            ...data,
            hostIpMap: hostIpMap
        };

        // Generate HTML
        const html = `
            ${renderSummary(summary, timeRangeText)}
            ${renderNotificationTable(notifications, hostIpMap)}
        `;

        content.innerHTML = html;
    }

    // Render summary section
    function renderSummary(summary, timeRangeText = '') {
        if (!summary) return '';

        return `
            <div class="notification-summary">
                <h3 class="summary-title">Notification Summary</h3>
                ${timeRangeText ? `<div class="summary-time-range">${timeRangeText}</div>` : ''}
                <div class="summary-stats">
                    <div class="summary-stat">
                        <span class="stat-label">Total Notifications</span>
                        <span class="stat-value">${summary.total}</span>
                    </div>
                    <div class="summary-stat">
                        <span class="stat-label">Host Notifications</span>
                        <span class="stat-value">${summary.host} (${summary.hostPct}%)</span>
                    </div>
                    <div class="summary-stat">
                        <span class="stat-label">Service Notifications</span>
                        <span class="stat-value">${summary.service} (${summary.servicePct}%)</span>
                    </div>
                    ${summary.critical > 0 ? `
                        <div class="summary-stat critical">
                            <span class="stat-label">Critical</span>
                            <span class="stat-value">${summary.critical} (${summary.criticalPct}%)</span>
                        </div>
                    ` : ''}
                    ${summary.warning > 0 ? `
                        <div class="summary-stat warning">
                            <span class="stat-label">Warning</span>
                            <span class="stat-value">${summary.warning} (${summary.warningPct}%)</span>
                        </div>
                    ` : ''}
                    ${summary.down > 0 ? `
                        <div class="summary-stat down">
                            <span class="stat-label">Host Down</span>
                            <span class="stat-value">${summary.down} (${summary.downPct}%)</span>
                        </div>
                    ` : ''}
                    ${summary.unreachable > 0 ? `
                        <div class="summary-stat unreachable">
                            <span class="stat-label">Host Unreachable</span>
                            <span class="stat-value">${summary.unreachable} (${summary.unreachablePct}%)</span>
                        </div>
                    ` : ''}
                    ${summary.unknown > 0 ? `
                        <div class="summary-stat unknown">
                            <span class="stat-label">Unknown</span>
                            <span class="stat-value">${summary.unknown} (${summary.unknownPct}%)</span>
                        </div>
                    ` : ''}
                    ${summary.recovery > 0 ? `
                        <div class="summary-stat">
                            <span class="stat-label">Recovery</span>
                            <span class="stat-value">${summary.recovery} (${summary.recoveryPct}%)</span>
                        </div>
                    ` : ''}
                    ${summary.custom > 0 ? `
                        <div class="summary-stat">
                            <span class="stat-label">Custom</span>
                            <span class="stat-value">${summary.custom} (${summary.customPct}%)</span>
                        </div>
                    ` : ''}
                    ${summary.ack > 0 ? `
                        <div class="summary-stat">
                            <span class="stat-label">Acknowledged</span>
                            <span class="stat-value">${summary.ack} (${summary.ackPct}%)</span>
                        </div>
                    ` : ''}
                    ${summary.flap > 0 ? `
                        <div class="summary-stat">
                            <span class="stat-label">Flap</span>
                            <span class="stat-value">${summary.flap} (${summary.flapPct}%)</span>
                        </div>
                    ` : ''}
                    ${summary.nodata > 0 ? `
                        <div class="summary-stat">
                            <span class="stat-label">No Data</span>
                            <span class="stat-value">${summary.nodata} (${summary.nodataPct}%)</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Render notification table
    function renderNotificationTable(notifications, hostIpMap) {
        return `
            <table class="notification-table">
                <thead>
                    <tr>
                        <th>Host</th>
                        <th>Service</th>
                        <th>Type</th>
                        <th>Time</th>
                        <th>Contact</th>
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody>
                    ${notifications.map(notification => renderNotificationRow(notification, hostIpMap)).join('')}
                </tbody>
            </table>
        `;
    }

    // Render individual notification row
    function renderNotificationRow(notification, hostIpMap) {
        const timestamp = window.notificationsCore.formatTimestamp(notification.timestamp);
        const notificationType = notification.notification_type || '';
        const objectType = notification.object_type || '';
        const host = notification.host_name || notification.name || '';
        const service = notification.description || '';
        const contact = notification.contact || '';
        const message = notification.message || '';
        
        // Get IP address from hostIpMap
        const hostIp = hostIpMap[host] || host;
        
        // Determine Bubble View nickname (if different)
        const bubbleName = window.bubbleHostnamesMap && window.bubbleHostnamesMap[hostIp];
        const nicknameToUse = bubbleName && bubbleName.trim() && bubbleName.toLowerCase() !== host.toLowerCase() ? bubbleName : host;
        
        // Determine message class based on notification type
        let messageClass = '';
        if (notificationType === 'critical' || notificationType === 'down') {
            messageClass = 'critical';
        } else if (notificationType === 'warning') {
            messageClass = 'warning';
        } else if (notificationType === 'recovery') {
            messageClass = 'ok';
        }

        // Determine notification type CSS class
        let typeClass = `notification-type-${objectType}`;
        if (notificationType === 'recovery') {
            typeClass += ' notification-type-recovery';
        } else if (notificationType === 'critical' || notificationType === 'down') {
            typeClass += ' notification-type-critical';
        } else if (notificationType === 'warning') {
            typeClass += ' notification-type-warning';
        }

        // Create clickable host cell - always clickable, opens host page directly
        const hostCell = `<td class="clickable-host" onclick="openHostInModal('${escapeHtml(host)}', '${escapeHtml(hostIp)}', 'External', false)">${escapeHtml(host)}</td>`;
        
        // Create clickable service cell (only for service notifications)
        const serviceCell = objectType === 'service' ? 
            `<td class="clickable-service" onclick="openServiceInModal('${escapeHtml(host)}', '${escapeHtml(hostIp)}', 'External', '${escapeHtml(service)}')">${escapeHtml(service)}</td>` :
            `<td>${escapeHtml(service)}</td>`;

        return `
            <tr data-status="${notificationType}" data-type="${objectType}">
                ${hostCell}
                ${serviceCell}
                <td><span class="${typeClass}">${notificationType}</span></td>
                <td class="notification-timestamp">${timestamp}</td>
                <td class="notification-contact">${escapeHtml(contact)}</td>
                <td class="notification-message ${messageClass}">${escapeHtml(message)}</td>
            </tr>
        `;
    }

    // Escape HTML to prevent XSS
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Export functions to global scope
    window.notificationsRenderer = {
        renderTable,
        renderSummary,
        renderNotificationTable,
        renderNotificationRow
    };
})();
