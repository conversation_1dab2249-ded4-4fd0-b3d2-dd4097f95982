(function() {
    // Map of IP address => Bubble View hostname (nickname)
    // Fetched once per page load from get_bubble_hostnames.php
    let bubbleHostnamesMap = null;

    // Helper: convert timestamp to formatted date string
    function formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString();
    }

    // Helper: local ISO string without seconds for <input type="datetime-local">
    function toLocalIso(dateObj) {
        const tzOffset = dateObj.getTimezoneOffset() * 60000;
        return new Date(dateObj.getTime() - tzOffset).toISOString().slice(0, 16);
    }

    // Helper: calculate time range display text
    function getTimeRangeText(startTs, endTs) {
        const startDate = new Date(startTs * 1000);
        const endDate = new Date(endTs * 1000);
        
        const formatDate = (date) => {
            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        };
        
        return `${formatDate(startDate)} - ${formatDate(endDate)}`;
    }

    // Helper: fetch Bubble View hostnames from backend database
    async function fetchBubbleHostnames() {
        if (bubbleHostnamesMap !== null) {
            // Already fetched (or attempted) – reuse cached value
            return bubbleHostnamesMap;
        }
        try {
            const response = await fetch('get_bubble_hostnames.php');
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            bubbleHostnamesMap = await response.json();
        } catch (error) {
            console.error('Error fetching Bubble View hostnames:', error);
            // Prevent further fetch attempts in case of repeated failures
            bubbleHostnamesMap = {};
        }
        return bubbleHostnamesMap;
    }

    // Helper: fetch hostname -> IP address map via Nagios objectjson API
    async function fetchHostIpMap(){
        try {
            const resp = await fetch('/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true', { credentials: 'include' });
            if(!resp.ok) throw new Error(resp.statusText);
            const js = await resp.json();
            const map = {};
            const hostlist = js?.data?.hostlist || {};
            Object.entries(hostlist).forEach(([hostname, obj])=>{
                if(obj && obj.address){
                    map[hostname] = obj.address;
                }
            });
            return map;
        } catch(err){
            console.warn('Failed to fetch host IP map', err);
            return {}; // graceful fallback
        }
    }

    // Helper function to calculate summary statistics
    function calculateSummary(data) {
        const notifications = data?.data?.notificationlist || [];
        if (!notifications.length) return null;
        
        let totalNotifications = notifications.length;
        let hostNotifications = 0;
        let serviceNotifications = 0;
        let criticalNotifications = 0;
        let warningNotifications = 0;
        let unknownNotifications = 0;
        let downNotifications = 0;
        let unreachableNotifications = 0;
        let recoveryNotifications = 0;
        let customNotifications = 0;
        let ackNotifications = 0;
        let flapNotifications = 0;
        let nodataNotifications = 0;
        
        notifications.forEach(notification => {
            // Count by object type
            if (notification.object_type === 'host') {
                hostNotifications++;
            } else if (notification.object_type === 'service') {
                serviceNotifications++;
            }
            
            // Count by notification type
            const notificationType = notification.notification_type;
            switch (notificationType) {
                case 'critical':
                    criticalNotifications++;
                    break;
                case 'warning':
                    warningNotifications++;
                    break;
                case 'unknown':
                    unknownNotifications++;
                    break;
                case 'down':
                    downNotifications++;
                    break;
                case 'unreachable':
                    unreachableNotifications++;
                    break;
                case 'recovery':
                    recoveryNotifications++;
                    break;
                case 'custom':
                case 'hostcustom':
                    customNotifications++;
                    break;
                case 'hostack':
                case 'serviceack':
                    ackNotifications++;
                    break;
                case 'hostflapstart':
                case 'hostflapstop':
                case 'serviceflapstart':
                case 'serviceflapstop':
                    flapNotifications++;
                    break;
                case 'nodata':
                    nodataNotifications++;
                    break;
            }
        });
        
        return {
            total: totalNotifications,
            host: hostNotifications,
            service: serviceNotifications,
            critical: criticalNotifications,
            warning: warningNotifications,
            unknown: unknownNotifications,
            down: downNotifications,
            unreachable: unreachableNotifications,
            recovery: recoveryNotifications,
            custom: customNotifications,
            ack: ackNotifications,
            flap: flapNotifications,
            nodata: nodataNotifications,
            hostPct: (hostNotifications / totalNotifications * 100).toFixed(1),
            servicePct: (serviceNotifications / totalNotifications * 100).toFixed(1),
            criticalPct: (criticalNotifications / totalNotifications * 100).toFixed(1),
            warningPct: (warningNotifications / totalNotifications * 100).toFixed(1),
            unknownPct: (unknownNotifications / totalNotifications * 100).toFixed(1),
            downPct: (downNotifications / totalNotifications * 100).toFixed(1),
            unreachablePct: (unreachableNotifications / totalNotifications * 100).toFixed(1),
            recoveryPct: (recoveryNotifications / totalNotifications * 100).toFixed(1),
            customPct: (customNotifications / totalNotifications * 100).toFixed(1),
            ackPct: (ackNotifications / totalNotifications * 100).toFixed(1),
            flapPct: (flapNotifications / totalNotifications * 100).toFixed(1),
            nodataPct: (nodataNotifications / totalNotifications * 100).toFixed(1)
        };
    }
    
    // Helper function to get status colors - same as table cell background colors
    function getStatusColor(status) {
        // Check if we're in dark theme
        const isDarkTheme = document.body.classList.contains('dark-theme') ||
                          getComputedStyle(document.body).backgroundColor.includes('rgb(26, 26, 26)');

        if (isDarkTheme) {
            // Dark theme - use 75% opacity
            switch(status) {
                case 'success':
                case 'ok':
                case 'up':
                    return 'rgba(76, 175, 80, 0.75)';
                case 'warning':
                    return 'rgba(255, 193, 7, 0.75)';
                case 'critical':
                case 'down':
                    return 'rgba(244, 67, 54, 0.75)';
                case 'unknown':
                case 'unreachable':
                    return 'rgba(100, 116, 139, 0.75)';
                default:
                    return 'rgba(100, 116, 139, 0.75)';
            }
        } else {
            // Light theme - use 15% opacity
            switch(status) {
                case 'success':
                case 'ok':
                case 'up':
                    return 'rgba(76, 175, 80, 0.15)';
                case 'warning':
                    return 'rgba(255, 193, 7, 0.15)';
                case 'critical':
                case 'down':
                    return 'rgba(244, 67, 54, 0.15)';
                case 'unknown':
                case 'unreachable':
                    return 'rgba(100, 116, 139, 0.15)';
                default:
                    return 'rgba(100, 116, 139, 0.15)';
            }
        }
    }

    // Helper function to determine notification status from notification_type
    function getNotificationStatus(notification) {
        return notification.notification_type || 'unknown';
    }

    // Helper function to get notification type label
    function getNotificationTypeLabel(objectType) {
        return objectType === 'host' ? 'Host' : 'Service';
    }

    // Helper function to get notification type class
    function getNotificationTypeClass(objectType) {
        return objectType === 'host' ? 'notification-type-host' : 'notification-type-service';
    }

    // Helper function to filter notifications based on type
    function filterNotificationsByType(notifications, filterType) {
        if (filterType === 'all') {
            return notifications;
        }

        return notifications.filter(notification => {
            const objectType = notification.object_type || '';
            const notificationType = notification.notification_type || '';

            switch (filterType) {
                case 'all-service':
                    return objectType === 'service';
                case 'all-host':
                    return objectType === 'host';
                case 'service-custom':
                    return objectType === 'service' && (notificationType === 'custom' || notificationType === 'servicecustom');
                case 'service-ack':
                    return objectType === 'service' && notificationType === 'serviceack';
                case 'service-warning':
                    return objectType === 'service' && notificationType === 'warning';
                case 'service-unknown':
                    return objectType === 'service' && notificationType === 'unknown';
                case 'service-critical':
                    return objectType === 'service' && notificationType === 'critical';
                case 'service-recovery':
                    return objectType === 'service' && notificationType === 'recovery';
                case 'service-flap':
                    return objectType === 'service' && (notificationType === 'serviceflapstart' || notificationType === 'serviceflapstop');
                case 'service-downtime':
                    return objectType === 'service' && notificationType === 'downtimestart';
                case 'host-custom':
                    return objectType === 'host' && (notificationType === 'custom' || notificationType === 'hostcustom');
                case 'host-ack':
                    return objectType === 'host' && notificationType === 'hostack';
                case 'host-down':
                    return objectType === 'host' && notificationType === 'down';
                case 'host-unreachable':
                    return objectType === 'host' && notificationType === 'unreachable';
                case 'host-recovery':
                    return objectType === 'host' && notificationType === 'recovery';
                case 'host-flap':
                    return objectType === 'host' && (notificationType === 'hostflapstart' || notificationType === 'hostflapstop');
                case 'host-downtime':
                    return objectType === 'host' && notificationType === 'downtimestart';
                default:
                    return true;
            }
        });
    }

    // Helper function to filter notifications based on search term
    function filterNotificationsBySearch(notifications, searchTerm) {
        if (!searchTerm || searchTerm.trim() === '') {
            return notifications;
        }

        const searchLower = searchTerm.toLowerCase().trim();
        
        return notifications.filter(notification => {
            const host = (notification.host_name || notification.name || '').toLowerCase();
            const service = (notification.description || '').toLowerCase();
            const message = (notification.message || '').toLowerCase();
            const contact = (notification.contact || '').toLowerCase();
            
            return host.includes(searchLower) || 
                   service.includes(searchLower) || 
                   message.includes(searchLower) || 
                   contact.includes(searchLower);
        });
    }

    // Main generator
    async function generateNotifications() {
        const startTs = Math.floor(new Date(window.notificationsUI.getStartInput().value).getTime() / 1000);
        const endTs   = Math.floor(new Date(window.notificationsUI.getEndInput().value).getTime() / 1000);
        const filterType = window.notificationsUI.getTypeFilter();
        const searchTerm = window.notificationsUI.getSearchTerm();
        
        if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
            alert('Invalid time range');
            return;
        }

        let url = `/nagios/cgi-bin/archivejson.cgi?query=notificationlist&formatoptions=enumerate&starttime=${startTs}&endtime=${endTs}`;

        // Loading indicator
        window.notificationsUI.showLoading();

        try {
            // Fetch notification data, host IP map, and bubble hostnames in parallel for efficiency
            const [resp, hostIpMap, bubbleHostnames] = await Promise.all([
                fetch(url, { credentials: 'include' }),
                fetchHostIpMap(),
                fetchBubbleHostnames()
            ]);
            
            if (!resp.ok) throw new Error(resp.statusText);
            const data = await resp.json();
            
            // Sort notifications from newest to oldest
            if (data.data && data.data.notificationlist) {
                data.data.notificationlist.sort((a, b) => b.timestamp - a.timestamp);
            }
            
            // Apply type filter
            if (data.data && data.data.notificationlist) {
                data.data.notificationlist = filterNotificationsByType(data.data.notificationlist, filterType);
            }
            
            // Apply search filter
            if (data.data && data.data.notificationlist) {
                data.data.notificationlist = filterNotificationsBySearch(data.data.notificationlist, searchTerm);
            }
            
            // Store the original data for export
            window.lastNotificationData = data;
            
            // Make bubble hostnames available globally
            window.bubbleHostnamesMap = bubbleHostnames;
            
            // Calculate time range text
            const timeRangeText = getTimeRangeText(startTs, endTs);
            
            window.notificationsRenderer.renderTable(data, hostIpMap, timeRangeText);
        } catch (err) {
            console.error('Notifications fetch error', err);
            window.notificationsUI.showError('Error fetching notification data');
        }
    }

    // Export functions to global scope
    window.notificationsCore = {
        formatTimestamp,
        toLocalIso,
        getTimeRangeText,
        fetchBubbleHostnames,
        fetchHostIpMap,
        calculateSummary,
        getStatusColor,
        getNotificationStatus,
        getNotificationTypeLabel,
        getNotificationTypeClass,
        filterNotificationsByType,
        filterNotificationsBySearch,
        generateNotifications
    };
})();
