(function() {
    // Main initialization function
    async function init() {
        console.log('Notifications page initialized');
        
        // Ensure all required modules are loaded
        if (!window.notificationsCore || !window.notificationsUI || !window.notificationsRenderer || !window.notificationsFilters) {
            console.error('Required notifications modules not loaded');
            return;
        }
        
        // Initialize the page (this will now automatically generate notifications)
        await window.notificationsUI.initializeUI();
        
        // Initialize filters modal
        if (window.notificationsFiltersModal) {
            window.notificationsFiltersModal.init();
        }
        
        // Initialize modal event handlers
        if (typeof setupModalEventHandlers === 'function') {
            setupModalEventHandlers();
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
