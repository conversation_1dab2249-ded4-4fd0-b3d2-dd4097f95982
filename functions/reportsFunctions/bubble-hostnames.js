/**
 * Bubble hostnames map functionality for Reports
 * Extracted from init.js to avoid loading the entire hostlist functionality
 */

/**
 * Map of IP address => Bubble View hostname (nickname)
 * Fetched once per page load from get_bubble_hostnames.php
 */
let bubbleHostnamesMap = null;

/**
 * Fetch Bubble View hostnames from backend database.
 * Returns a map keyed by IP where value is the Bubble View hostname.
 * The result is cached in bubbleHostnamesMap for subsequent calls.
 */
async function fetchBubbleHostnames() {
    if (bubbleHostnamesMap !== null) {
        // Already fetched (or attempted) – reuse cached value
        return bubbleHostnamesMap;
    }
    try {
        const response = await fetch('get_bubble_hostnames.php');
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        bubbleHostnamesMap = await response.json();
    } catch (error) {
        console.error('Error fetching Bubble View hostnames:', error);
        // Prevent further fetch attempts in case of repeated failures
        bubbleHostnamesMap = {};
    }
    return bubbleHostnamesMap;
}

// Initialize bubble hostnames on page load
document.addEventListener('DOMContentLoaded', function() {
    // Pre-fetch bubble hostnames for use in reports
    fetchBubbleHostnames();
});

// Expose for other modules
window.bubbleHostnamesMap = bubbleHostnamesMap;
window.fetchBubbleHostnames = fetchBubbleHostnames; 