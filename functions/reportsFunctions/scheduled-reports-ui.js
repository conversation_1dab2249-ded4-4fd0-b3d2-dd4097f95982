(function() {
    let scheduledReports = [];

    // Load scheduled reports from server
    async function loadScheduledReports() {
        try {
            const response = await fetch('functions/reportsFunctions/scheduledReportsManager.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=list'
            });
            const result = await response.json();
            
            if (result.success) {
                scheduledReports = result.reports || [];
                displayScheduledReports();
                updateScheduledReportsBadge();
            }
        } catch (error) {
            console.error('Failed to load scheduled reports:', error);
        }
    }

    // Display scheduled reports in the modal
    function displayScheduledReports() {
        const container = document.getElementById('scheduled-reports-list');
        if (!container) return;

        // Get search term
        const searchInput = document.getElementById('scheduled-reports-search-input');
        const searchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';

        // Filter reports based on search term
        let filteredReports = scheduledReports;
        if (searchTerm) {
            filteredReports = scheduledReports.filter(report => {
                const nameMatch = report.name.toLowerCase().includes(searchTerm);
                const emailMatch = report.email.toLowerCase().includes(searchTerm);
                return nameMatch || emailMatch;
            });
        }

        if (filteredReports.length === 0) {
            if (scheduledReports.length === 0) {
                container.innerHTML = '<div class="scheduled-reports-empty">No scheduled reports found</div>';
            } else {
                container.innerHTML = '<div class="scheduled-reports-empty">No reports match your search</div>';
            }
            return;
        }

        let html = '';
        filteredReports.forEach(report => {
            const statusClass = report.enabled ? 'enabled' : 'disabled';
            const statusText = report.enabled ? 'Enabled' : 'Disabled';
            const nextRun = calculateNextRun(report);
            
            html += `
                <div class="scheduled-report-item ${statusClass}" data-name="${report.name}">
                    <div class="report-header">
                        <div class="report-name">${report.name}</div>
                        <div class="report-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="report-details">
                        <div class="detail-row">
                            <span class="detail-label">Email:</span>
                            <span class="detail-dots"></span>
                            <span class="detail-value">${report.email}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Schedule:</span>
                            <span class="detail-dots"></span>
                            <span class="detail-value">${report.frequency} at ${report.time}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Range:</span>
                            <span class="detail-dots"></span>
                            <span class="detail-value">${report.range} day(s)</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Type:</span>
                            <span class="detail-dots"></span>
                            <span class="detail-value">${formatReportType(report.type)}</span>
                        </div>
                        ${report.hostgroup ? `<div class="detail-row">
                            <span class="detail-label">Hostgroup:</span>
                            <span class="detail-dots"></span>
                            <span class="detail-value">${report.hostgroup}</span>
                        </div>` : ''}
                        ${report.hostname ? `<div class="detail-row">
                            <span class="detail-label">Host:</span>
                            <span class="detail-dots"></span>
                            <span class="detail-value">${report.hostname}</span>
                        </div>` : ''}
                        ${report.serviceDescription ? `<div class="detail-row">
                            <span class="detail-label">Service Description:</span>
                            <span class="detail-dots"></span>
                            <span class="detail-value">${report.serviceDescription}</span>
                        </div>` : ''}
                        <div class="detail-row">
                            <span class="detail-label">Next Run:</span>
                            <span class="detail-dots"></span>
                            <span class="detail-value">${nextRun}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Created:</span>
                            <span class="detail-dots"></span>
                            <span class="detail-value">${formatDate(report.created)}</span>
                        </div>
                    </div>
                    <div class="report-actions">
                        <button class="report-action-btn send-btn" onclick="sendScheduledReportNow('${report.name}')" title="Send Now">
                            <i class="fa fa-paper-plane"></i>
                        </button>
                        <button class="report-action-btn edit-btn" onclick="editScheduledReport('${report.name}')" title="Edit">
                            <i class="fa fa-edit"></i>
                        </button>
                        <button class="report-action-btn toggle-btn" onclick="toggleScheduledReport('${report.name}')" title="${report.enabled ? 'Disable' : 'Enable'}">
                            <i class="fa fa-${report.enabled ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="report-action-btn delete-btn" onclick="deleteScheduledReport('${report.name}')" title="Delete">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    // Format report type for display
    function formatReportType(type) {
        switch (type) {
            case 'hostgroups': return 'Hosts Only';
            case 'services': return 'Services Only';
            case 'hostgroups,services': return 'Hosts & Services';
            default: return type;
        }
    }

    // Calculate next run time
    function calculateNextRun(report) {
        const now = new Date();
        const [hour, minute] = report.time.split(':').map(Number);
        
        let nextRun = new Date();
        nextRun.setHours(hour, minute, 0, 0);
        
        // If today's time has passed, move to next occurrence
        if (nextRun <= now) {
            switch (report.frequency) {
                case 'daily':
                    nextRun.setDate(nextRun.getDate() + 1);
                    break;
                case 'weekly':
                    // Find the next occurrence of the specified weekday
                    const targetWeekday = report.weekday || 0; // 0 = Sunday, 1 = Monday, etc.
                    const currentWeekday = nextRun.getDay();
                    let daysToAdd = targetWeekday - currentWeekday;
                    
                    // If the target weekday is today or has passed this week, move to next week
                    if (daysToAdd <= 0) {
                        daysToAdd += 7;
                    }
                    
                    nextRun.setDate(nextRun.getDate() + daysToAdd);
                    break;
                case 'monthly':
                    // Find the next occurrence of the specified month day
                    const targetMonthday = report.monthday || 1;
                    const currentMonthday = nextRun.getDate();
                    
                    if (targetMonthday <= currentMonthday) {
                        // Move to next month
                        nextRun.setMonth(nextRun.getMonth() + 1);
                        nextRun.setDate(targetMonthday);
                    } else {
                        // Move to target day in current month
                        nextRun.setDate(targetMonthday);
                    }
                    break;
            }
        } else {
            // Time hasn't passed today, but we need to check if it's the right day for weekly/monthly
            switch (report.frequency) {
                case 'weekly':
                    const targetWeekday = report.weekday || 0;
                    const currentWeekday = nextRun.getDay();
                    
                    if (currentWeekday !== targetWeekday) {
                        // Find the next occurrence of the target weekday
                        let daysToAdd = targetWeekday - currentWeekday;
                        if (daysToAdd <= 0) {
                            daysToAdd += 7;
                        }
                        nextRun.setDate(nextRun.getDate() + daysToAdd);
                    }
                    break;
                case 'monthly':
                    const targetMonthday = report.monthday || 1;
                    const currentMonthday = nextRun.getDate();
                    
                    if (currentMonthday !== targetMonthday) {
                        // Move to target day in current month, or next month if day doesn't exist
                        const daysInMonth = new Date(nextRun.getFullYear(), nextRun.getMonth() + 1, 0).getDate();
                        if (targetMonthday <= daysInMonth) {
                            nextRun.setDate(targetMonthday);
                        } else {
                            // Move to next month
                            nextRun.setMonth(nextRun.getMonth() + 1);
                            nextRun.setDate(targetMonthday);
                        }
                    }
                    break;
            }
        }
        
        return nextRun.toLocaleDateString() + ' ' + nextRun.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    // Format date for display
    function formatDate(timestamp) {
        const date = new Date(timestamp * 1000);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    // Update badge count
    function updateScheduledReportsBadge() {
        const badge = document.getElementById('scheduled-reports-badge');
        if (badge) {
            const count = scheduledReports.filter(r => r.enabled).length;
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count.toString();
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    // Add new scheduled report
    async function addScheduledReport(formData) {
        try {
            const response = await fetch('functions/reportsFunctions/scheduledReportsManager.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Refresh the list
                loadScheduledReports();
                return true;
            } else {
                alert('Failed to add scheduled report: ' + (result.message || 'Unknown error'));
                return false;
            }
        } catch (error) {
            console.error('Add scheduled report error:', error);
            alert('Error adding scheduled report');
            return false;
        }
    }

    // Update existing scheduled report
    async function updateScheduledReport(formData) {
        try {
            const response = await fetch('functions/reportsFunctions/scheduledReportsManager.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Refresh the list
                loadScheduledReports();
                return true;
            } else {
                alert('Failed to update scheduled report: ' + (result.message || 'Unknown error'));
                return false;
            }
        } catch (error) {
            console.error('Update scheduled report error:', error);
            alert('Error updating scheduled report');
            return false;
        }
    }

    // Toggle scheduled report enabled/disabled
    window.toggleScheduledReport = async function(name) {
        try {
            const response = await fetch('functions/reportsFunctions/scheduledReportsManager.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=toggle&name=' + encodeURIComponent(name)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Refresh the list
                loadScheduledReports();
            } else {
                alert('Failed to toggle scheduled report: ' + (result.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Toggle scheduled report error:', error);
            alert('Error toggling scheduled report');
        }
    };

    // Edit scheduled report
    window.editScheduledReport = function(name) {
        const report = scheduledReports.find(r => r.name === name);
        if (!report) {
            alert('Report not found');
            return;
        }

        // Populate the edit form with current values
        document.getElementById('edit-sr-original-name').value = report.name;
        document.getElementById('edit-sr-name').value = report.name;
        document.getElementById('edit-sr-email').value = report.email;
        document.getElementById('edit-sr-frequency').value = report.frequency;
        document.getElementById('edit-sr-time').value = report.time;
        document.getElementById('edit-sr-range').value = report.range;
        document.getElementById('edit-sr-type').value = report.type;
        document.getElementById('edit-sr-entity').value = report.hostgroup || report.hostname || '';
        document.getElementById('edit-sr-service-description').value = report.serviceDescription || '';
        document.getElementById('edit-sr-save-to-server').checked = report.saveToServer;
        
        // Set day selection values and show appropriate field
        if (report.frequency === 'weekly' && report.weekday !== undefined) {
            document.getElementById('edit-sr-weekday').value = report.weekday;
            document.getElementById('edit-sr-weekday-field').style.display = 'flex';
            document.getElementById('edit-sr-monthday-field').style.display = 'none';
        }
        if (report.frequency === 'monthly' && report.monthday !== undefined) {
            document.getElementById('edit-sr-monthday').value = report.monthday;
            document.getElementById('edit-sr-monthday-field').style.display = 'flex';
            document.getElementById('edit-sr-weekday-field').style.display = 'none';
        }
        if (report.frequency === 'daily') {
            document.getElementById('edit-sr-weekday-field').style.display = 'none';
            document.getElementById('edit-sr-monthday-field').style.display = 'none';
        }

        // Set host statuses
        const hostStatuses = report.hostStatuses.split(',');
        document.querySelectorAll('#edit-sr-host-statuses input').forEach(cb => {
            cb.checked = hostStatuses.includes(cb.value);
        });

        // Set service statuses
        const svcStatuses = report.svcStatuses.split(',');
        document.querySelectorAll('#edit-sr-svc-statuses input').forEach(cb => {
            cb.checked = svcStatuses.includes(cb.value);
        });

        // Update entity label based on report type
        const entityLabel = document.querySelector('label[for="edit-sr-entity"]');
        const serviceDescriptionField = document.getElementById('edit-sr-service-description-field');
        if (entityLabel) {
            entityLabel.textContent = report.type === 'services' ? 'Host (optional)' : 'Hostgroup (optional)';
        }
        
        // Show/hide service description field
        if (serviceDescriptionField) {
            serviceDescriptionField.style.display = report.type === 'services' ? 'flex' : 'none';
        }

        // Update status checkbox visibility based on report type
        if (window.reportsUI && window.reportsUI.updateStatusCheckboxVisibility) {
            window.reportsUI.updateStatusCheckboxVisibility('edit-sr', report.type);
        }

        // Populate entity dropdown
        populateEditScheduledReportDropdowns();

        // Show edit form
        const reportsList = document.getElementById('scheduled-reports-list');
        const editForm = document.getElementById('edit-report-form');
        const addForm = document.getElementById('add-report-form');
        const addBtn = document.getElementById('add-new-report-btn');
        const searchBar = document.querySelector('.scheduled-reports-search');
        
        if (reportsList && editForm && addForm && addBtn) {
            reportsList.style.display = 'none';
            addForm.style.display = 'none';
            editForm.style.display = 'block';
            addBtn.style.display = 'none';
            if(searchBar) searchBar.style.display = 'none';
        }
    };

    // Populate service descriptions for scheduled reports
    async function populateScheduledReportServiceDescriptions(selectElement, hostname = '') {
        if (!selectElement) return;
        
        // Clear existing options except the first one
        while (selectElement.options.length > 1) {
            selectElement.remove(1);
        }
        
        try {
            let url = '/nagios/cgi-bin/objectjson.cgi?query=servicelist';
            if (hostname && hostname !== 'all') {
                url += '&hostname=' + encodeURIComponent(hostname);
            }
            
            const response = await fetch(url, { credentials: 'include' });
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
            
            const data = await response.json();
            const servicelist = data?.data?.servicelist || {};
            
            // Collect all unique service descriptions
            const serviceDescriptions = new Set();
            Object.values(servicelist).forEach(services => {
                if (Array.isArray(services)) {
                    services.forEach(service => serviceDescriptions.add(service));
                }
            });
            
            // Sort and add to dropdown
            const sortedServices = Array.from(serviceDescriptions).sort();
            sortedServices.forEach(service => {
                const opt = document.createElement('option');
                opt.value = service;
                opt.textContent = service;
                selectElement.appendChild(opt);
            });
        } catch (error) {
            console.error('Error fetching service descriptions for scheduled reports:', error);
        }
    }

    // Populate dropdowns for edit scheduled reports
    function populateEditScheduledReportDropdowns() {
        const entitySelect = document.getElementById('edit-sr-entity');
        const serviceDescriptionSelect = document.getElementById('edit-sr-service-description');
        const reportType = document.getElementById('edit-sr-type').value;

        if (entitySelect) {
            // Store the current selected value before clearing
            const currentEntityValue = entitySelect.value;

            // Clear existing options except the first one
            while (entitySelect.options.length > 1) {
                entitySelect.remove(1);
            }
            
            const nowSec = Math.floor(Date.now() / 1000);
            const objectType = reportType === 'services' ? 'hosts' : 'hostgroups';
            const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=${objectType}&starttime=${nowSec - 3600}&endtime=${nowSec}`;
            
            fetch(url, { credentials: 'include' })
                .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
                .then(js => {
                    let arr = [];
                    if (objectType === 'hosts') {
                        arr = js?.data?.hosts?.map(h => h.name) || (js?.data?.host ? [js.data.host.name] : []);
                    } else {
                        arr = js?.data?.hostgroups?.map(g => g.name) || (js?.data?.hostgroup ? [js.data.hostgroup.name] : []);
                    }
                    
                    if (arr.length) {
                        arr.sort().forEach(v => {
                            const opt = document.createElement('option');
                            opt.value = v;
                            opt.textContent = v;
                            entitySelect.appendChild(opt);
                        });

                        // Restore the previously selected value
                        if (currentEntityValue) {
                            entitySelect.value = currentEntityValue;
                        }
                    }
                })
                .catch(() => {
                    console.warn(`Failed to populate ${objectType} dropdown for edit`);
                });
        }
        
        // Populate service descriptions if it's a services report
        if (reportType === 'services' && serviceDescriptionSelect) {
            // Store the current selected value before populating
            const currentServiceDescValue = serviceDescriptionSelect.value;
            const entityValue = document.getElementById('edit-sr-entity').value;

            populateScheduledReportServiceDescriptions(serviceDescriptionSelect, entityValue).then(() => {
                // Restore the previously selected value
                if (currentServiceDescValue) {
                    serviceDescriptionSelect.value = currentServiceDescValue;
                }
            });
        }
    }

    // Send scheduled report now
    window.sendScheduledReportNow = async function(name) {
        if (!confirm('Send this report now? This will immediately generate and email the report.')) return;

        try {
            const response = await fetch('functions/reportsFunctions/scheduledReportsManager.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=sendNow&name=' + encodeURIComponent(name)
            });
            
            const result = await response.json();
            
            if (result.success) {
                alert('Report sent successfully!');
            } else {
                alert('Failed to send report: ' + (result.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Send scheduled report error:', error);
            alert('Error sending scheduled report');
        }
    };

    // Delete scheduled report
    window.deleteScheduledReport = async function(name) {
        if (!confirm('Are you sure you want to delete this scheduled report?')) return;

        try {
            const response = await fetch('functions/reportsFunctions/scheduledReportsManager.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=delete&name=' + encodeURIComponent(name)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Refresh the list
                loadScheduledReports();
            } else {
                alert('Failed to delete scheduled report: ' + (result.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Delete scheduled report error:', error);
            alert('Error deleting scheduled report');
        }
    };

    // Open scheduled reports modal
    function openScheduledReportsModal() {
        const modal = document.getElementById('scheduleReportModal');
        if (!modal) return;
        
        modal.style.display = 'flex';
        // Refresh the scheduled reports list when opening
        loadScheduledReports();
    }

    // Initialize scheduled reports on page load
    function init() {
        loadScheduledReports();
    }

    // Export to global scope
    window.scheduledReportsUI = {
        init,
        loadScheduledReports,
        displayScheduledReports,
        addScheduledReport,
        updateScheduledReport,
        populateEditScheduledReportDropdowns,
        openScheduledReportsModal,
        updateScheduledReportsBadge
    };
})();
