<?php
// deleteReportCron.php – disables the scheduled report by deleting the cron file (legacy support)
// Method: POST

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Use the new scheduled reports manager for consistency
require_once 'scheduledReportsManager.php';

// Load existing reports
$reports = loadScheduledReports();

// Find and disable legacy reports
$legacyReports = ['Legacy Daily Report'];
$found = false;

foreach ($reports as $key => $report) {
    if (in_array($report['name'], $legacyReports)) {
        $reports[$key]['enabled'] = false;
        $found = true;
    }
}

if ($found) {
    // Save and update cron
    if (saveScheduledReports($reports) && updateCronFile($reports)) {
        echo json_encode(['success' => true, 'message' => 'Legacy scheduled reports disabled.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to disable scheduled reports.']);
    }
} else {
    // Fallback to old method
    $cronFilePath = '/etc/cron.d/blesk_daily_report';
    $cmd = 'sudo rm -f ' . escapeshellarg($cronFilePath);
    exec($cmd, $out, $status);

    if ($status === 0) {
        echo json_encode(['success' => true, 'message' => 'Scheduled report disabled.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to disable scheduled report (exit ' . $status . ').']);
    }
}
?> 