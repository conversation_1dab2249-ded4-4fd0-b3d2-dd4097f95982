<?php
// getReportCron.php – returns JSON describing the current scheduled report configuration
// Method: GET
// Response: { success: bool, enabled: bool, config?: { email, frequency, time, range } }

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Check for new scheduled reports system first
$configDir = '/var/lib/blesk/scheduled_reports';
$configFile = $configDir . '/scheduled_reports.json';

if (file_exists($configFile)) {
    $content = file_get_contents($configFile);
    if ($content !== false) {
        $reports = json_decode($content, true);
        if (is_array($reports) && !empty($reports)) {
            // Return the first enabled report for backward compatibility
            foreach ($reports as $report) {
                if ($report['enabled']) {
                    echo json_encode([
                        'success' => true,
                        'enabled' => true,
                        'config'  => [
                            'email'     => $report['email'],
                            'frequency' => $report['frequency'],
                            'time'      => $report['time'],
                            'range'     => $report['range'],
                            'hostStatuses' => $report['hostStatuses'],
                            'svcStatuses'  => $report['svcStatuses'],
                        ]
                    ]);
                    exit;
                }
            }
        }
    }
}

// Fallback to old single report system
$cronFilePath = '/etc/cron.d/blesk_daily_report';
if (!file_exists($cronFilePath)) {
    echo json_encode(['success' => true, 'enabled' => false]);
    exit;
}

$lines = file($cronFilePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
$cronLine = null;
foreach ($lines as $line) {
    $trim = trim($line);
    if ($trim === '' || $trim[0] === '#') continue; // skip comments
    $cronLine = $trim;
    break;
}

if (!$cronLine) {
    echo json_encode(['success' => true, 'enabled' => false]);
    exit;
}

// Split by whitespace preserving quoted strings
$parts = preg_split('/\s+/', $cronLine);
if (count($parts) < 10) {
    echo json_encode(['success' => false, 'message' => 'Unable to parse cron entry']);
    exit;
}

$min  = $parts[0];
$hour = $parts[1];
$dom  = $parts[2];
$dow  = $parts[4];

// Email is usually at index 8 (may contain quotes)
$emailRaw = $parts[8] ?? '';
$email = trim($emailRaw, "'\"");
$range = intval($parts[9] ?? 1);
$hostStatusesRaw = $parts[10] ?? 'up,down,unreachable';
$svcStatusesRaw  = $parts[11] ?? 'ok,warning,critical,unknown';
$hostStatuses = trim($hostStatusesRaw, "'\"");
$svcStatuses  = trim($svcStatusesRaw, "'\"");

if ($dom === '*' && $dow === '*') {
    $frequency = 'daily';
} elseif ($dom === '*' && $dow !== '*') {
    $frequency = 'weekly';
} elseif ($dom !== '*' && $dow === '*') {
    $frequency = 'monthly';
} else {
    $frequency = 'custom';
}

$time = str_pad($hour, 2, '0', STR_PAD_LEFT) . ':' . str_pad($min, 2, '0', STR_PAD_LEFT);

echo json_encode([
    'success' => true,
    'enabled' => true,
    'config'  => [
        'email'     => $email,
        'frequency' => $frequency,
        'time'      => $time,
        'range'     => $range,
        'hostStatuses' => $hostStatuses,
        'svcStatuses'  => $svcStatuses,
    ]
]);
?> 