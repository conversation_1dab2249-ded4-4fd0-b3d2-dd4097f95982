<?php
// deleteReport.php - deletes a saved report from the server
// Expected POST param: file (filename without path)

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$filename = isset($_POST['file']) ? trim($_POST['file']) : '';

// Basic filename check
if (empty($filename)) {
    echo json_encode(['success' => false, 'message' => 'No filename provided']);
    exit;
}

// Security check - ensure it's a PDF file
if (!str_ends_with(strtolower($filename), '.pdf')) {
    echo json_encode(['success' => false, 'message' => 'Invalid file type']);
    exit;
}

$REPORTS_DIR = __DIR__ . '/saved_reports';
$filepath = $REPORTS_DIR . '/' . $filename;
$metadataFile = $REPORTS_DIR . '/' . pathinfo($filename, PATHINFO_FILENAME) . '.json';

// Check if file exists
if (!file_exists($filepath)) {
    echo json_encode(['success' => false, 'message' => 'File not found']);
    exit;
}

// Delete both the PDF file and metadata file
$pdfDeleted = unlink($filepath);
$metadataDeleted = true;

if (file_exists($metadataFile)) {
    $metadataDeleted = unlink($metadataFile);
}

if ($pdfDeleted && $metadataDeleted) {
    echo json_encode(['success' => true, 'message' => 'Report deleted successfully']);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to delete report']);
}
?> 