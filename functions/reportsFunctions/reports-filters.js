(function() {
    // Status filtering functionality
    function applyStatusFilter(){
        const contentDiv = document.getElementById('reports-content');
        const rows = contentDiv.querySelectorAll('tr[data-status]');
        const activeStatuses = window.reportsUI.getActiveStatuses();
        
        rows.forEach(row=>{
            const st = row.dataset.status;
            const allStatuses = row.dataset.allStatuses ? row.dataset.allStatuses.split(',') : [st];
            
            // Show row if ANY of its statuses are active
            const shouldShow = allStatuses.some(status => activeStatuses.has(status));
            row.style.display = shouldShow ? '' : 'none';
        });

        // Hide or show entire tables (and their titles) based on visible rows
        const tables = contentDiv.querySelectorAll('table.report-table');
        tables.forEach(table => {
            const hasVisibleRow = Array.from(table.querySelectorAll('tbody tr')).some(r => r.style.display !== 'none');
            const titleEl = table.previousElementSibling;
            if (hasVisibleRow) {
                table.style.display = '';
                if (titleEl && titleEl.classList.contains('report-title')) titleEl.style.display = '';
            } else {
                table.style.display = 'none';
                if (titleEl && titleEl.classList.contains('report-title')) titleEl.style.display = 'none';
            }
        });
    }

    // Export to global scope
    window.reportsFilters = {
        applyStatusFilter
    };
})(); 