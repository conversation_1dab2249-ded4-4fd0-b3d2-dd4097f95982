(function() {
    // Filters modal functionality
    let filtersModal, filtersForm, filtersCancel;
    let filterStart, filterEnd, filterType, filterEntity, filterServiceDescription;
    let filterEntityWrapper, filterServiceDescriptionWrapper, filterEntityLabel;

    // Initialize elements
    function initializeElements() {
        filtersModal = document.getElementById('filtersModal');
        filtersForm = document.getElementById('filters-form');
        filtersCancel = document.getElementById('filters-cancel');
        
        filterStart = document.getElementById('filter-start');
        filterEnd = document.getElementById('filter-end');
        filterType = document.getElementById('filter-type');
        filterEntity = document.getElementById('filter-entity');
        filterServiceDescription = document.getElementById('filter-service-description');

        filterEntityWrapper = document.getElementById('filter-entity-wrapper');
        filterServiceDescriptionWrapper = document.getElementById('filter-service-description-wrapper');
        filterEntityLabel = document.getElementById('filter-entity-label');
    }

    // Open filters modal
    function openFiltersModal() {
        if (!filtersModal) {
            console.error('Filters modal not found');
            return;
        }
        // Sync current values to modal
        syncCurrentValuesToModal();

        // Show modal
        filtersModal.style.display = 'flex';

        // Populate dropdowns
        populateFilterDropdowns();
    }

    // Sync current filter values to modal inputs
    function syncCurrentValuesToModal() {
        const reportStart = document.getElementById('report-start');
        const reportEnd = document.getElementById('report-end');
        const reportType = document.getElementById('report-type');
        const reportHostgroup = document.getElementById('report-hostgroup');
        const reportServiceDescription = document.getElementById('report-service-description');

        if (reportStart && filterStart) filterStart.value = reportStart.value;
        if (reportEnd && filterEnd) filterEnd.value = reportEnd.value;
        if (reportType && filterType) filterType.value = reportType.value;
        if (reportHostgroup && filterEntity) filterEntity.value = reportHostgroup.value;
        if (reportServiceDescription && filterServiceDescription) {
            filterServiceDescription.value = reportServiceDescription.value;
        }

        // Update visibility based on type
        updateFilterTypeVisibility();
        
        // Update time shortcut active state
        updateTimeShortcutActiveState();
    }
    
    // Update time shortcut active state based on current values
    function updateTimeShortcutActiveState() {
        if (!filterStart || !filterEnd) return;
        
        const startDate = new Date(filterStart.value);
        const endDate = new Date(filterEnd.value);
        
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return;
        
        const diffMs = endDate.getTime() - startDate.getTime();
        const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
        
        // Clear all active states
        const shortcutButtons = document.querySelectorAll('.time-shortcut-btn');
        shortcutButtons.forEach(btn => btn.classList.remove('active'));
        
        // Set active state for matching shortcut
        shortcutButtons.forEach(btn => {
            const btnDays = parseInt(btn.dataset.days);
            if (btnDays === diffDays) {
                btn.classList.add('active');
            }
        });
    }

    // Update visibility based on filter type
    function updateFilterTypeVisibility() {
        if (!filterType || !filterEntityWrapper || !filterServiceDescriptionWrapper) return;

        if (filterType.value === 'services') {
            if (filterEntityLabel) filterEntityLabel.textContent = 'Host';
            filterServiceDescriptionWrapper.style.display = 'flex';
            populateFilterHosts();
            populateFilterServiceDescriptions();
        } else {
            if (filterEntityLabel) filterEntityLabel.textContent = 'Hostgroup';
            filterServiceDescriptionWrapper.style.display = 'none';
            populateFilterHostgroups();
        }
    }

    // Populate filter hostgroups dropdown
    function populateFilterHostgroups() {
        if (!filterEntity) return;

        // Clear existing options except first
        while (filterEntity.options.length > 1) {
            filterEntity.remove(1);
        }

        const nowSec = Math.floor(Date.now() / 1000);
        const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hostgroups&starttime=${nowSec - 3600}&endtime=${nowSec}`;

        fetch(url, { credentials: 'include' })
            .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
            .then(js => {
                const arr = js?.data?.hostgroups?.map(g => g.name) || (js?.data?.hostgroup ? [js.data.hostgroup.name] : []);
                if (arr.length) {
                    arr.sort().forEach(v => {
                        const opt = document.createElement('option');
                        opt.value = v;
                        opt.textContent = v;
                        filterEntity.appendChild(opt);
                    });
                }
            })
            .catch(() => {
                console.warn('Failed to populate filter hostgroups dropdown');
            });
    }

    // Populate filter hosts dropdown
    function populateFilterHosts() {
        if (!filterEntity) return;

        // Clear existing options except first
        while (filterEntity.options.length > 1) {
            filterEntity.remove(1);
        }

        const nowSec = Math.floor(Date.now() / 1000);
        const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&starttime=${nowSec - 3600}&endtime=${nowSec}`;

        fetch(url, { credentials: 'include' })
            .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
            .then(js => {
                const arr = js?.data?.hosts?.map(h => h.name) || (js?.data?.host ? [js.data.host.name] : []);
                if (arr.length) {
                    arr.sort().forEach(v => {
                        const opt = document.createElement('option');
                        opt.value = v;
                        opt.textContent = v;
                        filterEntity.appendChild(opt);
                    });
                }
            })
            .catch(() => {
                console.warn('Failed to populate filter hosts dropdown');
            });
    }

    // Populate filter service descriptions dropdown
    async function populateFilterServiceDescriptions(hostname = '') {
        if (!filterServiceDescription) return;
        
        // Clear existing options except first
        while (filterServiceDescription.options.length > 1) {
            filterServiceDescription.remove(1);
        }
        
        try {
            let url = '/nagios/cgi-bin/objectjson.cgi?query=servicelist';
            if (hostname && hostname !== 'all') {
                url += '&hostname=' + encodeURIComponent(hostname);
            }
            
            const response = await fetch(url, { credentials: 'include' });
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
            
            const data = await response.json();
            const servicelist = data?.data?.servicelist || {};
            
            // Collect all unique service descriptions
            const serviceDescriptions = new Set();
            Object.values(servicelist).forEach(services => {
                if (Array.isArray(services)) {
                    services.forEach(service => serviceDescriptions.add(service));
                }
            });
            
            // Sort and add to dropdown
            const sortedServices = Array.from(serviceDescriptions).sort();
            sortedServices.forEach(service => {
                const opt = document.createElement('option');
                opt.value = service;
                opt.textContent = service;
                filterServiceDescription.appendChild(opt);
            });
        } catch (error) {
            console.error('Error fetching filter service descriptions:', error);
        }
    }

    // Populate filter dropdowns
    function populateFilterDropdowns() {
        updateFilterTypeVisibility();
    }

    // Apply filters
    function applyFilters() {
        const reportStart = document.getElementById('report-start');
        const reportEnd = document.getElementById('report-end');
        const reportType = document.getElementById('report-type');
        const reportHostgroup = document.getElementById('report-hostgroup');
        const reportServiceDescription = document.getElementById('report-service-description');
        
        // Update main form values
        if (reportStart && filterStart) reportStart.value = filterStart.value;
        if (reportEnd && filterEnd) reportEnd.value = filterEnd.value;
        if (reportType && filterType) reportType.value = filterType.value;
        if (reportHostgroup && filterEntity) reportHostgroup.value = filterEntity.value;
        if (reportServiceDescription && filterServiceDescription) {
            reportServiceDescription.value = filterServiceDescription.value;
        }
        
        // Update UI to reflect type change
        if (window.reportsUI && window.reportsUI.buildStatusFilters) {
            window.reportsUI.buildStatusFilters(filterType.value);
        }
        
        // Close modal
        filtersModal.style.display = 'none';

        // Generate report with new filters
        if (window.reportsCore && window.reportsCore.generateReport) {
            window.reportsCore.generateReport();
        }
    }



    // Setup event listeners
    function setupEventListeners() {
        // Filters button
        const filtersBtn = document.getElementById('report-filters');
        if (filtersBtn) {
            filtersBtn.addEventListener('click', openFiltersModal);
        }
        
        // Modal close handlers
        if (filtersCancel) {
            filtersCancel.addEventListener('click', () => {
                filtersModal.style.display = 'none';
            });
        }
        
        // Form submit
        if (filtersForm) {
            filtersForm.addEventListener('submit', (e) => {
                e.preventDefault();
                applyFilters();
            });
        }
        
        // Type change handler
        if (filterType) {
            filterType.addEventListener('change', updateFilterTypeVisibility);
        }
        
        // Entity change handler for service descriptions
        if (filterEntity) {
            filterEntity.addEventListener('change', () => {
                if (filterType.value === 'services') {
                    populateFilterServiceDescriptions(filterEntity.value);
                }
            });
        }
        
        // Time shortcuts handlers
        setupTimeShortcuts();
    }
    
    // Setup time shortcuts functionality
    function setupTimeShortcuts() {
        const shortcutButtons = document.querySelectorAll('.time-shortcut-btn');
        
        shortcutButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const days = parseInt(btn.dataset.days);
                if (isNaN(days)) return;
                
                // Calculate start and end dates
                const endDate = new Date();
                const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));
                
                // Update the datetime inputs
                if (filterStart) {
                    filterStart.value = window.reportsCore.toLocalIso(startDate);
                }
                if (filterEnd) {
                    filterEnd.value = window.reportsCore.toLocalIso(endDate);
                }
                
                // Update active state
                shortcutButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
        
        // Also update active state when manually changing dates
        if (filterStart) {
            filterStart.addEventListener('change', updateTimeShortcutActiveState);
        }
        if (filterEnd) {
            filterEnd.addEventListener('change', updateTimeShortcutActiveState);
        }
    }

    // Initialize
    function init() {
        initializeElements();
        setupEventListeners();
    }

    // Export to global scope
    window.reportsFiltersModal = {
        init,
        openFiltersModal
    };
})();
