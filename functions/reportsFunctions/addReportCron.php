<?php
// addReportCron.php - handles scheduling a daily email report via cron (legacy support)
// Expected POST param: email

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$email = isset($_POST['email']) ? trim($_POST['email']) : '';

// Validate multiple emails separated by semicolons
$emails = array_map('trim', explode(';', $email));
$validEmails = [];
foreach ($emails as $singleEmail) {
    if (!empty($singleEmail)) {
        if (!filter_var($singleEmail, FILTER_VALIDATE_EMAIL)) {
            echo json_encode(['success' => false, 'message' => 'Invalid email address: ' . $singleEmail]);
            exit;
        }
        $validEmails[] = $singleEmail;
    }
}

if (empty($validEmails)) {
    echo json_encode(['success' => false, 'message' => 'At least one valid email address must be provided.']);
    exit;
}

// Join back the validated emails for storage
$email = implode(';', $validEmails);

// Use the new scheduled reports manager for consistency
require_once 'scheduledReportsManager.php';

$frequency = isset($_POST['frequency']) ? strtolower(trim($_POST['frequency'])) : 'daily';
$time      = isset($_POST['time']) ? trim($_POST['time']) : '00:05';
$range     = isset($_POST['range']) ? intval($_POST['range']) : 1;
$hostStatuses = isset($_POST['hostStatuses']) ? preg_replace('/[^a-z,]/i','', strtolower($_POST['hostStatuses'])) : 'up,down,unreachable';
$svcStatuses  = isset($_POST['svcStatuses']) ? preg_replace('/[^a-z,]/i','', strtolower($_POST['svcStatuses'])) : 'ok,warning,critical,unknown';
$saveToServer = isset($_POST['saveToServer']) ? ($_POST['saveToServer'] === 'true') : true;

if (!in_array($frequency, ['daily','weekly','monthly'])) $frequency = 'daily';
if ($range < 1 || $range > 365) {
    $range = $frequency==='daily'?1:($frequency==='weekly'?7:31);
}

// Create a legacy report name
$reportName = 'Legacy Daily Report';

// Load existing reports
$reports = loadScheduledReports();

// Check if legacy report already exists
$legacyExists = false;
foreach ($reports as $report) {
    if ($report['name'] === $reportName) {
        $legacyExists = true;
        break;
    }
}

if ($legacyExists) {
    echo json_encode(['success' => false, 'message' => 'A legacy report already exists. Please use the new scheduled reports interface.']);
    exit;
}

// Create new legacy report
$newReport = [
    'name' => $reportName,
    'email' => $email,
    'frequency' => $frequency,
    'time' => $time,
    'range' => $range,
    'type' => 'hostgroups,services',
    'hostgroup' => '',
    'hostname' => '',
    'hostStatuses' => $hostStatuses,
    'svcStatuses' => $svcStatuses,
    'saveToServer' => $saveToServer,
    'enabled' => true,
    'created' => time(),
    'createdDate' => date('Y-m-d H:i:s')
];

$reports[] = $newReport;

// Save and update cron
if (saveScheduledReports($reports) && updateCronFile($reports)) {
    echo json_encode(['success' => true, 'message' => 'Daily report successfully scheduled using the new system.']);
} else {
    echo json_encode(['success' => false, 'message' => 'Error scheduling report.']);
}
?> 