<?php
// scheduledReportsManager.php - manages multiple scheduled reports with enhanced filtering
// Expected POST params: action, name, email, frequency, time, range, type, hostgroup, hostname, hostStatuses, svcStatuses, saveToServer

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$action = isset($_POST['action']) ? trim($_POST['action']) : '';

// Path where the scheduled reports configuration will be stored
$configDir = '/var/lib/blesk/scheduled_reports';
$configFile = $configDir . '/scheduled_reports.json';

// Ensure config directory exists
if (!is_dir($configDir)) {
    if (!mkdir($configDir, 0755, true)) {
        echo json_encode(['success' => false, 'message' => 'Failed to create config directory']);
        exit;
    }
}

// Load existing scheduled reports
function loadScheduledReports() {
    global $configFile;
    if (file_exists($configFile)) {
        $content = file_get_contents($configFile);
        if ($content !== false) {
            $data = json_decode($content, true);
            if (is_array($data)) {
                return $data;
            }
        }
    }
    return [];
}

// Save scheduled reports
function saveScheduledReports($reports) {
    global $configFile;
    return file_put_contents($configFile, json_encode($reports, JSON_PRETTY_PRINT)) !== false;
}

// Generate cron line for a scheduled report
function generateCronLine($report) {
    $scriptPath = '/var/www/html/bubblemaps/functions/reportsFunctions/sendScheduledReport.php';
    
    // Parse time HH:MM
    if (!preg_match('/^(\d{1,2}):(\d{2})$/', $report['time'], $m)) {
        $hour = 0; $min = 5;
    } else {
        $hour = max(0, min(23, intval($m[1])));
        $min = max(0, min(59, intval($m[2])));
    }

    // Generate cron schedule
    switch ($report['frequency']) {
        case 'weekly':
            $weekday = isset($report['weekday']) ? intval($report['weekday']) : 0;
            $cronSchedule = "$min $hour * * $weekday";
            break;
        case 'monthly':
            $monthday = isset($report['monthday']) ? intval($report['monthday']) : 1;
            // Ensure monthday is valid (1-31)
            $monthday = max(1, min(31, $monthday));
            $cronSchedule = "$min $hour $monthday * *";
            break;
        case 'daily':
        default:
            $cronSchedule = "$min $hour * * *";
            break;
    }

    // Build command arguments
    $args = [
        escapeshellarg($report['email']),
        $report['range'],
        escapeshellarg($report['hostStatuses']),
        escapeshellarg($report['svcStatuses']),
        escapeshellarg($report['type']),
        escapeshellarg($report['hostgroup'] ?? ''),
        escapeshellarg($report['hostname'] ?? ''),
        escapeshellarg($report['serviceDescription'] ?? ''),
        escapeshellarg($report['name'])
    ];

    $argsStr = implode(' ', $args);
    
    return sprintf("%s root /usr/bin/php %s %s >>/var/log/blesk_report.log 2>&1", 
        $cronSchedule, escapeshellarg($scriptPath), $argsStr);
}

// Update cron file with all scheduled reports
function updateCronFile($reports) {
    $cronFilePath = '/etc/cron.d/blesk_daily_report';
    $cronContent = "# Blesk - scheduled reports\n";
    
    foreach ($reports as $report) {
        if ($report['enabled']) {
            $cronContent .= generateCronLine($report) . "\n";
        }
    }

    try {
        // Write cron file using sudo (we have permissions)
        $writeCmd = 'echo ' . escapeshellarg($cronContent) . ' | sudo tee ' . escapeshellarg($cronFilePath) . ' > /dev/null';
        exec($writeCmd, $out, $statusWrite);
        if ($statusWrite !== 0) {
            error_log('[scheduledReportsManager] Failed to write cron file (exit ' . $statusWrite . ')');
            return false;
        }

        // Set correct permissions
        $chmodCmd = 'sudo chmod 644 ' . escapeshellarg($cronFilePath);
        exec($chmodCmd, $out2, $statusChmod);
        if ($statusChmod !== 0) {
            error_log('[scheduledReportsManager] chmod failed for cron file (exit ' . $statusChmod . ')');
        }

        return true;
    } catch (Throwable $e) {
        error_log('[scheduledReportsManager] ' . $e->getMessage());
        return false;
    }
}

// Handle different actions
switch ($action) {
    case 'add':
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $frequency = isset($_POST['frequency']) ? strtolower(trim($_POST['frequency'])) : 'daily';
        $time = isset($_POST['time']) ? trim($_POST['time']) : '00:05';
        $range = isset($_POST['range']) ? intval($_POST['range']) : 1;
        $type = isset($_POST['type']) ? trim($_POST['type']) : 'hostgroups,services';
        $hostgroup = isset($_POST['hostgroup']) ? trim($_POST['hostgroup']) : '';
        $hostname = isset($_POST['hostname']) ? trim($_POST['hostname']) : '';
        $serviceDescription = isset($_POST['serviceDescription']) ? trim($_POST['serviceDescription']) : '';
        $hostStatuses = isset($_POST['hostStatuses']) ? preg_replace('/[^a-z,]/i', '', strtolower($_POST['hostStatuses'])) : 'up,down,unreachable';
        $svcStatuses = isset($_POST['svcStatuses']) ? preg_replace('/[^a-z,]/i', '', strtolower($_POST['svcStatuses'])) : 'ok,warning,critical,unknown';
        $saveToServer = isset($_POST['saveToServer']) ? ($_POST['saveToServer'] === 'true') : true;

        // Validate required fields
        if (empty($name) || empty($email)) {
            echo json_encode(['success' => false, 'message' => 'Name and email are required']);
            exit;
        }

        // Validate email
        $emails = array_map('trim', explode(';', $email));
        $validEmails = [];
        foreach ($emails as $singleEmail) {
            if (!empty($singleEmail)) {
                if (!filter_var($singleEmail, FILTER_VALIDATE_EMAIL)) {
                    echo json_encode(['success' => false, 'message' => 'Invalid email address: ' . $singleEmail]);
                    exit;
                }
                $validEmails[] = $singleEmail;
            }
        }

        if (empty($validEmails)) {
            echo json_encode(['success' => false, 'message' => 'At least one valid email address must be provided']);
            exit;
        }

        // Validate frequency
        if (!in_array($frequency, ['daily', 'weekly', 'monthly'])) {
            $frequency = 'daily';
        }

        // Validate range
        if ($range < 1 || $range > 365) {
            $range = $frequency === 'daily' ? 1 : ($frequency === 'weekly' ? 7 : 31);
        }

        // Validate type
        if (!in_array($type, ['hostgroups', 'services', 'hostgroups,services'])) {
            $type = 'hostgroups,services';
        }

        // Load existing reports
        $reports = loadScheduledReports();

        // Check if name already exists
        foreach ($reports as $report) {
            if ($report['name'] === $name) {
                echo json_encode(['success' => false, 'message' => 'A scheduled report with this name already exists']);
                exit;
            }
        }

        // Create new report
        $newReport = [
            'name' => $name,
            'email' => implode(';', $validEmails),
            'frequency' => $frequency,
            'time' => $time,
            'range' => $range,
            'type' => $type,
            'hostgroup' => $hostgroup,
            'hostname' => $hostname,
            'serviceDescription' => $serviceDescription,
            'hostStatuses' => $hostStatuses,
            'svcStatuses' => $svcStatuses,
            'saveToServer' => $saveToServer,
            'weekday' => isset($_POST['weekday']) ? intval($_POST['weekday']) : 0,
            'monthday' => isset($_POST['monthday']) ? intval($_POST['monthday']) : 1,
            'enabled' => true,
            'created' => time(),
            'createdDate' => date('Y-m-d H:i:s')
        ];

        $reports[] = $newReport;

        // Save and update cron
        $saveResult = saveScheduledReports($reports);
        $cronResult = updateCronFile($reports);
        
        if ($saveResult && $cronResult) {
            echo json_encode(['success' => true, 'message' => 'Scheduled report added successfully']);
        } else {
            $errorMsg = 'Failed to save scheduled report';
            if (!$saveResult) {
                $errorMsg .= ' - configuration save failed';
            }
            if (!$cronResult) {
                $errorMsg .= ' - cron file creation failed';
            }
            echo json_encode(['success' => false, 'message' => $errorMsg]);
        }
        break;

    case 'list':
        $reports = loadScheduledReports();
        echo json_encode(['success' => true, 'reports' => $reports]);
        break;

    case 'delete':
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        
        if (empty($name)) {
            echo json_encode(['success' => false, 'message' => 'Report name is required']);
            exit;
        }

        $reports = loadScheduledReports();
        $found = false;

        foreach ($reports as $key => $report) {
            if ($report['name'] === $name) {
                unset($reports[$key]);
                $found = true;
                break;
            }
        }

        if (!$found) {
            echo json_encode(['success' => false, 'message' => 'Scheduled report not found']);
            exit;
        }

        // Re-index array
        $reports = array_values($reports);

        // Save and update cron
        if (saveScheduledReports($reports) && updateCronFile($reports)) {
            echo json_encode(['success' => true, 'message' => 'Scheduled report deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete scheduled report']);
        }
        break;

    case 'toggle':
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        
        if (empty($name)) {
            echo json_encode(['success' => false, 'message' => 'Report name is required']);
            exit;
        }

        $reports = loadScheduledReports();
        $found = false;

        foreach ($reports as $key => $report) {
            if ($report['name'] === $name) {
                $reports[$key]['enabled'] = !$report['enabled'];
                $found = true;
                break;
            }
        }

        if (!$found) {
            echo json_encode(['success' => false, 'message' => 'Scheduled report not found']);
            exit;
        }

        // Save and update cron
        if (saveScheduledReports($reports) && updateCronFile($reports)) {
            $status = $reports[$key]['enabled'] ? 'enabled' : 'disabled';
            echo json_encode(['success' => true, 'message' => "Scheduled report $status successfully"]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update scheduled report']);
        }
        break;

    case 'update':
        $originalName = isset($_POST['originalName']) ? trim($_POST['originalName']) : '';
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $frequency = isset($_POST['frequency']) ? strtolower(trim($_POST['frequency'])) : 'daily';
        $time = isset($_POST['time']) ? trim($_POST['time']) : '00:05';
        $range = isset($_POST['range']) ? intval($_POST['range']) : 1;
        $type = isset($_POST['type']) ? trim($_POST['type']) : 'hostgroups,services';
        $hostgroup = isset($_POST['hostgroup']) ? trim($_POST['hostgroup']) : '';
        $hostname = isset($_POST['hostname']) ? trim($_POST['hostname']) : '';
        $serviceDescription = isset($_POST['serviceDescription']) ? trim($_POST['serviceDescription']) : '';
        $hostStatuses = isset($_POST['hostStatuses']) ? preg_replace('/[^a-z,]/i', '', strtolower($_POST['hostStatuses'])) : 'up,down,unreachable';
        $svcStatuses = isset($_POST['svcStatuses']) ? preg_replace('/[^a-z,]/i', '', strtolower($_POST['svcStatuses'])) : 'ok,warning,critical,unknown';
        $saveToServer = isset($_POST['saveToServer']) ? ($_POST['saveToServer'] === 'true') : true;

        // Validate required fields
        if (empty($originalName) || empty($name) || empty($email)) {
            echo json_encode(['success' => false, 'message' => 'Original name, name and email are required']);
            exit;
        }

        // Validate email
        $emails = array_map('trim', explode(';', $email));
        $validEmails = [];
        foreach ($emails as $singleEmail) {
            if (!empty($singleEmail)) {
                if (!filter_var($singleEmail, FILTER_VALIDATE_EMAIL)) {
                    echo json_encode(['success' => false, 'message' => 'Invalid email address: ' . $singleEmail]);
                    exit;
                }
                $validEmails[] = $singleEmail;
            }
        }

        if (empty($validEmails)) {
            echo json_encode(['success' => false, 'message' => 'At least one valid email address must be provided']);
            exit;
        }

        // Validate frequency
        if (!in_array($frequency, ['daily', 'weekly', 'monthly'])) {
            $frequency = 'daily';
        }

        // Validate range
        if ($range < 1 || $range > 365) {
            $range = $frequency === 'daily' ? 1 : ($frequency === 'weekly' ? 7 : 31);
        }

        // Validate type
        if (!in_array($type, ['hostgroups', 'services', 'hostgroups,services'])) {
            $type = 'hostgroups,services';
        }

        // Load existing reports
        $reports = loadScheduledReports();

        // Find the report to update
        $reportIndex = -1;
        foreach ($reports as $key => $report) {
            if ($report['name'] === $originalName) {
                $reportIndex = $key;
                break;
            }
        }

        if ($reportIndex === -1) {
            echo json_encode(['success' => false, 'message' => 'Scheduled report not found']);
            exit;
        }

        // Check if new name conflicts with existing reports (excluding the current one)
        foreach ($reports as $key => $report) {
            if ($key !== $reportIndex && $report['name'] === $name) {
                echo json_encode(['success' => false, 'message' => 'A scheduled report with this name already exists']);
                exit;
            }
        }

        // Update the report
        $reports[$reportIndex]['name'] = $name;
        $reports[$reportIndex]['email'] = implode(';', $validEmails);
        $reports[$reportIndex]['frequency'] = $frequency;
        $reports[$reportIndex]['time'] = $time;
        $reports[$reportIndex]['range'] = $range;
        $reports[$reportIndex]['type'] = $type;
        $reports[$reportIndex]['hostgroup'] = $hostgroup;
        $reports[$reportIndex]['hostname'] = $hostname;
        $reports[$reportIndex]['serviceDescription'] = $serviceDescription;
        $reports[$reportIndex]['hostStatuses'] = $hostStatuses;
        $reports[$reportIndex]['svcStatuses'] = $svcStatuses;
        $reports[$reportIndex]['saveToServer'] = $saveToServer;
        $reports[$reportIndex]['weekday'] = isset($_POST['weekday']) ? intval($_POST['weekday']) : 0;
        $reports[$reportIndex]['monthday'] = isset($_POST['monthday']) ? intval($_POST['monthday']) : 1;

        // Save and update cron
        $saveResult = saveScheduledReports($reports);
        $cronResult = updateCronFile($reports);
        
        if ($saveResult && $cronResult) {
            echo json_encode(['success' => true, 'message' => 'Scheduled report updated successfully']);
        } else {
            $errorMsg = 'Failed to update scheduled report';
            if (!$saveResult) {
                $errorMsg .= ' - configuration save failed';
            }
            if (!$cronResult) {
                $errorMsg .= ' - cron file creation failed';
            }
            echo json_encode(['success' => false, 'message' => $errorMsg]);
        }
        break;

    case 'sendNow':
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        
        if (empty($name)) {
            echo json_encode(['success' => false, 'message' => 'Report name is required']);
            exit;
        }

        $reports = loadScheduledReports();
        $report = null;

        foreach ($reports as $r) {
            if ($r['name'] === $name) {
                $report = $r;
                break;
            }
        }

        if (!$report) {
            echo json_encode(['success' => false, 'message' => 'Scheduled report not found']);
            exit;
        }

        // Check if email sender is configured
        $emailSenderFile = '/var/lib/blesk/emailsender';
        if (!file_exists($emailSenderFile)) {
            echo json_encode(['success' => false, 'message' => 'Email sender not configured. Please configure email settings first.']);
            exit;
        }

        // Validate email addresses
        $emails = array_map('trim', explode(';', $report['email']));
        $validEmails = [];
        foreach ($emails as $email) {
            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $validEmails[] = $email;
            }
        }
        
        if (empty($validEmails)) {
            echo json_encode(['success' => false, 'message' => 'No valid email addresses found in the report configuration']);
            exit;
        }

        // Build command to send the report now
        $scriptPath = '/var/www/html/bubblemaps/functions/reportsFunctions/sendScheduledReport.php';
        
        // Check if script exists
        if (!file_exists($scriptPath)) {
            echo json_encode(['success' => false, 'message' => 'Report script not found: ' . $scriptPath]);
            exit;
        }
        
        // Build command arguments
        $args = [
            escapeshellarg($report['email']),
            $report['range'],
            escapeshellarg($report['hostStatuses']),
            escapeshellarg($report['svcStatuses']),
            escapeshellarg($report['type']),
            escapeshellarg($report['hostgroup'] ?? ''),
            escapeshellarg($report['hostname'] ?? ''),
            escapeshellarg($report['serviceDescription'] ?? ''),
            escapeshellarg($report['name'])
        ];

        $argsStr = implode(' ', $args);
        $cmd = '/usr/bin/php ' . escapeshellarg($scriptPath) . ' ' . $argsStr;
        
        // Execute the command
        $output = [];
        $returnVar = 0;
        $currentDir = getcwd();
        chdir('/var/www/html/bubblemaps');
        exec($cmd . ' 2>&1', $output, $returnVar);
        chdir($currentDir);
        
        if ($returnVar === 0) {
            echo json_encode(['success' => true, 'message' => 'Report sent successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to send report']);
        }
        break;

    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}
?>
