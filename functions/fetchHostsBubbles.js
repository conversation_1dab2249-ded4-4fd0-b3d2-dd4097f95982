function fetchHostsBubbles() {
    const svg = d3.select("#map");
    const container = d3.select("#canvas-container");

    // Multi-select functionality variables
    let selectedHosts = new Set();
    let isMultiSelectMode = false;

    // Make selectedHosts globally accessible
    window.selectedHosts = selectedHosts;

    // Detect mobile
    function isMobile() {
        return window.matchMedia("(max-width: 768px)").matches;
    }

    // Dynamic dimensions
    function updateDimensions() {
        const width = container.node().getBoundingClientRect().width || 1200;
        const height = container.node().getBoundingClientRect().height || 900;
        svg.attr("width", width).attr("height", height);
        return { width, height };
    }

    let dimensions = updateDimensions();

    // Adjusted sizes based on mobile
    const baseHostRadius = isMobile() ? 30 : 50;
    const minGroupRadius = isMobile() ? 70 : 120;

    // Multi-select helper functions
    function toggleHostSelection(hostData, bubbleElement) {
        const hostId = hostData.id;
        if (selectedHosts.has(hostId)) {
            selectedHosts.delete(hostId);
            d3.select(bubbleElement).classed('selected', false);
        } else {
            selectedHosts.add(hostId);
            d3.select(bubbleElement).classed('selected', true);
        }
        updateMultiSelectUI();
    }

    function clearSelection() {
        selectedHosts.clear();
        d3.selectAll('.host-bubble').classed('selected', false);
        updateMultiSelectUI();
    }

    function updateMultiSelectUI() {
        const selectionCount = selectedHosts.size;

        // Update or create selection indicator
        let selectionIndicator = d3.select('#selection-indicator');
        if (selectionIndicator.empty()) {
            selectionIndicator = d3.select('body')
                .append('div')
                .attr('id', 'selection-indicator')
                .classed('selection-indicator', true);
        }

        if (selectionCount > 0) {
            selectionIndicator
                .style('display', 'block')
                .html(`
                    <span class="selection-count">${selectionCount} host(s) selected</span>
                    <button class="clear-selection-btn" onclick="clearHostSelection()">Clear</button>
                `);
        } else {
            selectionIndicator.style('display', 'none');
        }
    }

    // Make clearSelection available globally
    window.clearHostSelection = clearSelection;

    // Keyboard event handlers for multi-select
    function setupKeyboardHandlers() {
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Control' || event.key === 'Meta') {
                isMultiSelectMode = true;
                document.body.classList.add('multi-select-mode');
            }
            if (event.key === 'Escape') {
                clearSelection();
            }
        });

        document.addEventListener('keyup', (event) => {
            if (event.key === 'Control' || event.key === 'Meta') {
                isMultiSelectMode = false;
                document.body.classList.remove('multi-select-mode');
            }
        });
    }

    setupKeyboardHandlers();

    // Construct the endpoint URL
    const urlParams = new URLSearchParams(window.location.search);
    const hostgroup = urlParams.get('hostgroup');
    const subnet = urlParams.get('subnet');

    // Check if we're in subnet mode: look for subnet=true in URL
    // When in subnet mode, the actual subnet value is in the first 'subnet' parameter
    const urlString = window.location.search;
    const isSubnetMode = urlString.includes('subnet=true');
    const subnetName = isSubnetMode && subnet && subnet !== 'all' && subnet !== 'true' ? subnet : null;
    let endpoint = `scan.php?fromHostsPage=yes&infra=${encodeURIComponent(urlParams.get('infra'))}`;
    if (hostgroup) {
        endpoint = `scan.php?fromHostsPage=yes&hostgroup=${encodeURIComponent(hostgroup)}&infra=${encodeURIComponent(urlParams.get('infra'))}`;
    }

    // Fetch JSON data
    fetch(endpoint)
        .then(response => response.json())
        .then(async data => {
            let hostData = [];
            let hostgroups = [];
            let simulation;
            let arrows = null; // Declare arrows once here

            // Initialize SPM availability flag
            let spmAvailable = false;

            // Layout persistence helpers for hostgroup centroids
            const infraName = urlParams.get('infra') || 'default';
            async function fetchLayout() {
                try {
                    let url = `functions/hostlistPhpFunctions/get_hostgroup_positions.php?infra=${encodeURIComponent(infraName)}`;
                    if (isSubnetMode && subnetName) {
                        url += `&subnet=${encodeURIComponent(subnetName)}`;
                    }
                    const r = await fetch(url);
                    if (!r.ok) return null;
                    return await r.json();
                } catch (e) {
                    console.warn('Failed to load layout', e);
                    return null;
                }
            }
            async function saveLayout(lock) {
                try {
                    if (hostgroup) return; // Only save in hostgroup overview
                    const positions = {};
                    hostgroups.forEach(g => {
                        if (g && g.hostgroup && g.hostgroup !== 'No Hostgroup' && g.hostgroup !== 'unknown' && typeof g.x === 'number' && typeof g.y === 'number') {
                            positions[g.hostgroup] = { x: g.x, y: g.y };
                        }
                    });
                    const payload = {
                        infra: infraName,
                        lockActive: !!lock,
                        positions
                    };
                    if (isSubnetMode && subnetName) {
                        payload.subnet = subnetName;
                    }
                    await fetch('functions/hostlistPhpFunctions/save_hostgroup_positions.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                } catch (e) {
                    console.warn('Failed to save layout', e);
                }
            }
            function applyLayoutToHostgroups(layout) {
                if (!layout || !layout.positions) return;
                const keys = Object.keys(layout.positions || {});
                // If no saved positions at all, keep the existing (Fibonacci/spiral) layout
                if (keys.length === 0) {
                    window.layoutLockActive = !!layout.lockActive;
                    const btn = document.getElementById('lockLayoutBtn');
                    if (btn) {
                        const update = () => {
                            btn.classList.add('connections-toggle');
                            btn.innerHTML = window.layoutLockActive ? '<i class="fa fa-lock"></i> Locked' : '<i class="fa fa-unlock-alt"></i> Unlocked';
                            btn.classList.toggle('active', !window.layoutLockActive);
                        };
                        update();
                        btn.onclick = async () => {
                            window.layoutLockActive = !window.layoutLockActive;
                            update();
                            // Toggle dragging based on lock state: unlocked = draggable, locked = not draggable
                            setGroupDragging(!window.layoutLockActive);
                            await saveLayout(window.layoutLockActive);
                        };
                    }
                    return;
                }
                const used = [];
                const width = dimensions.width;
                const height = dimensions.height;
                function collides(x, y, r) {
                    return used.some(u => {
                        const dx = x - u.x; const dy = y - u.y;
                        return Math.sqrt(dx*dx + dy*dy) < (r + u.r + 10);
                    });
                }
                function placeRandomNonOverlapping(r) {
                    const pad = 20;
                    const minX = r + pad, maxX = width - r - pad;
                    const minY = r + pad, maxY = height - r - pad;
                    for (let i = 0; i < 200; i++) {
                        const x = minX + Math.random() * (maxX - minX);
                        const y = minY + Math.random() * (maxY - minY);
                        if (!collides(x, y, r)) return { x, y };
                    }
                    return { x: Math.max(minX, Math.min(maxX, width/2)), y: Math.max(minY, Math.min(maxY, height/2)) };
                }
                hostgroups.forEach(g => {
                    const p = layout.positions[g.hostgroup];
                    if (p && typeof p.x === 'number' && typeof p.y === 'number') {
                        g.x = p.x; g.y = p.y;
                    } else {
                        const pos = placeRandomNonOverlapping(g.radius);
                        g.x = pos.x; g.y = pos.y;
                    }
                    used.push({ x: g.x, y: g.y, r: g.radius });
                });
                window.layoutLockActive = !!layout.lockActive;
                const btn = document.getElementById('lockLayoutBtn');
                if (btn) {
                    const update = () => {
                        btn.classList.add('connections-toggle');
                        btn.innerHTML = window.layoutLockActive ? '<i class="fa fa-lock"></i> Locked' : '<i class="fa fa-unlock-alt"></i> Unlocked';
                        btn.classList.toggle('active', !window.layoutLockActive);
                    };
                    update();
                    btn.onclick = async () => {
                        window.layoutLockActive = !window.layoutLockActive;
                        update();
                        // Toggle dragging based on lock state: unlocked = draggable, locked = not draggable
                        setGroupDragging(!window.layoutLockActive);
                        await saveLayout(window.layoutLockActive);
                    };
                }
            }

            // Create separate groups for arrows, bubbles, speed labels, and host bubbles to control z-order
            const arrowGroup = svg.append("g").attr("class", "arrow-group"); // Arrows go in this group (under)
            const bubbleGroup = svg.append("g").attr("class", "bubble-group"); // Hostgroup bubbles go in this group (middle)
            const speedLabelGroup = svg.append("g").attr("class", "speed-label-group"); // Speed labels go in this group (above hostgroups)
            const hostBubbleGroup = svg.append("g").attr("class", "host-bubble-group"); // Host bubbles go in this group (top)

                // Global toggle for speed text visibility (will be loaded from server)
                if (typeof window.showSpeedText === 'undefined') {
                    window.showSpeedText = true; // Default fallback
                }

                function updateSpeedTextVisibility() {
                    // Hide or show the whole speed-label group based on toggle
                    speedLabelGroup.style("display", window.showSpeedText ? null : "none");
                    if (window.showSpeedText && typeof updateSpeedLabelVisibility === 'function') {
                        updateSpeedLabelVisibility();
                    }
                }
                window.updateSpeedTextVisibility = updateSpeedTextVisibility;

                function updateSpeedButtonVisibility() {
                    const speedTextBtn = document.getElementById('toggle-speedtext-btn');
                    if (speedTextBtn) {
                        // Hide speed button if connections are off OR if SPM is not available
                        // Use the global spmAvailable variable that's set by checkSpmAvailability()
                        const shouldShow = window.showConnections && spmAvailable;
                        speedTextBtn.style.display = shouldShow ? '' : 'none';
                    }
                }
                window.updateSpeedButtonVisibility = updateSpeedButtonVisibility;

                // Function to load speed text toggle state from server
                async function loadSpeedTextToggle() {
                    try {
                        const response = await fetch('configHandler.php?param=speedTextToggle');
                        const data = await response.json();
                        if (data.success) {
                            window.showSpeedText = data.speedTextToggle === 'on';
                            return data.speedTextToggle;
                        }
                    } catch (error) {
                        console.error('Error loading speed text toggle state:', error);
                    }
                    return 'on'; // Default fallback
                }

                // Function to save speed text toggle state to server
                async function saveSpeedTextToggle(toggle) {
                    try {
                        const response = await fetch('configHandler.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ speedTextToggle: toggle })
                        });
                        const data = await response.json();
                        return data.success;
                    } catch (error) {
                        console.error('Error saving speed text toggle state:', error);
                        return false;
                    }
                }

                // Wire up Speed Text toggle button if present in the UI
                const speedTextBtn = document.getElementById('toggle-speedtext-btn');
                if (speedTextBtn) {
                    const refresh = () => {
                        // Add/remove active class; keep label constant
                        speedTextBtn.classList.toggle('active', !!window.showSpeedText);
                        speedTextBtn.innerHTML = '<i class="fa fa-bolt"></i> Speed';
                    };

                    speedTextBtn.onclick = async () => {
                        window.showSpeedText = !window.showSpeedText;
                        refresh();
                        updateSpeedTextVisibility();
                        // Save the new state to server
                        await saveSpeedTextToggle(window.showSpeedText ? 'on' : 'off');
                    };
                }

                // Load initial state from server and update UI
                loadSpeedTextToggle().then(() => {
                    updateSpeedTextVisibility();
                    // Update button appearance after loading state
                    if (speedTextBtn) {
                        speedTextBtn.classList.toggle('active', !!window.showSpeedText);
                    }
                    // Update visibility after state is loaded and button is updated
                    updateSpeedButtonVisibility();
                });


            // Zoom behavior with touch support
            const zoom = d3.zoom()
                .scaleExtent([0.1, 5])
                .on("zoom", (event) => {
                    arrowGroup.attr("transform", event.transform);
                    bubbleGroup.attr("transform", event.transform);
                    speedLabelGroup.attr("transform", event.transform);
                    hostBubbleGroup.attr("transform", event.transform);
                });

            // Apply zoom behavior without restricting touch events
            svg.call(zoom);

            // Enable canvas panning by adding touch listeners to the SVG
            svg.style("touch-action", "none");

            // Explicit touch event handling for mobile
            let touchStartX, touchStartY;
            let lastTouchDistance = 0;
            let currentTransform = d3.zoomIdentity;

            svg.on("touchstart", function(event) {
                // Store the current transform
                currentTransform = d3.zoomTransform(svg.node());

                // Get touch coordinates
                const touches = event.touches;
                if (touches.length === 1) {
                    touchStartX = touches[0].clientX;
                    touchStartY = touches[0].clientY;
                } else if (touches.length === 2) {
                    // For pinch-zoom, calculate initial distance
                    lastTouchDistance = Math.hypot(
                        touches[1].clientX - touches[0].clientX,
                        touches[1].clientY - touches[0].clientY
                    );
                }
                event.preventDefault();
            });

            svg.on("touchmove", function(event) {
                const touches = event.touches;

                if (touches.length === 1) {
                    // Single touch for panning
                    const dx = touches[0].clientX - touchStartX;
                    const dy = touches[0].clientY - touchStartY;

                    // Apply translation based on touch movement
                    const newTransform = currentTransform.translate(dx / currentTransform.k, dy / currentTransform.k);
                    svg.call(zoom.transform, newTransform);
                } else if (touches.length === 2) {
                    // Two touches for pinch zoom
                    const currentDistance = Math.hypot(
                        touches[1].clientX - touches[0].clientX,
                        touches[1].clientY - touches[0].clientY
                    );

                    if (lastTouchDistance > 0) {
                        // Calculate zoom factor
                        const scaleFactor = currentDistance / lastTouchDistance;

                        // Get the midpoint of the two touches
                        const midX = (touches[0].clientX + touches[1].clientX) / 2;
                        const midY = (touches[0].clientY + touches[1].clientY) / 2;

                        // Convert midpoint to SVG coordinates
                        const svgPoint = svg.node().createSVGPoint();
                        svgPoint.x = midX;
                        svgPoint.y = midY;

                        // Apply zoom centered on the midpoint
                        const newK = currentTransform.k * scaleFactor;
                        const constrainedK = Math.max(0.1, Math.min(5, newK)); // Respect zoom limits

                        // Create transform with new scale
                        const newTransform = currentTransform.scale(constrainedK / currentTransform.k);
                        svg.call(zoom.transform, newTransform);

                        // Update for next move
                        currentTransform = newTransform;
                        lastTouchDistance = currentDistance;
                    }
                }
                event.preventDefault();
            });

            svg.on("touchend", function() {
                // Reset touch states when touches end
                lastTouchDistance = 0;
            });

            if (hostgroup) {
                let allHosts = data.all || [];
                // Filter out blacklisted hosts and hosts pending approval (apmStatus = 'ask')
                allHosts = allHosts.filter(host =>
                    (host.blacklist !== 1 && host.blacklist !== "1") &&
                    (host.apmStatus !== 'ask')
                );

                allHosts = allHosts.filter(host => {
                    let hostgroups;
                    if (host && host.hostgroup != null) {
                        try {
                            hostgroups = JSON.parse(host.hostgroup);
                            if (!Array.isArray(hostgroups)) {
                                console.warn(`Hostgroup is not an array for ${host.hostname}: ${host.hostgroup}`);
                                hostgroups = [host.hostgroup];
                            }


                        } catch (e) {
                            console.warn(`Invalid hostgroup format for ${host.hostname}: ${host.hostgroup}`);
                            hostgroups = [host.hostgroup];
                        }
                    } else {
                        hostgroups = [];
                    }
                    const matchesHostgroup = Array.isArray(hostgroups) && hostgroups.includes(hostgroup);
                    const matchesSubnet = !subnet || subnet === 'all' || host.subnet === subnet;
                    return matchesHostgroup && matchesSubnet;
                });

                const centerX = dimensions.width / 2;
                const centerY = dimensions.height / 2;
                hostData = allHosts.map(host => {
                    const angle = Math.random() * 2 * Math.PI;
                    const r = Math.random() * (Math.min(dimensions.width, dimensions.height) / 3);
                    let hostgroups;
                    if (host && host.hostgroup != null) {
                        try {
                            hostgroups = JSON.parse(host.hostgroup);
                            if (!Array.isArray(hostgroups)) {
                                hostgroups = [host.hostgroup];
                            }
                        } catch (e) {
                            hostgroups = [host.hostgroup];
                        }
                    } else {
                        hostgroups = [];
                    }
                    return {
                        id: host.id,
                        hostname: host.hostname || host.ip || 'Unknown',
                        ip: host.ip || null,
                        subnet: host.subnet || null,
                        hostgroups: hostgroups,
                        x: centerX + (Math.random() - 0.5) * dimensions.width * 0.5, // Random position within 50% of width
                        y: centerY + (Math.random() - 0.5) * dimensions.height * 0.5, // Random position within 50% of height
                        size: baseHostRadius
                    };
                });

                simulation = d3.forceSimulation(hostData)
                    .force("charge", d3.forceManyBody().strength(isMobile() ? -10 : -30)) // Moderate repulsion
                    .force("collision", d3.forceCollide().radius(d => d.size + 2).strength(0.8))
                    .force("center", d3.forceCenter(centerX, centerY).strength(0.05)) // Gentle centering force
                    .alphaDecay(0.02); // Slightly faster stabilization

                // Make simulation globally available for filtering functions
                window.simulation = simulation;
            } else {
                let allHosts = [];
                if (subnet && data[subnet]) {
                    allHosts = data[subnet] || [];
                } else {
                    allHosts = Object.values(data).flat();
                }

                // Filter out blacklisted hosts and hosts pending approval (apmStatus = 'ask')
                allHosts = allHosts.filter(host =>
                    (host.blacklist !== 1 && host.blacklist !== "1") &&
                    (host.apmStatus !== 'ask')
                );

                const parsedHosts = allHosts.map(host => {
                    let hostgroups;
                    try {
                        hostgroups = JSON.parse(host.hostgroup);
                    } catch (e) {
                        console.warn(`Invalid hostgroup format for ${host.hostname}: ${host.hostgroup}`);
                        hostgroups = [host.hostgroup];
                    }
                    if (!hostgroups || hostgroups.length === 0 || hostgroups[0] === null) {
                        hostgroups = ["No Hostgroup"];
                    }
                    return { ...host, hostgroups };
                });

                const hostgroupsData = d3.group(parsedHosts, d => d.hostgroups[0]);
                hostgroups = Array.from(hostgroupsData, ([hostgroup, hosts]) => ({
                    hostgroup,
                    hosts,
                    count: hosts.length,
                    radius: Math.max(minGroupRadius, Math.sqrt(hosts.length) * baseHostRadius * (isMobile() ? 1.5 : 1.6))
                }));

                function assignCentroids() {
                    const width = dimensions.width;
                    const height = dimensions.height;
                    const margin = isMobile() ? 20 : 40;

                    // Sort hostgroups by size (largest first for better priority)
                    hostgroups.sort((a, b) => b.radius - a.radius);

                    // Create a more natural arrangement using a combination of techniques

                    // Revised scaling and placement for Fibonacci-like spiral
                    const goldenRatio = 1.618033988749895;
                    const centerX = width / 2;
                    const centerY = height / 2;
                    let effectiveScale;

                    if (hostgroups.length === 0) {
                        effectiveScale = 0; // No groups, no scale needed
                    } else if (hostgroups.length === 1) {
                        effectiveScale = 0; // Single group will be at the center (distance = 0)
                    } else {
                        // Scale so the outermost element (at index hostgroups.length - 1),
                        // using sqrt(i) for distance, is roughly at 1/3 of the smaller screen dimension.
                        // Max sqrt(i) for i from 0 to N-1 is sqrt(N-1).
                        effectiveScale = (Math.min(width, height) / 3) / Math.sqrt(hostgroups.length - 1);
                    }

                    // Estimate text widths for collision detection
                    // Approximate character width in pixels (adjust based on your font)
                    const charWidth = isMobile() ? 7 : 10;
                    const textHeight = isMobile() ? 12 : 18;

                    // Add text dimensions to each hostgroup
                    hostgroups.forEach(group => {
                        // Estimate text width based on string length
                        group.textWidth = (group.hostgroup || "").length * charWidth;
                        group.textHeight = textHeight;
                        // Center the text horizontally above the bubble and keep a constant vertical gap.
                        // textX is half of the estimated text width to achieve horizontal centering.
                        group.textX = -group.textWidth / 2;
                        // Place the baseline of the text a constant distance (e.g., 10px) above the circle's top edge.
                        group.textY = -group.radius - 10;
                    });

                    // Place each bubble along the spiral with varied randomness
                    hostgroups.forEach((group, i) => {
                        // Calculate the distance along the spiral (larger groups closer to center)
                        // Distance from center based on index `i`. Math.sqrt(i) places i=0 (largest group) at the center.
                        const distance = effectiveScale * Math.sqrt(i);

                        // Calculate angle using golden ratio for better distribution
                        const angle = i * goldenRatio * Math.PI * 2;

                        // Calculate position with some randomness in the angle and distance
                        const angleJitter = (Math.random() - 0.5) * Math.PI / 4; // +/- 22.5 degrees
                        const distanceJitter = (Math.random() * 0.3 + 0.85); // 85-115% of original distance

                        group.x = centerX + Math.cos(angle + angleJitter) * distance * distanceJitter;
                        group.y = centerY + Math.sin(angle + angleJitter) * distance * distanceJitter;

                        // Add additional non-uniform randomness based on position in spiral
                        const randomFactor = 0.1 + (i / hostgroups.length) * 0.2; // Increases randomness for outer elements
                        group.x += (Math.random() - 0.5) * width * randomFactor;
                        group.y += (Math.random() - 0.5) * height * randomFactor;
                    });

                    // Ensure bubbles stay within visible bounds
                    const padding = margin * 2;
                    hostgroups.forEach(group => {
                        group.x = Math.max(padding + group.radius, Math.min(width - padding - group.radius, group.x));
                        group.y = Math.max(padding + group.radius, Math.min(height - padding - group.radius, group.y));
                    });

                    // Apply collision detection to prevent significant overlaps
                    const iterations = 20; // Increased iterations for better separation
                    for (let iter = 0; iter < iterations; iter++) {
                        hostgroups.forEach((groupA, i) => {
                            hostgroups.forEach((groupB, j) => {
                                if (i !== j) {
                                    // Circle-to-circle collision
                                    const dx = groupA.x - groupB.x;
                                    const dy = groupA.y - groupB.y;
                                    const distance = Math.sqrt(dx * dx + dy * dy);
                                    const minDistance = groupA.radius + groupB.radius + margin/2;

                                    if (distance < minDistance) {
                                        // Move groups apart
                                        const moveX = dx * (minDistance - distance) / distance * 0.5;
                                        const moveY = dy * (minDistance - distance) / distance * 0.5;

                                        groupA.x += moveX;
                                        groupA.y += moveY;
                                        groupB.x -= moveX;
                                        groupB.y -= moveY;
                                    }

                                    // Text-to-circle collision detection
                                    // Check if groupA's text overlaps with groupB's circle
                                    const textCenterX = groupA.x + groupA.textX + groupA.textWidth / 2;
                                    const textCenterY = groupA.y + groupA.textY + groupA.textHeight / 2;

                                    const textToBubbleDistance = Math.sqrt(
                                        Math.pow(textCenterX - groupB.x, 2) +
                                        Math.pow(textCenterY - groupB.y, 2)
                                    );

                                    // Text rectangle diagonal radius (approximation)
                                    const textRadius = Math.sqrt(Math.pow(groupA.textWidth / 2, 2) + Math.pow(groupA.textHeight / 2, 2));

                                    if (textToBubbleDistance < (textRadius + groupB.radius)) {
                                        // Text overlaps with another bubble, adjust position
                                        const moveAwayFactor = 0.3;
                                        const moveX = (textCenterX - groupB.x) * moveAwayFactor;
                                        const moveY = (textCenterY - groupB.y) * moveAwayFactor;

                                        groupA.x += moveX;
                                        groupB.x -= moveX;
                                        groupA.y += moveY;
                                        groupB.y -= moveY;
                                    }
                                }
                            });
                        });
                    }
                }
                assignCentroids();

                // Load saved layout positions (and lock state); apply before creating hostData
                let __layout = null;
                if (!hostgroup) {
                    __layout = await fetchLayout();
                    if (__layout) {
                        applyLayoutToHostgroups(__layout);
                    } else {
                        // No layout exists - save the current Fibonacci positions that were generated by assignCentroids()
                        // This applies to both subnet mode and "All Hosts" mode
                        await saveLayout(false);
                    }
                }


                function centerAndZoomOnCentroids(groups, svgElement, zoomBehavior) {
                    if (!groups || groups.length === 0) return; // Handle empty groups

                    // Get current VISIBLE dimensions of the container
                    const visibleWidth = container.node().getBoundingClientRect().width || 1200;
                    const visibleHeight = container.node().getBoundingClientRect().height || 900;

                    // Calculate the bounding box including radii based on group positions
                    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                    groups.forEach(d => {
                        // Ensure d.x, d.y, and d.radius are valid numbers
                        if (typeof d.x !== 'number' || typeof d.y !== 'number' || typeof d.radius !== 'number') {
                            console.warn('Skipping group with invalid data:', d);
                            return;
                        }
                        minX = Math.min(minX, d.x - d.radius);
                        minY = Math.min(minY, d.y - d.radius);
                        maxX = Math.max(maxX, d.x + d.radius);
                        maxY = Math.max(maxY, d.y + d.radius);
                    });

                    // Check if bounding box calculation was successful
                    if (!isFinite(minX) || !isFinite(minY) || !isFinite(maxX) || !isFinite(maxY)) {
                        console.error("Failed to calculate bounding box for centroids.");
                        return;
                    }

                    const boundingWidth = maxX - minX;
                    const boundingHeight = maxY - minY;

                    if (boundingWidth <= 0 || boundingHeight <= 0) {
                         console.warn("Bounding box has zero or negative dimensions.", {boundingWidth, boundingHeight});
                         // Fallback: Center on the average position with a default scale
                         const avgX = d3.mean(groups, d => d.x);
                         const avgY = d3.mean(groups, d => d.y);
                         if (typeof avgX === 'number' && typeof avgY === 'number') {
                            const fallbackScale = 0.5;
                            const translateX = visibleWidth / 2 - avgX * fallbackScale;
                            const translateY = visibleHeight / 2 - avgY * fallbackScale;
                             svgElement.transition()

                                .duration(750)
                                .call(zoomBehavior.transform, d3.zoomIdentity.translate(translateX, translateY).scale(fallbackScale));
                         }
                         return;
                    }

                    // Add some padding (e.g., 10% on each side -> 20% total)
                    const paddingFactor = 1.2;
                    const targetWidth = boundingWidth * paddingFactor;
                    const targetHeight = boundingHeight * paddingFactor;

                    // Calculate scale to fit the padded bounding box within the visible area
                    const scaleX = visibleWidth / targetWidth;
                    const scaleY = visibleHeight / targetHeight;
                    let scale = Math.min(scaleX, scaleY);

                    // Clamp scale within allowed limits (e.g., 0.1x to 5x)
                    scale = Math.max(0.1, Math.min(scale, 5));

                    // Calculate translation to center the bounding box center in the visible area
                    const boundingCenterX = minX + boundingWidth / 2;
                    const boundingCenterY = minY + boundingHeight / 2;
                    const translateX = visibleWidth / 2 - boundingCenterX * scale;
                    const translateY = visibleHeight / 2 - boundingCenterY * scale;

                    // Apply the calculated transform
                    svgElement.transition()
                        .duration(750)
                        .call(zoomBehavior.transform, d3.zoomIdentity.translate(translateX, translateY).scale(scale));
                }



                hostData = parsedHosts.map(host => {
                    const group = hostgroups.find(g => g.hostgroup === host.hostgroups[0]);
                    const angle = Math.random() * 2 * Math.PI;
                    const r = Math.random() * (group.radius - baseHostRadius);
                    return {
                        id: host.id,
                        hostname: host.hostname || host.ip,
                        ip: host.ip,
                        hostgroups: host.hostgroups,
                        apmStatus: host.apmStatus,
                        x: group.x + Math.cos(angle) * r,
                        y: group.y + Math.sin(angle) * r,
                        size: baseHostRadius,
                        groupX: group.x,
                        groupY: group.y,
                        groupRadius: group.radius
                    };
                });

                function forceContain() {
                    const strength = isMobile() ? 0.3 : 0.2;
                    const gravityStrength = 0.05;
                    let nodes;

                    function force(alpha) {
                        nodes.forEach(node => {
                            const dx = node.x - node.groupX;
                            const dy = node.y - node.groupY;
                            const distance = Math.sqrt(dx * dx + dy * dy);
                            if (distance > node.groupRadius - node.size) {
                                const angle = Math.atan2(dy, dx);
                                const targetX = node.groupX + Math.cos(angle) * (node.groupRadius - node.size);
                                const targetY = node.groupY + Math.sin(angle) * (node.groupRadius - node.size);
                                node.vx += (targetX - node.x) * strength * alpha * 2;
                                node.vy += (targetY - node.y) * strength * alpha * 2;
                            }

                            node.vy += gravityStrength * alpha;
                        });
                    }

                    force.initialize = function (_) { nodes = _; };
                    return force;
                }

                simulation = d3.forceSimulation(hostData)
                    .force("charge", d3.forceManyBody().strength(isMobile() ? 5 : 10))
                    .force("collision", d3.forceCollide().radius(d => d.size + 2))
                    .force("contain", forceContain())
                    .alphaDecay(0.01);

                // Make simulation globally available for filtering functions
                window.simulation = simulation;
            }

            function dragGroup(sim) {
                let moved = false;
                let startX, startY;
                let touchStartTime; // To track the start of a touch
                let longPressTriggered = false; // Flag to indicate a long press occurred
                const LONG_PRESS_THRESHOLD = 500; // Time in ms to consider it a long press

                function dragstarted(event, d) {
                    if (!event.active) sim.alphaTarget(0.3).restart();
                    d.fx = d.x;
                    d.fy = d.y;
                    startX = event.x;
                    startY = event.y;
                    moved = false;
                    longPressTriggered = false;

                    // If it's a touch event, record the start time
                    if (event.sourceEvent && event.sourceEvent.type === 'touchstart') {
                        touchStartTime = Date.now();
                    }
                }

                function dragged(event, d) {
                    d.fx = event.x;
                    d.fy = event.y;
                    d.x = event.x;
                    d.y = event.y;
                    hostData.forEach(host => {
                        if (host.hostgroups[0] === d.hostgroup) {
                            host.groupX = d.x;
                            host.groupY = d.y;
                        }
                    });
                    sim.nodes(hostData).alpha(0.3).restart();
                    moved = Math.abs(event.x - startX) > 5 || Math.abs(event.y - startY) > 5;
                }

                function dragended(event, d) {
                    if (!event.active) sim.alphaTarget(0);
                    d.fx = null;
                    d.fy = null;

                    // Check if it was a touch event and calculate duration
                    if (event.sourceEvent && event.sourceEvent.type === 'touchend' && touchStartTime) {
                        const touchDuration = Date.now() - touchStartTime;
                        if (touchDuration >= LONG_PRESS_THRESHOLD) {
                            longPressTriggered = true; // Mark as long press
                        }
                    }


                        // Persist positions whenever a hostgroup was moved (regardless of lock state)
                        if (moved) {
                            saveLayout(!!window.layoutLockActive);
                        }

                    // Only proceed with navigation if it wasn't moved significantly AND no long press occurred
                    if (!moved && !longPressTriggered) {
                        const hostgroupName = d.hostgroup;
                        const currentUrl = new URL(window.location.href);
                        const currentSubnet = urlParams.get('subnet') || 'all';
                        currentUrl.searchParams.set('hostgroup', hostgroupName);
                        currentUrl.searchParams.set('subnet', currentSubnet);
                        window.location.href = currentUrl.toString();
                    }
                }

                return d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended)
                    .touchable(true); // Explicitly enable touch support
            }

                // Enable/disable dragging of hostgroup bubbles based on lock state
                function setGroupDragging(enabled) {
                    const sel = d3.selectAll('.group-bubble');
                    if (sel.empty()) return;
                    if (enabled) {
                        sel.style('cursor', 'move');
                        sel.call(dragGroup(simulation));
                    } else {
                        sel.on('.drag', null);
                        // Remove explicit cursor so it inherits the canvas cursor
                        sel.style('cursor', null);
                    }
                }


            if (!hostgroup) {
                const groupBubbles = bubbleGroup.selectAll(".group-bubble")
                    .data(hostgroups)
                    .enter()
                    .append("circle")
                    .attr("class", "group-bubble")
                    .attr("r", d => d.radius)
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y)
                    .style("display", d => d.hostgroup === "No Hostgroup" || d.hostgroup === "unknown" ? "none" : null)
                    .call(dragGroup(simulation))

	                // Apply lock state to dragging: unlocked => draggable; locked => disabled
	                setGroupDragging(!window.layoutLockActive);


	                // Fit all hostgroups on initial load
	                centerAndZoomOnCentroids(hostgroups, svg, zoom);


                groupBubbles
                    .on("touchstart", function (event) {
                        // Don't completely prevent default - just stop propagation
                        event.stopPropagation();
                    })
                    .on("touchend", function (event, d) {
                        const touchDuration = Date.now() - (touchStartTime || Date.now());
                        if (touchDuration < LONG_PRESS_THRESHOLD) {
                            // Treat as a tap (short press), trigger navigation manually if needed
                            if (!moved) {
                                const hostgroupName = d.hostgroup;
                                const currentUrl = new URL(window.location.href);
                                const currentSubnet = urlParams.get('subnet') || 'all';
                                currentUrl.searchParams.set('hostgroup', hostgroupName);
                                currentUrl.searchParams.set('subnet', currentSubnet);
                                window.location.href = currentUrl.toString();
                            }
                        }
                    });

                bubbleGroup.selectAll(".group-text")
                    .data(hostgroups)
                    .enter()
                    .append("text")
                    .attr("class", "group-text")
                    .attr("x", d => d.x + d.textX)
                    .attr("y", d => d.y + d.textY)
                    .style("text-anchor", "start")
                    .style("font-size", isMobile() ? "12px" : "18px")
                    .style("display", d => d.hostgroup === "No Hostgroup" || d.hostgroup === "unknown" ? "none" : null)
                    .text(d => `${d.hostgroup} (${d.count})`);
            }

            // Check if SPM module is available once
            async function checkSpmAvailability() {
                try {
                    const response = await fetch('src/hostRelationship/getSpeedInfo.php?parent_ip=test&child_ip=test');
                    if (response.status === 500) {
                        console.log('SPM module not available - speed info will be disabled');
                        return false;
                    }
                    return true;
                } catch (error) {
                    console.log('SPM module not available - speed info will be disabled');
                    return false;
                }
            }

            // Fetch parent-child relationships from the database
            async function fetchParentChildRelationships(hostData) {
                const relationshipMap = new Map();
                try {
                    const response = await fetch('src/hostRelationship/getParentChildRelationships.php');
                    if (!response.ok) {
                        console.error("Failed to fetch parent-child relationships from DB:", response.statusText);
                        return relationshipMap;
                    }
                    const dbRelationships = await response.json();

                    // Group children by parent IP with additional metadata
                    const childrenByParentIp = new Map();
                    for (const rel of dbRelationships) {
                        if (!childrenByParentIp.has(rel.parent_ip)) {
                            childrenByParentIp.set(rel.parent_ip, []);
                        }
                        childrenByParentIp.get(rel.parent_ip).push({
                            childIp: rel.child_ip,
                            source: rel.source,
                            connectionType: rel.connection_type,
                            parentHostname: rel.parent_hostname,
                            childHostname: rel.child_hostname
                        });
                    }

                    // Create the relationshipMap expected by renderArrows
                    childrenByParentIp.forEach((childrenData, parentIp) => {
                        const parentHost = hostData.find(h => h.ip === parentIp);
                        if (parentHost) {
                            // Find all child hosts that exist in the current view
                            const visibleChildren = childrenData.filter(childData =>
                                hostData.some(h => h.ip === childData.childIp)
                            );

                            if (visibleChildren.length > 0) {
                                relationshipMap.set(parentHost.id, {
                                    parent: parentHost,
                                    childrenData: visibleChildren
                                });
                            }
                        }
                    });
                } catch (err) {
                    console.error('Error fetching or processing parent-child relationships:', err);
                }
                return relationshipMap;
            }

            // Render arrows from parent to children using IPs
            function renderArrows(relationshipMap) {
                // Flatten the parent-child relationships into individual pairs
                const arrowData = [];
                relationshipMap.forEach((value, parentId) => {
                    const { parent, childrenData = [] } = value || {};

                    if (childrenData && childrenData.length > 0) {
                        childrenData.forEach(childData => {
                            const childHost = hostData.find(h =>
                                h.ip === childData.childIp
                            );
                            if (childHost) {
                                arrowData.push({
                                    parentId,
                                    parent,
                                    childIp: childData.childIp,
                                    source: childData.source,
                                    connectionType: childData.connectionType,
                                    parentHostname: childData.parentHostname,
                                    childHostname: childData.childHostname
                                });
                            } else {
                                console.warn(`Child IP ${childData.childIp} not found in hostData`);
                            }
                        });
                    }
                });

                // Remove existing arrows and speed labels
                arrowGroup.selectAll(".parent-child-arrow").remove();
                speedLabelGroup.selectAll(".speed-label").remove();



                // Draw arrows with clearer styling
                arrows = arrowGroup.selectAll(".parent-child-arrow")
                    .data(arrowData)
                    .enter()
                    .append("path")
                    .attr("class", "parent-child-arrow")
                    .attr("fill", "none")
                    .attr("stroke", "#4da6ff")
                    .attr("stroke-opacity", 0.8)
                    .attr("stroke-width", 2)
                    .attr("marker-end", "url(#arrowhead)")
                    .attr("stroke-linecap", "round")
                    .attr("stroke-dasharray", "6,4")
                    .style("pointer-events", "none");

                // Add speed labels alongside arrows
                const speedLabels = speedLabelGroup.selectAll(".speed-label")
                    .data(arrowData)
                    .enter()
                    .append("text")
                    .attr("class", "speed-label")
                    .style("font-size", "9px")
                    .style("pointer-events", "all")
                    .style("cursor", "pointer")
                    .style("text-anchor", "middle")
                    .style("dominant-baseline", "middle")
                    .style("display", "none") // Start hidden until we get speed info
                    .style("font-family", "monospace") // Use monospace for better alignment
                    .text("")
                    .on("mouseenter", function(event, d) {
                        event.stopPropagation();
                        showSpeedTooltip(event, d);
                    })
                    .on("mouseleave", function(event) {
                        event.stopPropagation();
                        hideSpeedTooltip();
                    });

                // Fetch speed information for each arrow
                arrowData.forEach((arrow, index) => {
                    fetchSpeedInfo(arrow.parent.ip, arrow.childIp).then(speedInfo => {
                        const label = d3.select(speedLabels.nodes()[index]);
                        if (speedInfo && speedInfo.ports && speedInfo.ports.length > 0) {
                            // Find the highest speed value
                            const highestSpeed = speedInfo.ports.reduce((max, port) => {
                                const currentSpeed = port.port_speed;
                                const maxSpeed = max.port_speed;

                                // Simple comparison - you might want to parse speeds for more accurate comparison
                                // For now, just compare strings (works for most cases like "1G", "10G", "100M")
                                return currentSpeed > maxSpeed ? port : max;
                            });

                            // Create label text with speed and traffic information
                            let labelText = highestSpeed.port_speed;

                            // Add traffic information if available
                            if (highestSpeed.traffic && (highestSpeed.traffic.current_in || highestSpeed.traffic.current_out)) {
                                const inTraffic = highestSpeed.traffic.current_in || 'N/A';
                                const outTraffic = highestSpeed.traffic.current_out || 'N/A';
                                labelText += `\nIN: ${inTraffic} | OUT: ${outTraffic}`;
                            }

                            label.text(labelText)
                                .attr("data-speed-info", JSON.stringify(speedInfo))
                                .style("display", window.showSpeedText ? "block" : "none");

                            // Update speed button visibility when new speed data is added
                            if (typeof window.updateSpeedButtonVisibility === 'function') {
                                window.updateSpeedButtonVisibility();
                            }
                        } else {
                            // No speed info available, hide the label
                            if (speedInfo && speedInfo.error) {
                                console.log('Speed info error for', arrow.parent.ip, '->', arrow.childIp, ':', speedInfo);
                            }
                            label.text("").style("display", "none");
                        }
                    }).catch(error => {
                        console.warn('Failed to fetch speed info for', arrow.parent.ip, '->', arrow.childIp, ':', error);
                        const label = d3.select(speedLabels.nodes()[index]);
                        label.text("").style("display", "none");
                    });
                });

            }



            // Function to fetch speed information from Netdisco
            async function fetchSpeedInfo(parentIp, childIp) {
                // Skip if SPM is not available
                if (!spmAvailable) {
                    return null;
                }

                try {
                    const response = await fetch(`src/hostRelationship/getSpeedInfo.php?parent_ip=${encodeURIComponent(parentIp)}&child_ip=${encodeURIComponent(childIp)}`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    const data = await response.json();
                    return data.error ? null : data;
                } catch (error) {
                    console.warn('Failed to fetch speed info for', parentIp, '->', childIp, ':', error.message);
                    return null;
                }
            }

            // Helper function to position tooltip within viewport bounds
            function positionTooltip(event, tooltip) {
                // Show the tooltip temporarily to get accurate dimensions
                tooltip.style("display", "block");

                // Get tooltip dimensions with fallbacks
                const tooltipHeight = tooltip.node().offsetHeight || 200;
                const tooltipWidth = tooltip.node().offsetWidth || 250;
                const viewportHeight = window.innerHeight;
                const viewportWidth = window.innerWidth;
                const buffer = 10; // Small buffer from viewport edges

                let top = event.pageY - 10;
                let left = event.pageX + 10;

                // Adjust if tooltip would go beyond bottom edge
                if (top + tooltipHeight + buffer > viewportHeight) {
                    top = event.pageY - tooltipHeight - 10; // Flip above the mouse
                }

                // Adjust if tooltip would go beyond right edge
                if (left + tooltipWidth + buffer > viewportWidth) {
                    left = event.pageX - tooltipWidth - 10; // Flip left of the mouse
                }

                // Ensure it doesn't go off the top or left edge
                top = Math.max(buffer, top); // Keep a small buffer from top
                left = Math.max(buffer, left); // Keep a small buffer from left

                tooltip.style("top", `${top}px`)
                       .style("left", `${left}px`);
            }

            // Function to show speed tooltip
            function showSpeedTooltip(event, arrowData) {
                const speedInfo = JSON.parse(event.target.getAttribute('data-speed-info') || '{}');
                if (!speedInfo.ports || speedInfo.ports.length === 0) return;

                const parentHost = hostData.find(h => h.ip === arrowData.parent.ip);
                const childHost = hostData.find(h => h.ip === arrowData.childIp);

                // Determine source display text
                let sourceText = 'Unknown';
                if (arrowData.source === 'netdisco') {
                    sourceText = 'SPM (Auto-detected)';
                } else if (arrowData.source === 'nagiosql') {
                    sourceText = 'APM';
                } else if (arrowData.source === 'both') {
                    sourceText = 'Both (SPM + APM)';
                }

                // Build ports section
                let portsHtml = '';
                if (speedInfo.ports && speedInfo.ports.length > 0) {
                    if (speedInfo.ports.length === 1) {
                        // Single port
                        const port = speedInfo.ports[0];
                        portsHtml = `
                            <div class="tooltip-separator"></div>
                            <div class="tooltip-row">
                                <span class="tooltip-label">Port:</span>
                                <span class="tooltip-value">${port.port_number}</span>
                            </div>
                            <div class="tooltip-separator"></div>
                            <div class="tooltip-row">
                                <span class="tooltip-label">Speed:</span>
                                <span class="tooltip-value">${port.port_speed}</span>
                            </div>
                        `;

                        // Add traffic information for single port if available
                        if (port.traffic && (port.traffic.current_in || port.traffic.current_out)) {
                            portsHtml += `
                                <div class="tooltip-separator"></div>
                                <div class="tooltip-row">
                                    <span class="tooltip-label">Traffic:</span>
                                    <span class="tooltip-value">IN: ${port.traffic.current_in || 'N/A'}, OUT: ${port.traffic.current_out || 'N/A'}</span>
                                </div>
                            `;
                        }
                    } else {
                        // Multiple ports
                        portsHtml = `
                            <div class="tooltip-separator"></div>
                            <div class="tooltip-row">
                                <span class="tooltip-label">Ports:</span>
                                <span class="tooltip-value">${speedInfo.ports.length} port(s)</span>
                            </div>
                        `;
                        speedInfo.ports.forEach((port, index) => {
                            portsHtml += `
                                <div class="tooltip-separator"></div>
                                <div class="tooltip-row">
                                    <span class="tooltip-label">Port ${port.port_number}:</span>
                                    <span class="tooltip-value">${port.port_speed}</span>
                                </div>
                            `;

                            // Add traffic information if available
                            if (port.traffic && (port.traffic.current_in || port.traffic.current_out)) {
                                portsHtml += `
                                    <div class="tooltip-separator"></div>
                                    <div class="tooltip-row">
                                        <span class="tooltip-label">Traffic:</span>
                                        <span class="tooltip-value">IN: ${port.traffic.current_in || 'N/A'}, OUT: ${port.traffic.current_out || 'N/A'}</span>
                                    </div>
                                `;
                            }
                        });
                    }
                }

                const tooltipContent = `
                    <div class="speed-tooltip">
                        <div class="tooltip-header">Connection Details</div>
                        <div class="tooltip-row">
                            <span class="tooltip-label">Parent:</span>
                            <span class="tooltip-value">${parentHost ? parentHost.hostname : arrowData.parent.ip}</span>
                        </div>
                        <div class="tooltip-separator"></div>
                        <div class="tooltip-row">
                            <span class="tooltip-label">Child:</span>
                            <span class="tooltip-value">${childHost ? childHost.hostname : arrowData.childIp}</span>
                        </div>
                        ${portsHtml}
                        <div class="tooltip-separator"></div>
                        <div class="tooltip-row">
                            <span class="tooltip-label">Type:</span>
                            <span class="tooltip-value">${arrowData.connectionType || 'Unknown'}</span>
                        </div>
                        <div class="tooltip-separator"></div>
                        <div class="tooltip-row">
                            <span class="tooltip-label">Source:</span>
                            <span class="tooltip-value">${sourceText}</span>
                        </div>
                    </div>
                `;

                // Create tooltip element
                let tooltip = d3.select("body").select(".speed-tooltip-container");
                if (tooltip.empty()) {
                    tooltip = d3.select("body").append("div")
                        .attr("class", "speed-tooltip-container")
                        .style("position", "absolute")
                        .style("z-index", "10000")
                        .style("display", "none");
                }

                tooltip.html(tooltipContent);
                positionTooltip(event, tooltip);
            }

            // Function to hide speed tooltip
            function hideSpeedTooltip() {
                const tooltip = d3.select("body").select(".speed-tooltip-container");
                if (!tooltip.empty()) {
                    tooltip.style("display", "none");
                }
            }

            // Function to update speed label visibility based on current filters
            function updateSpeedLabelVisibility() {
                speedLabelGroup.selectAll(".speed-label")
                    .style("display", d => {
                        const parentNode = hostData.find(h => h.id === d.parentId);
                        const childNode = hostData.find(h => h.ip === d.childIp);
                        if (!parentNode || !childNode) return "none";

                        // Check if both parent and child are visible (not filtered out)
                        const parentBubble = d3.select(`.host-bubble[data-id="${parentNode.id}"]`);
                        const childBubble = d3.select(`.host-bubble[data-id="${childNode.id}"]`);

                        const parentVisible = parentBubble.size() > 0 && parentBubble.style("display") !== "none";
                        const childVisible = childBubble.size() > 0 && childBubble.style("display") !== "none";

                        if (!parentVisible || !childVisible) return "none";

                        const dx = childNode.x - parentNode.x;
                        const dy = childNode.y - parentNode.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parentNode.size + 5;
                        const childPadding = childNode.size + 5;

                        return distance < parentPadding + childPadding ? "none" : "block";
                    });
            }

            // Make updateSpeedLabelVisibility globally available
            window.updateSpeedLabelVisibility = updateSpeedLabelVisibility;

            // Define hostBubbles, hostLabels, and arrows before using them in ticked
            const hostBubbles = hostBubbleGroup.selectAll(".host-bubble")
                .data(hostData)
                .enter()
                .append("circle")
                .attr("class", d => `host-bubble ${d.hostgroups[0].toLowerCase().replace(/\s+/g, '-')} ${d.apmStatus}`)
                .attr("data-id", d => d.id)
                .attr("r", d => d.size)
                .attr("cx", d => d.x)
                .attr("cy", d => d.y)
                .call(drag(simulation))
                .on("touchstart", function(event) {
                    // Prevent default only for drag operations
                    event.stopPropagation();
                })
                .on("mouseover", function () {
                    d3.select(this).transition().duration(200).attr("r", d => d.size * 1.2);
                })
                .on("mouseout", function () {
                    d3.select(this).transition().duration(200).attr("r", d => d.size);
                })
                .on("click", function (event, d) {
                    // Handle multi-select mode
                    if (isMultiSelectMode || event.ctrlKey || event.metaKey) {
                        event.preventDefault();
                        event.stopPropagation();
                        toggleHostSelection(d, this);
                        return;
                    }

                    // Clear any existing selection when clicking normally
                    if (selectedHosts.size > 0) {
                        clearSelection();
                    }

                    const ip = d.ip;
                    const subnet = d.subnet || urlParams.get('subnet');
                    const hostname = d.hostname;

                    // Show modal immediately with the hostname we already have
                    showModal(`host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(urlParams.get('infra'))}&hostip=${encodeURIComponent(ip)}&subnet=${encodeURIComponent(subnet)}`);

                    // Then resolve the real hostname asynchronously
                    getHostnameByIP(ip).then(realHostName => {
                        if (realHostName && realHostName !== hostname) {
                            // Update the iframe's src with the resolved hostname
                            const iframe = document.getElementById('modal-frame');
                            if (iframe) {
                                iframe.src = `host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(realHostName)}&infra=${encodeURIComponent(urlParams.get('infra'))}&hostip=${encodeURIComponent(ip)}&subnet=${encodeURIComponent(subnet)}`;
                            }
                        }
                    }).catch(error => {
                        console.error('Error resolving hostname:', error);
                        // Modal is already shown with hostname, so no further action needed
                    });
                });

            hostBubbles.append("title")
                .text(d => d.ip);

            const hostLabels = hostBubbleGroup.selectAll(".bubble-text")
                .data(hostData)
                .enter()
                .append("text")
                .attr("class", "bubble-text")
                .style("font-size", isMobile() ? "10px" : "12px")
                .attr("x", d => d.x)
                .attr("y", d => d.y + (isMobile() ? 3 : 5))
                .text(d => {
                    const maxLength = d.size * 1.6; // Adjust this factor based on your needs
                    if (d.hostname.length * 6 > maxLength) { // Approximate character width
                        return d.hostname.substring(0, Math.floor(maxLength / 6)) + "...";
                    }
                    return d.hostname;
                });

            // Now that hostBubbles, hostLabels, and arrows are defined, define the ticked function
            function ticked() {
                hostBubbles
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y);
                hostLabels
                    .attr("x", d => d.x)
                    .attr("y", d => d.y + (isMobile() ? 3 : 5));
                if (!hostgroup) {
                    bubbleGroup.selectAll(".group-bubble")
                        .attr("cx", d => d.x)
                        .attr("cy", d => d.y);
                    bubbleGroup.selectAll(".group-text")
                        .attr("x", d => d.x + d.textX)
                        .attr("y", d => d.y + d.textY);
                }
                hostData.forEach(d => {
                    const badge = hostBubbleGroup.select(`.badge-${d.id}`);
                    if (!badge.empty()) {
                        badge.attr("transform", `translate(${d.x + d.size * 0.7}, ${d.y - d.size * 0.7})`);
                    }
                });

                // Update arrow positions
                if (arrows) {
                    arrows.attr("d", d => {
                        const parentNode = hostData.find(h => h.id === d.parentId);
                        if (!parentNode) return "";

                        const childNode = hostData.find(h => h.ip === d.childIp);
                        if (!childNode) return "";

                        // Check if both parent and child are visible (not filtered out)
                        const parentSel = d3.select(`.host-bubble[data-id="${parentNode.id}"]`);
                        const childSel = d3.select(`.host-bubble[data-id="${childNode.id}"]`);
                        const parentVisible = parentSel.size() > 0 && parentSel.style("display") !== "none";
                        const childVisible = childSel.size() > 0 && childSel.style("display") !== "none";

                        if (!parentVisible || !childVisible) return "";

                        const dx = childNode.x - parentNode.x; // Direction from parent to child
                        const dy = childNode.y - parentNode.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parentNode.size + 5;
                        const childPadding = childNode.size + 5;

                        if (distance < parentPadding + childPadding) return "";

                        const angle = Math.atan2(dy, dx);
                        const startX = parentNode.x + Math.cos(angle) * parentPadding; // Start near parent
                        const startY = parentNode.y + Math.sin(angle) * parentPadding;
                        const endX = childNode.x - Math.cos(angle) * childPadding; // End near child
                        const endY = childNode.y - Math.sin(angle) * childPadding;

                        return `M${startX},${startY} L${endX},${endY}`;
                    });
                }

                // Update speed label positions
                speedLabelGroup.selectAll(".speed-label")
                    .attr("x", d => {
                        const parentNode = hostData.find(h => h.id === d.parentId);
                        const childNode = hostData.find(h => h.ip === d.childIp);
                        if (!parentNode || !childNode) return 0;

                        const dx = childNode.x - parentNode.x;
                        const dy = childNode.y - parentNode.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parentNode.size + 5;
                        const childPadding = childNode.size + 5;

                        if (distance < parentPadding + childPadding) return 0;

                        const angle = Math.atan2(dy, dx);
                        const midPoint = (distance - parentPadding - childPadding) / 2 + parentPadding;

                        // Position text along the arrow line with perpendicular offset
                        const baseX = parentNode.x + Math.cos(angle) * midPoint;

                        // Add perpendicular offset for multiple connections (above/below arrow)
                        const perpendicularAngle = angle + Math.PI / 2;
                        const offsetDistance = 12; // Distance above/below the arrow

                        return baseX + Math.cos(perpendicularAngle) * offsetDistance;
                    })
                    .attr("y", d => {
                        const parentNode = hostData.find(h => h.id === d.parentId);
                        const childNode = hostData.find(h => h.ip === d.childIp);
                        if (!parentNode || !childNode) return 0;

                        const dx = childNode.x - parentNode.x;
                        const dy = childNode.y - parentNode.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parentNode.size + 5;
                        const childPadding = childNode.size + 5;

                        if (distance < parentPadding + childPadding) return 0;

                        const angle = Math.atan2(dy, dx);
                        const midPoint = (distance - parentPadding - childPadding) / 2 + parentPadding;

                        // Position text along the arrow line with perpendicular offset
                        const baseY = parentNode.y + Math.sin(angle) * midPoint;

                        // Add perpendicular offset for multiple connections (above/below arrow)
                        const perpendicularAngle = angle + Math.PI / 2;
                        const offsetDistance = 12; // Distance above/below the arrow

                        return baseY + Math.sin(perpendicularAngle) * offsetDistance;
                    })
                    .attr("transform", d => {
                        const parentNode = hostData.find(h => h.id === d.parentId);
                        const childNode = hostData.find(h => h.ip === d.childIp);
                        if (!parentNode || !childNode) return "";

                        const dx = childNode.x - parentNode.x;
                        const dy = childNode.y - parentNode.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parentNode.size + 5;
                        const childPadding = childNode.size + 5;

                        if (distance < parentPadding + childPadding) return "";

                        const angle = Math.atan2(dy, dx);
                        const midPoint = (distance - parentPadding - childPadding) / 2 + parentPadding;

                        // Calculate text position with offset
                        const baseX = parentNode.x + Math.cos(angle) * midPoint;
                        const baseY = parentNode.y + Math.sin(angle) * midPoint;

                        const perpendicularAngle = angle + Math.PI / 2;
                        const offsetDistance = 12;

                        const textX = baseX + Math.cos(perpendicularAngle) * offsetDistance;
                        const textY = baseY + Math.sin(perpendicularAngle) * offsetDistance;

                        // Convert angle from radians to degrees
                        let angleDegrees = angle * (180 / Math.PI);

                        // Keep text readable by flipping it if it would be upside down
                        if (angleDegrees > 90 || angleDegrees < -90) {
                            angleDegrees += 180;
                        }

                        return `rotate(${angleDegrees}, ${textX}, ${textY})`;
                    })
                    .style("display", d => {
                        const parentNode = hostData.find(h => h.id === d.parentId);
                        const childNode = hostData.find(h => h.ip === d.childIp);
                        if (!parentNode || !childNode) return "none";

                        // Check if both parent and child are visible (not filtered out)
                        const parentBubble = d3.select(`.host-bubble[data-id="${parentNode.id}"]`);
                        const childBubble = d3.select(`.host-bubble[data-id="${childNode.id}"]`);

                        const parentVisible = parentBubble.size() > 0 && parentBubble.style("display") !== "none";
                        const childVisible = childBubble.size() > 0 && childBubble.style("display") !== "none";

                        if (!parentVisible || !childVisible) return "none";

                        const dx = childNode.x - parentNode.x;
                        const dy = childNode.y - parentNode.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parentNode.size + 5;
                        const childPadding = childNode.size + 5;

                        return distance < parentPadding + childPadding ? "none" : "block";
                    });
            }

            // Set the tick callback after defining ticked
            simulation.on("tick", ticked);

            function drag(sim) {
                let isDragging = false;

                function dragstarted(event, d) {
                    if (!event.active) sim.alphaTarget(0.3).restart();
                    d.fx = d.x;
                    d.fy = d.y;
                    isDragging = false;
                }

                function dragged(event, d) {
                    d.fx = event.x;
                    d.fy = event.y;
                    isDragging = true;

                    // If this host is selected and there are multiple selections, move all selected hosts
                    if (selectedHosts.has(d.id) && selectedHosts.size > 1) {
                        const dx = event.x - d.x;
                        const dy = event.y - d.y;

                        // Move all selected hosts together
                        hostData.forEach(host => {
                            if (selectedHosts.has(host.id) && host.id !== d.id) {
                                host.fx = host.x + dx;
                                host.fy = host.y + dy;
                            }
                        });
                        sim.alpha(0.3).restart();
                    }
                }

                function dragended(event, d) {
                    if (!event.active) sim.alphaTarget(0);
                    d.fx = null;
                    d.fy = null;

                    // Clear fx/fy for all selected hosts
                    if (selectedHosts.has(d.id) && selectedHosts.size > 1) {
                        hostData.forEach(host => {
                            if (selectedHosts.has(host.id)) {
                                host.fx = null;
                                host.fy = null;
                            }
                        });
                    }

                    if (isDragging) {
                        const targetGroup = detectGroupCollision(d);
                        if (targetGroup) {
                            // Check if we have multiple selected hosts
                            if (selectedHosts.size > 1 && selectedHosts.has(d.id)) {
                                handleMultipleHostMove(targetGroup);
                            } else {
                                // Single host move
                                const currentHostgroup = d.hostgroups[0];
                                const groupElement = d3.selectAll(".group-bubble")
                                    .filter(g => g.hostgroup === targetGroup.hostgroup)
                                    .node();
                                const isVisible = groupElement && window.getComputedStyle(groupElement).display !== "none";

                                if (targetGroup.hostgroup !== currentHostgroup && isVisible) {
                                    handleHostMove(d, targetGroup);
                                }
                            }
                        }
                    }
                }

                return d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended)
                    .touchable(true); // Explicitly enable touch support
            }

            // Handle multiple host move
            async function handleMultipleHostMove(targetGroup) {
                const selectedHostsData = hostData.filter(h => selectedHosts.has(h.id));
                const targetGroupName = targetGroup.hostgroup;
                const hostCount = selectedHostsData.length;

                // Show confirmation popup
                const confirmed = confirm(`Move ${hostCount} selected host(s) to hostgroup "${targetGroupName}"?`);
                if (!confirmed) return;

                try {
                    const hostGroupId = await getHostGroupIdByName(targetGroupName);
                    const ips = selectedHostsData.map(h => h.ip);

                    // Show loading animation
                    const loadingDiv = document.createElement('div');
                    loadingDiv.id = 'loading-animation';
                    loadingDiv.style.position = 'fixed';
                    loadingDiv.style.top = '50%';
                    loadingDiv.style.left = '50%';
                    loadingDiv.style.transform = 'translate(-50%, -50%)';
                    loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                    loadingDiv.style.color = 'white';
                    loadingDiv.style.padding = '20px';
                    loadingDiv.style.borderRadius = '8px';
                    loadingDiv.style.zIndex = '1000';
                    loadingDiv.innerHTML = `Moving ${hostCount} host(s)... <div class="loader" style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 10px auto;"></div>`;

                    const style = document.createElement('style');
                    style.textContent = `
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    `;
                    document.head.appendChild(style);
                    document.body.appendChild(loadingDiv);

                    const response = await fetch('move_host_to_group.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `ips=${encodeURIComponent(JSON.stringify(ips))}&hostGroupId=${encodeURIComponent(hostGroupId)}`
                    });

                    const result = await response.json();
                    if (result.success) {
                        // Show detailed results only if there were errors
                        if (result.details && result.details.errorCount > 0) {
                            const errorDetails = result.details.errors.join('\n');
                            alert(`${result.message}\n\nErrors:\n${errorDetails}`);
                        }
                        // Remove success alert - just clear selection and reload
                        clearSelection();
                        window.location.reload(); // Reload to reflect the changes
                    } else {
                        alert(`Error: ${result.message}`);
                    }
                } catch (error) {
                    console.error('Error moving hosts to group:', error);
                    alert('An error occurred while moving the hosts to the group');
                } finally {
                    const loadingDiv = document.getElementById('loading-animation');
                    if (loadingDiv) {
                        document.body.removeChild(loadingDiv);
                    }
                    const style = document.querySelector('style');
                    if (style && style.textContent.includes('@keyframes spin')) {
                        document.head.removeChild(style);
                    }
                }
            }

            // Handle the move logic with a confirmation popup (single host)
            async function handleHostMove(host, targetGroup) {
                const hostIp = host.ip;
                const hostGroupId = await getHostGroupIdByName(targetGroup.hostgroup);
                const hostName = host.hostname;
                const targetGroupName = targetGroup.hostgroup;

                // Show confirmation popup
                const confirmed = confirm(`Move host "${hostName}" to hostgroup "${targetGroupName}"?`);
                if (!confirmed) return;

                // Show loading animation
                const loadingDiv = document.createElement('div');
                loadingDiv.id = 'loading-animation';
                loadingDiv.style.position = 'fixed';
                loadingDiv.style.top = '50%';
                loadingDiv.style.left = '50%';
                loadingDiv.style.transform = 'translate(-50%, -50%)';
                loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                loadingDiv.style.color = 'white';
                loadingDiv.style.padding = '20px';
                loadingDiv.style.borderRadius = '8px';
                loadingDiv.style.zIndex = '1000';
                loadingDiv.innerHTML = 'Moving host... <div class="loader" style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 10px auto;"></div>';

                const style = document.createElement('style');
                style.textContent = `
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `;
                document.head.appendChild(style);
                document.body.appendChild(loadingDiv);

                try {
                    const response = await fetch('move_host_to_group.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `ip=${encodeURIComponent(hostIp)}&hostGroupId=${encodeURIComponent(hostGroupId)}`
                    });

                    const result = await response.json();
                    if (result.success) {
                        window.location.reload(); // Reload to reflect the change
                    } else {
                        alert(`Error: ${result.message}`);
                    }
                } catch (error) {
                    console.error('Error moving host to group:', error);
                    alert('An error occurred while moving the host to the group');
                } finally {
                    document.body.removeChild(loadingDiv);
                    document.head.removeChild(style);
                }
            }

            svg.on("dblclick.zoom", null);
            svg.on("dblclick", () => {
                // Use the new function to center on centroids
                if (!hostgroup) { // Only center on centroids if we are in the hostgroup view
                    centerAndZoomOnCentroids(hostgroups, svg, zoom);
                } else {
                    // If in single hostgroup view, just reset to identity or maybe fit all hosts?
                    // For now, let's stick to resetting for the single hostgroup view
                    svg.transition()
                       .duration(750)
                       .call(zoom.transform, d3.zoomIdentity);
                }
            });

            window.addEventListener("resize", () => {
                dimensions = updateDimensions();
                if (!hostgroup) assignCentroids();
                simulation.nodes(hostData).alpha(1).restart();
            });

            function animateBubbles() {
                hostBubbles.each(function (d) {
                    d3.select(this)
                        .transition()
                        .duration(2000 + Math.random() * 1000)
                        .ease(d3.easeSinInOut)
                        .attr("r", d => d.size * 1.1)
                        .transition()
                        .duration(2000 + Math.random() * 1000)
                        .ease(d3.easeSinInOut)
                        .attr("r", d => d.size)
                        .on("end", animateBubbles);
                });
            }
            //animateBubbles(); // Commented out to improve performance

            // Function to ensure all hosts have correct status classes
            async function ensureCorrectHostStatus(svg, hostData) {
                try {
                    const hostBubbles = svg.selectAll(".host-bubble").data(hostData);

                    for (const bubble of hostBubbles.nodes()) {
                        const d = d3.select(bubble).datum();
                        if (!d) continue; // Skip if no data is bound

                        // Skip hosts with apmStatus = 'ask' - they should remain as is until approved
                        if (d.apmStatus === 'ask') continue;

                        const hostname = d.hostname;
                        const ip = d.ip;

                        try {
                            // Check in two ways: first by IP (most reliable)
                            let realHostName = await getHostnameByIP(ip);

                            // If not found by IP, try by hostname
                            if (!realHostName) {
                                // Get host status directly - if 0, then it's not in Nagios
                                const hostStatus = await checkHostStatus(hostname);

                                if (hostStatus === 0) {
                                    // Not found in Nagios by either IP or hostname
                                    console.log(`Host ${hostname} (${ip}) not found in Nagios, marking as not-added`);
                                    d3.select(bubble).attr("class", "host-bubble not-added");
                                    await updateApmStatus(d.id, "not-added");

                                    // If the host was previously pending, make sure to refresh the class
                                    if (bubble.classList.contains("pending")) {
                                        bubble.classList.remove("pending");
                                        bubble.classList.add("not-added");
                                    }
                                }
                            }
                        } catch (error) {
                            console.error(`Error checking status for ${ip}: ${error.message}`);
                        }
                    }
                    console.log("Initial host status verification complete");
                } catch (error) {
                    console.error(`Error in ensureCorrectHostStatus: ${error.message}`);
                }
            }


            // Initial status update and one-time arrow rendering
            realTimeBubbleStatus(svg, hostData).then(() => {
                if (typeof updateHostAndServiceCounts === 'function') {
                    console.log("fetchHostsBubbles: Calling updateHostAndServiceCounts after realTimeBubbleStatus");
                    updateHostAndServiceCounts();
                }
            }).catch(error => {
                console.error("Error during initial realTimeBubbleStatus, counts may not update:", error);
            });

            // Check SPM availability first, then fetch relationships and render arrows
            checkSpmAvailability().then(available => {
                spmAvailable = available;
                // Update speed button visibility after SPM check
                if (typeof window.updateSpeedButtonVisibility === 'function') {
                    window.updateSpeedButtonVisibility();
                }
                return fetchParentChildRelationships(hostData);
            }).then(relationshipMap => {
                renderArrows(relationshipMap);
            });

            // Periodic refresh only for status
            setInterval(() => {
                // Initial status update and one-time arrow rendering
                realTimeBubbleStatus(svg, hostData).then(() => {
                    if (typeof updateHostAndServiceCounts === 'function') {
                        console.log("fetchHostsBubbles: Calling updateHostAndServiceCounts after realTimeBubbleStatus");
                        updateHostAndServiceCounts();
                    }
                }).catch(error => {
                    console.error("Error during initial realTimeBubbleStatus, counts may not update:", error);
                });
            }, 10000);

            // Detect if the host-bubble overlaps with any group-bubble
            function detectGroupCollision(host) {
                const hostX = host.x;
                const hostY = host.y;
                const hostRadius = host.size;

                const groupBubbles = d3.selectAll(".group-bubble").data();
                for (const group of groupBubbles) {
                    const groupX = group.x;
                    const groupY = group.y;
                    const groupRadius = group.radius;

                    const dx = hostX - groupX;
                    const dy = hostY - groupY;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < (hostRadius + groupRadius)) {
                        return group; // Return the group that the host collided with
                    }
                }
                return null; // No collision detected
            }

            // Helper function to get hostgroup ID by name
            async function getHostGroupIdByName(hostgroupName) {
                const hostGroups = await fetchAllHostGroups(); // Reuse existing function
                const group = hostGroups.find(hg => {
                    const [, alias] = hg.split('. ', 2);
                    return alias === hostgroupName;
                });
                if (group) {
                    const [id] = group.split('. ', 2);
                    return id;
                }
                throw new Error(`Hostgroup "${hostgroupName}" not found`);
            }
        })
        .catch(error => console.error('Error fetching JSON:', error));
}