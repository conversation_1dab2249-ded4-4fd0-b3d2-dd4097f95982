// Function to send request using fetch
async function sendRequest(url) {
    try {
        const response = await fetch(url, {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ timestamp: Date.now() }),
            keepalive: true,
        });
        if (response.ok) {
            console.log(`Request to ${url} sent successfully.`);
        } else {
            console.error(`Request to ${url} failed with status: ${response.status}`);
        }
    } catch (err) {
        console.error(`Request error for ${url}:`, err);
    }
}

// Send initial requests
console.log("Sending initial requests...");
sendRequest('execute_background_scan.php');
sendRequest('execute_service_scan.php');
sendRequest('src/update_apm_status.php');
sendRequest('execute_auto_scan.php');
//sendRequest('execute_netdisco_sync.php'); // Will now run via cron

// Set interval to send requests every 20 seconds
console.log("Setting up request intervals...");
setInterval(() => {
    sendRequest('execute_background_scan.php');
    sendRequest('execute_service_scan.php');
}, 20000);

// Set interval to send APM status update request every 10 seconds
setInterval(() => {
    sendRequest('src/update_apm_status.php');
}, 10000);

// Set interval to send auto scan request every 1 minute (60 seconds)
setInterval(() => {
    sendRequest('execute_auto_scan.php');
}, 60000);

// Set interval to send netdisco sync request every 2 minutes (120 seconds)
/* setInterval(() => {
    sendRequest('execute_netdisco_sync.php');
}, 120000); */