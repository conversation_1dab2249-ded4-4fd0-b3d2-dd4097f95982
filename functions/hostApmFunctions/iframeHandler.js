// Function to set up and configure iframes for performance graphs
function setupPerformanceIframe(iframe, containerId) {
    // Add class for styling
    iframe.classList.add('performance-iframe');
    
    // Function to show the loading spinner
    const showLoadingSpinner = () => {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const loadingOverlay = container.querySelector('.iframe-loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
            iframe.style.opacity = '0';
        }
    };
    
    // Function to hide the loading spinner
    const hideLoadingSpinner = () => {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const loadingOverlay = container.querySelector('.iframe-loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
            iframe.style.opacity = '1';
        }
    };
    
    // Show loading spinner whenever iframe src changes
    const originalSetAttribute = iframe.setAttribute;
    iframe.setAttribute = function(name, value) {
        if (name === 'src' && value !== this.src) {
            showLoadingSpinner();
        }
        return originalSetAttribute.call(this, name, value);
    };
    
    // Also intercept direct src property changes
    const srcDescriptor = Object.getOwnPropertyDescriptor(HTMLIFrameElement.prototype, 'src');
    Object.defineProperty(iframe, 'src', {
        set: function(value) {
            if (value !== this.src) {
                showLoadingSpinner();
            }
            return srcDescriptor.set.call(this, value);
        },
        get: function() {
            return srcDescriptor.get.call(this);
        }
    });
    
    iframe.addEventListener('load', function() {
        try {
            const iframeDocument = this.contentWindow.document;
            const navElement = iframeDocument.querySelector('nav');
            if (navElement) {
                navElement.style.display = 'none';
            }
            
            // Function to check for errors and hide container if needed
            const checkForErrors = () => {
                try {
                    // Check if the iframe content contains error messages
                    const iframeContent = iframeDocument.body.textContent || '';
                    const errorMessages = [
                        'Unable to Complete Request',
                        'Error: Could not retrieve performance data',
                        'No performance data available',
                        'Access denied',
                        'Failed to load data',
                        'Error loading performance data'
                    ];
                    
                    // Check for specific error messages
                    const hasError = errorMessages.some(errorMsg => iframeContent.includes(errorMsg));
                    
                    // Check for empty or minimal content (might be just whitespace or a tiny error message)
                    const isEmptyContent = iframeContent.trim().length < 50;
                    
                    // Check if there are any graph elements
                    const hasGraphElements = iframeDocument.querySelectorAll('img[src*="graph"], canvas, .chart, svg').length > 0;
                    
                    if (hasError || (isEmptyContent && !hasGraphElements)) {
                        // Hide the entire container if error is detected
                        const container = document.getElementById(containerId);
                        if (container) {
                            console.log(`Hiding performance graph container '${containerId}' due to error or empty content`);
                            container.style.display = 'none';
                            
                            // Immediately show the no performance data message
                            const noDataMsg = document.getElementById('no-performance-data');
                            if (noDataMsg) {
                                noDataMsg.style.display = 'block';
                            }
                        }
                        return true; // Error detected
                    }
                    
                    return false; // No error detected
                } catch (e) {
                    console.error('Error checking iframe content for errors:', e);
                    return false;
                }
            };
            
            // Check for errors immediately
            if (checkForErrors()) {
                hideLoadingSpinner(); // Still hide the spinner even if there's an error
                return; // Exit early if errors are found
            }
            
            // Add navigation links at the top of the iframe
            const addNavigationLinks = () => {
                try {
                    // Parse current URL to get the host parameter
                    const currentSrc = this.src;
                    const url = new URL(currentSrc);
                    const hostParam = url.searchParams.get('host') || 'BLESK';
                    
                    // Create navigation container
                    const navContainer = iframeDocument.createElement('div');
                    navContainer.className = 'navigation-links';
                    
                    // Define the navigation links
                    const links = [
                        { text: 'Overview', view: '' },
                        { text: '4 Hours', view: '0' },
                        { text: '25 Hours', view: '1' },
                        { text: 'One Week', view: '2' },
                        { text: 'One Month', view: '3' },
                        { text: 'One Year', view: '4' }
                    ];
                    
                    // Get current view (if any)
                    const currentView = url.searchParams.get('view') || '';
                    
                    // Build the navigation HTML
                    links.forEach(link => {
                        const a = iframeDocument.createElement('a');
                        a.className = 'multi0';
                        a.href = `/pnp4nagios/index.php/graph?host=${hostParam}${link.view ? `&view=${link.view}` : ''}`;
                        a.textContent = link.text;
                        a.style.marginRight = '10px';
                        a.style.color = 'var(--link, #0066cc)';
                        
                        // Highlight current view
                        if ((link.view === currentView) || (link.view === '' && currentView === '')) {
                            a.style.fontWeight = 'bold';
                            a.style.textDecoration = 'underline';
                        }
                        
                        // Add click handler
                        a.addEventListener('click', (e) => {
                            e.preventDefault();
                            
                            // Show loading spinner before changing iframe source
                            showLoadingSpinner();
                            
                            // Update iframe src with the new view
                            const newUrl = new URL(currentSrc);
                            if (link.view) {
                                newUrl.searchParams.set('view', link.view);
                            } else {
                                newUrl.searchParams.delete('view');
                            }
                            iframe.src = newUrl.toString();
                            
                            // Reset scroll position of parent modal to show the iframe from the top
                            scrollToIframe();
                        });
                        
                        navContainer.appendChild(a);
                        navContainer.appendChild(iframeDocument.createTextNode(' '));
                    });
                    
                    // Insert at the top of the body
                    if (iframeDocument.body.firstChild) {
                        iframeDocument.body.insertBefore(navContainer, iframeDocument.body.firstChild);
                    } else {
                        iframeDocument.body.appendChild(navContainer);
                    }
                } catch (e) {
                    console.error('Error adding navigation links:', e);
                }
            };
            
            // Add the navigation links
            addNavigationLinks();
            
            // Add custom styles to the iframe content
            const styleElement = iframeDocument.createElement('style');
            
            // Detect theme by checking the stylesheet link
            const isLightTheme = () => {
                const styleLinks = document.querySelectorAll('link[rel="stylesheet"]');
                for (const link of styleLinks) {
                    if (link.href && link.href.includes('light-theme')) {
                        return true;
                    }
                }
                return false;
            };
            const textColor = isLightTheme() ? '#333' : '#f8f9fa';
            
            styleElement.textContent = `
                body { 
                    padding-top: 0 !important; 
                    margin: 0 !important;
                    width: 100% !important;
                    overflow-x: hidden !important;
                    background: var(--background);
                    color: ${textColor} !important;
                }
                .container { 
                    padding-top: 10px !important; 
                    max-width: 100% !important;
                    padding-left: 5px !important;
                    padding-right: 5px !important;
                    width: auto !important;
                    transform-origin: 0 0;
                }
                nav { display: none !important; }
                .ui-corner-all{
                    background: var(--background) !important;
                    color: ${textColor} !important;
                }
                .p4{
                    background: var(--surface) !important;
                    border: none !important;
                }
                .ui-widget-header{
                    background: var(--background) !important;
                    border: none !important;
                }
                
                /* Add specific styling for links with "Availability report" title */
                a[title="Availability report for this time range"] {
                    margin-left: 4px !important;
                    margin-right: 4px !important;
                }
                
                /* Add stronger text color overrides */
                body, body *, .ui-widget, .ui-widget *, div, span, p, a, h1, h2, h3, h4, h5, h6, td, th {
                    color: ${textColor} !important;
                }
                /* Force all text elements to use the correct color */
                [style*="color"] {
                    color: ${textColor} !important;
                }
                
                /* Navigation links styling */
                .navigation-links {
                    padding: 10px 5px;
                    margin-bottom: 10px;
                    border-bottom: 1px solid var(--border, #ccc);
                    background: var(--background);
                }
                .navigation-links a {
                    display: inline-block;
                    margin-right: 10px;
                    text-decoration: none;
                }
                .navigation-links a:hover {
                    text-decoration: underline;
                }
                
                /* Hide the specified elements */
                .p4.ui-widget-header.ui-corner-all { display: none !important; }
                td div.right.ui-widget-content.ui-corner-all { display: none !important; }
                td:has(> div.right.ui-widget-content.ui-corner-all) { display: none !important; }
                
                /* Hide all 4 action buttons in table cells */
                td:has(> a[href*="summary.cgi"]) { display: none !important; }
                td:has(> a[href*="avail.cgi"]) { display: none !important; }
                td:has(> span#basket_action_add) { display: none !important; }
                td:has(> a[href*="javascript:Gzoom"]) { display: none !important; }
                
                /* Auto-scale content if it overflows */
                @media (max-width: 100%) {
                    html, body {
                        transform-origin: 0 0;
                        transform: scale(0.95);
                        width: 105.26% !important; /* 100/0.95 to compensate for scale */
                    }
                }
                
                /* Safari-specific iframe fixes */
                html, body {
                    -webkit-transform: none !important;
                    -webkit-transform-origin: 0 0 !important;
                    min-height: 100px !important;
                    min-width: 100px !important;
                }
                
                /* Prevent horizontal scrollbar */
                ::-webkit-scrollbar {
                    height: 0 !important;
                    width: 6px;
                }
            `;
            
            iframeDocument.head.appendChild(styleElement);
            
            // Function to check width and scale if needed
            const checkWidth = () => {
                try {
                    const contentWidth = iframeDocument.scrollWidth || iframeDocument.body.scrollWidth;
                    const frameWidth = this.clientWidth;
                    
                    if (contentWidth > frameWidth) {
                        const scale = frameWidth / (contentWidth + 20); // Add padding
                        iframeDocument.body.style.transform = `scale(${scale})`;
                        iframeDocument.body.style.transformOrigin = '0 0';
                        iframeDocument.body.style.width = `${100 / scale}%`;
                    }
                    
                    // Adjust iframe height to match content
                    adjustIframeHeight();
                } catch (e) {
                    console.error('Error scaling iframe content:', e);
                }
            };
            
            // Function to adjust iframe height
            const adjustIframeHeight = () => {
                try {
                    // Calculate the total height of the content
                    const bodyHeight = iframeDocument.body.scrollHeight;
                    const scale = parseFloat(iframeDocument.body.style.transform?.match(/scale\((.*?)\)/)?.[1] || 1);
                    
                    // Set the iframe height to match content with a small buffer
                    const newHeight = (bodyHeight * scale) + 20; // Increased buffer
                    this.style.height = `${newHeight}px`;
                    
                    // After adjusting iframe height, check if we need to adjust modal scrolling
                    const modalBody = document.getElementById('modal-body');
                    if (modalBody) {
                        // Force modal body layout update
                        modalBody.style.display = 'none';
                        modalBody.offsetHeight; // Force reflow
                        modalBody.style.display = '';
                    }
                } catch (e) {
                    console.error('Error adjusting iframe height:', e);
                }
            };
            
            // Function to scroll parent modal to show iframe from the top
            const scrollToIframe = () => {
                try {
                    // Get the modal body that contains the iframe container
                    const modalBody = document.getElementById('modal-body');
                    if (modalBody) {
                        // Scroll the modal body to the top
                        modalBody.scrollTop = 0;
                    }
                } catch (e) {
                    console.error('Error scrolling to iframe:', e);
                }
            };
            
            // Check immediately and after a short delay
            checkWidth();
            setTimeout(checkWidth, 500);
            
            // Create a MutationObserver to watch for content changes
            const resizeObserver = new MutationObserver(() => {
                adjustIframeHeight();
                
                // Check for errors again when content changes
                checkForErrors();
            });
            
            // Start observing the iframe body for changes
            resizeObserver.observe(iframeDocument.body, { 
                childList: true,
                subtree: true,
                attributes: true
            });
            
            // Add event listeners for all links in the iframe content to detect navigation
            try {
                // Find all internal links in the iframe
                const internalLinks = iframeDocument.querySelectorAll('a[href]:not([href^="http"]):not([href^="//"]):not([target="_blank"])');
                
                // Add click handler to each link
                internalLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        // Show loading spinner when internal link is clicked
                        showLoadingSpinner();
                        
                        // Schedule height adjustment after navigation occurs
                        setTimeout(() => {
                            adjustIframeHeight();
                            scrollToIframe();
                        }, 1000);
                    });
                });
            } catch (e) {
                console.error('Error adding link handlers in iframe:', e);
            }
            
            // Intercept iframe history navigation
            try {
                if (this.contentWindow) {
                    const originalPushState = this.contentWindow.history.pushState;
                    const originalReplaceState = this.contentWindow.history.replaceState;
                    
                    // Override pushState
                    this.contentWindow.history.pushState = function() {
                        const result = originalPushState.apply(this, arguments);
                        showLoadingSpinner();
                        
                        // Re-adjust height after state change
                        setTimeout(() => {
                            adjustIframeHeight();
                            scrollToIframe();
                        }, 1000);
                        
                        return result;
                    };
                    
                    // Override replaceState
                    this.contentWindow.history.replaceState = function() {
                        const result = originalReplaceState.apply(this, arguments);
                        showLoadingSpinner();
                        
                        // Re-adjust height after state change
                        setTimeout(() => {
                            adjustIframeHeight();
                            scrollToIframe();
                        }, 1000);
                        
                        return result;
                    };
                }
            } catch (e) {
                console.error('Error intercepting iframe history:', e);
            }
            
            // Hide the loading overlay and show the iframe after content is loaded and styled
            setTimeout(() => {
                // More specific selector to target only this iframe's overlay
                const container = document.getElementById(containerId);
                if (!container) return;
                
                // Do a final error check after a delay (for async content)
                if (!checkForErrors()) {
                    hideLoadingSpinner();
                    // Ensure iframe height is correct after fully loaded
                    adjustIframeHeight();
                    // Scroll to show iframe from the top
                    scrollToIframe();
                }
            }, 1000); // Longer delay to ensure async content is loaded
            
            // One final check after all content should be loaded
            setTimeout(() => {
                checkForErrors();
                adjustIframeHeight();
            }, 3000);
        } catch (e) {
            console.error('Could not modify iframe content due to same-origin policy:', e);
            // Hide the loading overlay even if there's an error
            hideLoadingSpinner();
        }
    });
    
    // Initialize by showing loading spinner
    showLoadingSpinner();
}

// Function to create iframe container for performance graphs
function createIframeContainer(containerId, fullUrl) {
    // Detect theme by checking the stylesheet link
    const isLightTheme = () => {
        const styleLinks = document.querySelectorAll('link[rel="stylesheet"]');
        for (const link of styleLinks) {
            if (link.href && link.href.includes('light-theme')) {
                return true;
            }
        }
        return false;
    };
    const textColor = isLightTheme() ? '#333' : '#f8f9fa';
    
    // Check if we're on a mobile or tablet device
    const isMobileOrTablet = window.innerWidth <= 1024;
    
    const iframeContainer = document.createElement('div');
    iframeContainer.id = containerId;
    
    // For mobile/tablet devices, we'll only create the desktop message, not the iframe
    if (isMobileOrTablet) {
        iframeContainer.innerHTML = `
            <h3>Performance Graphs</h3>
            <div class="desktop-only-message" style="display: block;">
                <p><i class="fa fa-desktop fa-lg"></i> Please use desktop version to view performance graphs.</p>
            </div>
        `;
    } else {
        // For desktop, create the full iframe container with loading spinner
        iframeContainer.innerHTML = `
            <h3>Performance Graphs</h3>
            <div class="iframe-container">
                <div class="iframe-loading-overlay">
                    <div class="spinner"></div>
                </div>
                <iframe id="${containerId}-iframe" src="${fullUrl}" width="100%" frameborder="0" scrolling="no" style="overflow:hidden;"></iframe>
            </div>
            <style>
                .iframe-container {
                    position: relative;
                    max-width: 98%;
                    border-radius: var(--radius);
                    overflow: hidden;
                    margin-top: 10px;
                    border: none;
                }
                iframe.performance-iframe {
                    background: var(--background);
                    color: ${textColor} !important;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    width: 100%;
                    box-sizing: border-box;
                }
                .iframe-loading-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: var(--background);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10;
                }
                .spinner {
                    width: 50px;
                    height: 50px;
                    border: 5px solid rgba(0, 0, 0, 0.1);
                    border-radius: 50%;
                    border-top-color: var(--border);
                    animation: spin 1s ease-in-out infinite;
                }
                @keyframes spin {
                    to { transform: rotate(360deg); }
                }
            </style>
        `;
    }
    
    return iframeContainer;
} 