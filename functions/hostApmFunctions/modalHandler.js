// Global variable to keep track of the currently opened modal context
window.currentModalContext = null;

// Function to populate the host modal with details
function populateHostModal(host, hostStats, statuses, statusEntry, statusDatInfo = null) {
    const modal = document.getElementById('service-modal');
    const modalBody = document.getElementById('modal-body');
    
    // Use status.dat info if provided, otherwise use hostStats info
    const pluginOutput = statusDatInfo ? statusDatInfo.plugin_output : hostStats.plugin_output;
    const longPluginOutput = statusDatInfo ? statusDatInfo.long_plugin_output : '';
    
    // Create tab navigation
    const tabNav = `
    <div class="tab-navigation">
        <button class="tab-button active" data-tab="info"><i class="fa fa-info-circle"></i> Host Info</button>
        <button class="tab-button" data-tab="comments"><i class="fa fa-comments"></i> Comments</button>
        <button class="tab-button" data-tab="availability"><i class="fa fa-pie-chart"></i> Availability</button>
        ${host.action_url ? `<button class="tab-button" data-tab="performance"><i class="fa fa-area-chart"></i> Performance</button>` : ''}
    </div>
    `;
    
    // Create info tab content
    const infoTabContent = `
    <div class="tab-content active" id="tab-info">
        <p><strong>Status:</strong> <span style="${getStyle(statusEntry.class)}">${statusEntry.text}</span></p>
        ${pluginOutput ? `<p><strong>Details:</strong> ${pluginOutput}</p>` : ''}
        ${longPluginOutput ? `<p><strong>Additional Details:</strong> ${longPluginOutput}</p>` : ''}
        <p><strong>Host Name:</strong> ${host.name}</p>
        <p><strong>Display Name:</strong> ${host.display_name}</p>
        <p><strong>Alias:</strong> ${host.alias}</p>
        <p><strong>Address:</strong> ${host.address}</p>
        <p><strong>Duration in Current State:</strong> ${calculateDuration(hostStats.last_state_change)}</p>
        <p><strong>Last Check:</strong> ${hostStats.last_check && hostStats.last_check !== 0 ? new Date(hostStats.last_check).toLocaleString() : 'N/A'}</p>
        <p><strong>Attempt:</strong> ${hostStats.current_attempt} / ${hostStats.max_attempts}</p>
        <p><strong>Is flapping?:</strong> <span style="${getStyle(hostStats.is_flapping ? 'critical' : 'ok')}">${hostStats.is_flapping ? 'YES' : 'NO'}</span> ${hostStats.percent_state_change}% state change</p>
        <p><strong>In Scheduled Downtime?:</strong> <span style="${getStyle(hostStats.scheduled_downtime_depth === 0 ? 'ok' : 'critical')}">${hostStats.scheduled_downtime_depth === 0 ? 'NO' : 'YES'}</span></p>
        <br>
        <div class="modal-grid">
            <div>
                ${generateStatusRow('ACTIVE CHECKS', statuses.activeChecks, true, host.name)}
                ${generateStatusRow('PASSIVE CHECKS', statuses.passiveChecks, true, host.name)}
                ${generateStatusRow('OBSESSING', statuses.obsessing, true, host.name)}
            </div>
            <div>
                ${generateStatusRow('NOTIFICATIONS', statuses.notifications, true, host.name)}
                ${generateStatusRow('EVENT HANDLER', statuses.eventHandler, true, host.name)}
                ${generateStatusRow('FLAP DETECTION', statuses.flapDetection, true, host.name)}
            </div>
        </div>
    </div>
    `;
    
    // Create comments tab content
    const commentsTabContent = `
    <div class="tab-content" id="tab-comments">
        <div id="host-comment-grid">
            <h3>Comments</h3>
            <table class="comments-table">
                <tbody>
                    <tr>
                        <td class="comment">
                            <i class="fa fa-comments-o fa-lg"></i> 
                            <a href="#" onclick="openCommandModal('https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi?cmd_typ=1&host=${host.name}', 'Add Comment'); return false;" class="comment">Add a new comment</a>
                        </td>
                        <td class="comment">
                            <i class="fa fa-trash-o fa-lg"></i> 
                            <a href="#" onclick="openCommandModal('https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi?cmd_typ=20&host=${host.name}', 'Delete All Comments'); return false;" class="comment">Delete all comments</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    `;
    
    // Create availability tab content
    const availabilityTabContent = `
    <div class="tab-content" id="tab-availability">
        <div class="availability-header" style="margin-bottom:6px;">
            <h3 id="host-availability-title" style="margin:0; font-size:16px;">Availability</h3>
            <div class="availability-controls">
                <input type="datetime-local" id="host-availability-start" title="Start time">
                <input type="datetime-local" id="host-availability-end" title="End time">
                <button id="host-availability-refresh" class="availability-refresh" title="Refresh availability graph"><i class="fa fa-refresh" aria-hidden="true"></i></button>
            </div>
        </div>
        <div id="host-availability-chart" class="availability-chart"></div>
    </div>
    `;
    
    // Create performance tab content if action_url exists - just a placeholder container
    let performanceTabContent = '';
    if (host.action_url) {
        performanceTabContent = `
        <div class="tab-content" id="tab-performance">
            <div id="host-performance-container" style="display: block;"></div>
            <div id="no-performance-data" class="no-performance-data" style="display: none;">
                <p><i class="fa fa-area-chart fa-lg"></i> No performance data available for this host.</p>
            </div>
            <div class="desktop-only-message">
                <p><i class="fa fa-desktop fa-lg"></i> Please use desktop version to view performance graphs.</p>
            </div>
        </div>
        `;
    }
    
    // Apply initial content (now includes availability tab)
    modalBody.innerHTML = tabNav + infoTabContent + commentsTabContent + availabilityTabContent + performanceTabContent;
    
    // Get the initial height of the info tab
    const infoTabHeight = document.getElementById('tab-info').clientHeight;
    const minHeight = Math.max(infoTabHeight, 300); // Ensure minimum height is reasonable
    
    // Apply minimum height to all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.style.minHeight = `${minHeight}px`;
    });
    
    // Store host info for lazy loading
    if (host.action_url) {
        // Just set a flag that performance graphs are available
        // Not using the actual action_url as the iframe source
        window.hostPerformanceUrl = true;
        window.performanceHostName = host.name;
        window.performanceServiceName = null;
    }
    
    // Store host info for availability
    window.hostAvailabilityHostName = host.name;
    
    // Add tab switching functionality
    setupTabNavigation();
    
    // Fetch comments
    fetchNagiosComments(host.name, "host-comment-grid");
    
    modal.style.display = 'flex';
    document.getElementById('modal-options').style.display = 'flex';
    const modalDelete = document.getElementById('modal-delete');
    modalDelete.style.display = 'flex';
    modalDelete.onclick = () => {
        blacklistHostApm(host.name);
    };

    // Setup refresh icon
    const modalRefresh = document.getElementById('modal-refresh');
    if (modalRefresh) {
        modalRefresh.style.display = 'flex';
        modalRefresh.onclick = refreshModalContent;
    }

    // Store current context for refresh functionality
    window.currentModalContext = { type: 'host', hostname: host.name };
    document.body.style.overflow = "hidden";
}

// Function to populate the service modal with details
async function populateServiceModal(service, statusDatInfo = null) {
    const modal = document.getElementById('service-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');
    const statuses = {
        status: statusMap[service.status],
        isFlapping: service.is_flapping ? { text: 'YES', class: 'critical' } : { text: 'NO', class: 'ok' },
        inDowntime: service.scheduled_downtime_depth === 0 ? { text: 'NO', class: 'ok' } : { text: 'YES', class: 'critical' },
        activeChecks: service.checks_enabled ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
        passiveChecks: service.accept_passive_checks ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
        obsessing: service.obsess ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
        notifications: service.notifications_enabled ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
        eventHandler: service.event_handler_enabled ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
        flapDetection: service.flap_detection_enabled ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' }
    };

    // Use status.dat info if provided, otherwise use service object info
    const pluginOutput = statusDatInfo ? statusDatInfo.plugin_output : service.plugin_output;
    const longPluginOutput = statusDatInfo ? statusDatInfo.long_plugin_output : service.long_plugin_output;
    


    modalTitle.textContent = service.description;
    
    // Get the service-specific action_url if available
    let serviceActionUrl = null;
    try {
        // Check if we already have the action_url in the service object
        if (service.action_url) {
            serviceActionUrl = service.action_url;
        } else {
            // Otherwise fetch it from the object API
            const encodedServiceName = encodeURIComponent(service.description);
            const serviceObjectUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=service&hostname=${realHostName}&servicedescription=${encodedServiceName}`;
            
            const response = await fetch(serviceObjectUrl, { credentials: 'include' });
            if (response.ok) {
                const serviceObjectInfo = await response.json();
                if (serviceObjectInfo.result.type_text === 'Success' && 
                    serviceObjectInfo.data.service && 
                    serviceObjectInfo.data.service.action_url) {
                    serviceActionUrl = serviceObjectInfo.data.service.action_url;
                }
            }
        }
    } catch (error) {
        console.error('Error fetching service action URL:', error);
    }
    
    // Create tab navigation
    const tabNav = `
    <div class="tab-navigation">
        <button class="tab-button active" data-tab="info"><i class="fa fa-info-circle"></i> Service Info</button>
        <button class="tab-button" data-tab="comments"><i class="fa fa-comments"></i> Comments</button>
        <button class="tab-button" data-tab="availability"><i class="fa fa-pie-chart"></i> Availability</button>
        ${serviceActionUrl ? `<button class="tab-button" data-tab="performance"><i class="fa fa-area-chart"></i> Performance</button>` : ''}
    </div>
    `;
    
    // Friendly display for last check / duration (handle pending services and invalid timestamps)
    const lastCheckDisplay = (service.last_check && service.last_check !== 0 && !isNaN(service.last_check)) ? new Date(service.last_check).toLocaleString() : 'N/A';
    const durationDisplay  = (service.last_state_change && service.last_state_change !== 0 && !isNaN(service.last_state_change)) ? calculateDuration(service.last_state_change) : 'N/A';

    // Create info tab content
    const infoTabContent = `
    <div class="tab-content active" id="tab-info">
        <p><strong>Status:</strong> <span style="${getStyle(statuses.status.class)}">${statuses.status.text}</span></p>
        <p><strong>Status Information:</strong> ${pluginOutput || 'N/A'}</p>
        ${longPluginOutput ? `<p><strong>Details:</strong> ${longPluginOutput}</p>` : ''}
        <p><strong>Duration in Current State:</strong> ${durationDisplay}</p>
        <p><strong>Last Check:</strong> ${lastCheckDisplay}</p>
        <p><strong>Attempt:</strong> ${service.current_attempt} / ${service.max_attempts}</p>
        <p><strong>Is This Service Flapping?</strong> <span style="${getStyle(statuses.isFlapping.class)}">${statuses.isFlapping.text}</span> ${service.percent_state_change}% state change</p>
        <p><strong>In Scheduled Downtime?</strong> <span style="${getStyle(statuses.inDowntime.class)}">${statuses.inDowntime.text}</span></p>
        <br>
        <div class="modal-grid">
            <div>
                ${generateStatusRow('ACTIVE CHECKS', statuses.activeChecks, false, realHostName, service.description)}
                ${generateStatusRow('PASSIVE CHECKS', statuses.passiveChecks, false, realHostName, service.description)}
                ${generateStatusRow('OBSESSING', statuses.obsessing, false, realHostName, service.description)}
            </div>
            <div>
                ${generateStatusRow('NOTIFICATIONS', statuses.notifications, false, realHostName, service.description)}
                ${generateStatusRow('EVENT HANDLER', statuses.eventHandler, false, realHostName, service.description)}
                ${generateStatusRow('FLAP DETECTION', statuses.flapDetection, false, realHostName, service.description)}
            </div>
        </div>
    </div>
    `;
    
    // Create comments tab content
    const commentsTabContent = `
    <div class="tab-content" id="tab-comments">
        <div id="host-comment-grid">
            <h3>Comments</h3>
            <table class="comments-table">
                <tbody>
                    <tr>
                        <td class="comment">
                            <i class="fa fa-comments-o fa-lg"></i>&nbsp;
                            <a href="#" onclick="openCommandModal('https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi?cmd_typ=3&host=${realHostName}&service=${encodeURIComponent(service.description)}', 'Add Comment'); return false;" class="comment">Add a new comment</a>
                        </td>
                        <td class="comment">
                            <i class="fa fa-trash-o fa-lg"></i>&nbsp;
                            <a href="#" onclick="openCommandModal('https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi?cmd_typ=21&host=${realHostName}&service=${encodeURIComponent(service.description)}', 'Delete All Comments'); return false;" class="comment">Delete all comments</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    `;
    
    // Create availability tab content (placed before performance content)
    const availabilityTabContent = `
    <div class="tab-content" id="tab-availability">
        <div class="availability-header" style="margin-bottom:6px;">
            <h3 id="svc-availability-title" style="margin:0; font-size:16px;">Availability</h3>
            <div class="availability-controls">
                <input type="datetime-local" id="svc-availability-start" title="Start time">
                <input type="datetime-local" id="svc-availability-end" title="End time">
                <button id="svc-availability-refresh" class="availability-refresh" title="Refresh availability graph"><i class="fa fa-refresh" aria-hidden="true"></i></button>
            </div>
        </div>
        <div id="svc-availability-chart" class="availability-chart"></div>
    </div>
    `;
    
    // Create performance tab content if we have a service action URL
    let performanceTabContent = '';
    if (serviceActionUrl) {
        performanceTabContent = `
        <div class="tab-content" id="tab-performance">
            <div id="service-performance-container" style="display: block;"></div>
            <div id="no-performance-data" class="no-performance-data" style="display: none;">
                <p><i class="fa fa-area-chart fa-lg"></i> No performance data available for this service.</p>
            </div>
            <div class="desktop-only-message">
                <p><i class="fa fa-desktop fa-lg"></i> Please use desktop version to view performance graphs.</p>
            </div>
        </div>
        `;
    }
    
    // Apply initial content (now includes availability tab)
    modalBody.innerHTML = tabNav + infoTabContent + commentsTabContent + availabilityTabContent + performanceTabContent;
    
    // Get the initial height of the info tab
    const infoTabHeight = document.getElementById('tab-info').clientHeight;
    const minHeight = Math.max(infoTabHeight, 300); // Ensure minimum height is reasonable
    
    // Apply minimum height to all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.style.minHeight = `${minHeight}px`;
    });
    
    // Store global references so other helpers know the current context
    window.hostPerformanceUrl = !!serviceActionUrl;
    window.performanceHostName = realHostName;
    window.performanceServiceName = serviceActionUrl ? service.description : null;
    window.serviceAvailabilityHostName = realHostName;
    window.serviceAvailabilityServiceName = service.description;
    
    // Add tab switching functionality
    setupTabNavigation();
    
    // Fetch comments
    fetchNagiosComments(realHostName, "host-comment-grid", service.description);

    modal.style.display = 'flex';
    document.getElementById('modal-options').style.display = 'flex';
    const modalDelete = document.getElementById('modal-delete');
    modalDelete.style.display = 'flex';
    modalDelete.onclick = () => {
        callDeleteService(urlParams.get('hostip'), service.description);
    };

    // Setup refresh icon
    const modalRefresh = document.getElementById('modal-refresh');
    if (modalRefresh) {
        modalRefresh.style.display = 'flex';
        modalRefresh.onclick = refreshModalContent;
    }

    // Store current context for refresh functionality
    window.currentModalContext = { type: 'service', hostname: realHostName, serviceName: service.description };
    document.body.style.overflow = "hidden";
}

// Function to set up tab navigation
function setupTabNavigation() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    let currentTab = 'info';
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            
            // Skip if already on this tab
            if (tabId === currentTab) return;
            
            // Remove active class from all buttons
            tabButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            button.classList.add('active');
            
            // Get all tab contents first
            const allTabContents = document.querySelectorAll('.tab-content');
            
            // Hide all tab contents without removing active class first
            allTabContents.forEach(content => {
                content.style.display = 'none';
            });
            
            // Show the corresponding tab content
            const activeTab = document.getElementById(`tab-${tabId}`);
            activeTab.style.display = 'block';
            
            // Then add active class to prevent layout shift
            allTabContents.forEach(content => {
                content.classList.remove('active');
            });
            activeTab.classList.add('active');
            
            // If we're switching to the performance tab, load it (every time)
            if (tabId === 'performance') {
                loadPerformanceTab();
            } else if (tabId === 'availability') {
                // Check if this is a host or service modal
                if (window.currentModalContext && window.currentModalContext.type === 'host') {
                    loadHostAvailabilityTab();
                } else {
                    loadServiceAvailabilityTab();
                }
            }
            
            // Update current tab
            currentTab = tabId;
        });
    });
}

// Function to load the performance tab
function loadPerformanceTab() {
    // If there's no hostPerformanceUrl flag set, show "no performance data" message
    if (!window.hostPerformanceUrl) {
        // Show the no performance data message if there's no action URL
        const noDataMsg = document.getElementById('no-performance-data');
        if (noDataMsg) {
            noDataMsg.style.display = 'block';
        }
        return;
    }
    
    // Check if we're on a mobile or tablet device
    const isMobileOrTablet = window.innerWidth <= 1024;
    
    // For mobile/tablet devices, we just show the desktop-only message and skip iframe loading
    if (isMobileOrTablet) {
        // Hide the "no performance data" message since we'll show the desktop-only message
        const noDataMsg = document.getElementById('no-performance-data');
        if (noDataMsg) {
            noDataMsg.style.display = 'none';
        }
        
        // Ensure the desktop-only message is visible
        const desktopMsg = document.querySelector('.desktop-only-message');
        if (desktopMsg) {
            desktopMsg.style.display = 'block';
        }
        
        return;
    }
    
    // Determine container ID and create URL based on whether it's a host or service
    let containerId, fullUrl;
    
    // Always create the URL using the standard PNP4Nagios structure
    // NOT using the action_url directly, only as a flag
    if (window.performanceServiceName) {
        // It's a service
        containerId = 'service-performance-container';
        
        // Base PNP4Nagios URL
        const baseUrl = '/pnp4nagios/index.php/graph';
        
        // Build URL with host and service parameters
        fullUrl = `${baseUrl}?host=${encodeURIComponent(window.performanceHostName)}&srv=${encodeURIComponent(window.performanceServiceName)}`;
    } else {
        // It's a host
        containerId = 'host-performance-container';
        
        // Base PNP4Nagios URL
        const baseUrl = '/pnp4nagios/index.php/graph';
        
        // Build URL with just host parameter
        fullUrl = `${baseUrl}?host=${encodeURIComponent(window.performanceHostName)}`;
    }
    
    // Add the domain
    fullUrl = `https://${window.location.hostname}${fullUrl}`;
    
    // Get container and clear previous content
    const container = document.getElementById(containerId);
    if (!container) return;
    
    // Clear any existing content first
    container.innerHTML = '';
    
    // Create the iframe container
    const iframeContainer = createIframeContainer(containerId, fullUrl);
    container.appendChild(iframeContainer);
    
    // Check if an iframe was created (won't be created on mobile)
    const iframe = document.getElementById(`${containerId}-iframe`);
    if (iframe) {
        // Add event listener to hide the nav element inside the iframe
        setupPerformanceIframe(iframe, containerId);
    }
}

// Function to load the service availability tab
function loadServiceAvailabilityTab() {
    try {
        const host = window.serviceAvailabilityHostName;
        const serviceName = window.serviceAvailabilityServiceName;
        if (!host || !serviceName) return;

        const startInput  = document.getElementById('svc-availability-start');
        const endInput    = document.getElementById('svc-availability-end');
        const refreshBtn  = document.getElementById('svc-availability-refresh');
        const chartDiv    = document.getElementById('svc-availability-chart');
        const titleEl     = document.getElementById('svc-availability-title');

        if (!startInput || !endInput || !refreshBtn || !chartDiv) return;

        // Helpers
        const toLocalIso = (dateObj) => {
            const tzOffset = dateObj.getTimezoneOffset() * 60000;
            return new Date(dateObj.getTime() - tzOffset).toISOString().slice(0,16);
        };
        const getCssVar = (name) => getComputedStyle(document.documentElement).getPropertyValue(name) || '#ccc';
        const formatDuration = (sec) => {
            const h = Math.floor(sec / 3600);
            const m = Math.floor((sec % 3600) / 60);
            const s = sec % 60;
            return `${h}h ${m}m ${s}s`;
        };
        const getStateColor = (stateVal) => {
            // Accept either numeric codes (0-3) or enumerated text (ok, warning, critical, unknown)
            const map = {
                ok: '--success',
                warning: '--warning',
                critical: '--critical',
                unknown: '--unknown'
            };
            const cssVar = map[stateVal] || map[String(stateVal).toLowerCase()] || '--pending';
            return getCssVar(cssVar);
        };

        // Track which sub-tab is active (summary | trends)
        let activeTab = 'summary';

        // Initialise default dates if empty
        if (!startInput.value || !endInput.value) {
            const now = new Date();
            endInput.value = toLocalIso(now);
            startInput.value = toLocalIso(new Date(now.getTime() - 24*60*60*1000));
        }

        // Avoid duplicate listeners
        if (!startInput.dataset.avInit) {
            [startInput, endInput].forEach(el => el.addEventListener('change', fetchAndRender));
            refreshBtn.addEventListener('click', () => {
            const now = new Date();
            endInput.value = toLocalIso(now);
            startInput.value = toLocalIso(new Date(now.getTime() - 24*60*60*1000));
            fetchAndRender();
        });
            startInput.dataset.avInit = '1';
        }

        function setTitle(startSec,endSec){
            const diffMs = (endSec-startSec)*1000;
            const diffH = diffMs/3600000;
            const diffD = diffH/24;
            if (!titleEl) return;
            titleEl.textContent = 'Availability';
        }

        function drawChart(svcData){
            chartDiv.innerHTML = '';
            const wrapper = document.createElement('div');
            wrapper.className = 'availability-flex';
            wrapper.id = 'svc-availability-summary';
            chartDiv.appendChild(wrapper);
            const donutContainer = document.createElement('div');
            donutContainer.className = 'availability-donut';
            wrapper.appendChild(donutContainer);
            const legendContainer = document.createElement('div');
            legendContainer.className = 'availability-legend';
            wrapper.appendChild(legendContainer);

            const ok = (+svcData.time_ok||0)+(+svcData.scheduled_time_ok||0);
            const warn = (+svcData.time_warning||0)+(+svcData.scheduled_time_warning||0);
            const crit = (+svcData.time_critical||0)+(+svcData.scheduled_time_critical||0);
            const unk = (+svcData.time_unknown||0)+(+svcData.scheduled_time_unknown||0);
            const total = ok+warn+crit+unk;
            const dataset=[
                {label:'OK',value:ok,color:getCssVar('--success')},
                {label:'Warning',value:warn,color:getCssVar('--warning')},
                {label:'Critical',value:crit,color:getCssVar('--critical')},
                {label:'Unknown',value:unk,color:getCssVar('--unknown')}
            ].filter(d=>d.value>0);
            if(total===0||dataset.length===0){
                chartDiv.innerHTML='<div style="text-align:center;color:var(--text);">No availability data for selected range</div>';
                return;
            }
            const size=140, radius=size/2, innerRadius=radius*0.55;
            const svg=d3.select(donutContainer).append('svg').attr('viewBox',`0 0 ${size} ${size}`).attr('preserveAspectRatio','xMidYMid meet');
            const g=svg.append('g').attr('transform',`translate(${radius},${radius})`);
            const pie=d3.pie().value(d=>d.value);
            const arc=d3.arc().innerRadius(innerRadius).outerRadius(radius);
            g.selectAll('path').data(pie(dataset)).enter().append('path').attr('d',arc).attr('fill',d=>d.data.color).append('title').text(d=>`${d.data.label}: ${formatDuration(d.data.value)} (${((d.data.value/total)*100).toFixed(1)}%)`);
            dataset.forEach(d=>{
                const item=document.createElement('div');
                item.className='availability-legend-item';
                const box=document.createElement('span');
                box.className='availability-legend-box';
                box.style.background=d.color;
                const text=document.createElement('span');
                text.textContent = `${d.label} (${((d.value/total)*100).toFixed(1)}%)`;
                item.appendChild(box);
                item.appendChild(text);
                legendContainer.appendChild(item);
            });
        }

        function drawTimeline(changes,startSec,endSec){
            let trendsDiv=document.getElementById('svc-availability-trends');
            if(!trendsDiv){
                trendsDiv=document.createElement('div');
                trendsDiv.id='svc-availability-trends';
                chartDiv.appendChild(trendsDiv);
            }
            trendsDiv.innerHTML='';
            if(!changes||changes.length===0){
                trendsDiv.innerHTML='<div style="text-align:center;color:var(--text);">No state changes for selected range</div>';
                return;
            }
            const width=trendsDiv.clientWidth||400;
            const height=60;
            const svg=d3.select(trendsDiv).append('svg').attr('viewBox',`0 0 ${width} ${height}`).attr('preserveAspectRatio','xMidYMid meet');
            const x=d3.scaleTime().domain([new Date(startSec*1000),new Date(endSec*1000)]).range([0,width]);
            for(let i=0;i<changes.length;i++){
                const cur=changes[i];
                const segStart=new Date(cur.timestamp);
                const segEnd=(i<changes.length-1)?new Date(changes[i+1].timestamp):new Date(endSec*1000);
                if(segEnd<new Date(startSec*1000)||segStart>new Date(endSec*1000)) continue;
                const xStart=x(segStart);
                const xEnd=x(segEnd);
                const segWidth=Math.max(xEnd-xStart,1);
                svg.append('rect')
                    .attr('x',xStart)
                    .attr('y',10)
                    .attr('width',segWidth)
                    .attr('height',20)
                    .attr('fill',getStateColor(cur.state))
                    .append('title')
                    .text(`${cur.state.toString().toUpperCase()} - ${new Date(cur.timestamp).toLocaleString()}`);
            }
            const axis=d3.axisBottom(x).ticks(5);
            svg.append('g').attr('transform','translate(0,35)').call(axis).selectAll('text').attr('font-size','10px').attr('fill',getCssVar('--text'));

            // Add brush interaction for zooming into a custom time range
            const brush = d3.brushX()
                .extent([[0, 0], [width, height]])
                .on('end', ({ selection }) => {
                    if (!selection) return;   // Ignore empty selections (e.g. clicks)
                    const [x0, x1] = selection;
                    const newStart = x.invert(x0);
                    const newEnd   = x.invert(x1);

                    // Update the date-time pickers
                    startInput.value = toLocalIso(newStart);
                    endInput.value   = toLocalIso(newEnd);

                    // Keep Trends tab active after zoom
                    activeTab = 'trends';

                    // Re-fetch data and redraw charts for the new window
                    fetchAndRender();
                });
            svg.append('g').attr('class', 'availability-brush').call(brush);
        }

        function fetchTimelineData(startSec,endSec){
            const url=`/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=service&hostname=${encodeURIComponent(host)}&servicedescription=${encodeURIComponent(serviceName)}&starttime=${startSec}&endtime=${endSec}`;
            return fetch(url,{credentials:'include'}).then(r=>r.json()).then(js=>js?.data?.statechangelist||[]).catch(()=>[]);
        }

        function fetchAndRender(){
            const startTs=Math.floor(new Date(startInput.value).getTime()/1000);
            const endTs=Math.floor(new Date(endInput.value).getTime()/1000);
            if(isNaN(startTs)||isNaN(endTs)||endTs<=startTs){
                console.warn('Invalid time range for availability graph.');
                return;
            }
            setTitle(startTs,endTs);
            chartDiv.innerHTML='<div style="text-align:center;padding:20px;"><i class="fa fa-spinner fa-spin"></i></div>';
            const url=`/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=services&hostname=${encodeURIComponent(host)}&servicedescription=${encodeURIComponent(serviceName)}&starttime=${startTs}&endtime=${endTs}`;
            fetch(url,{credentials:'include'}).then(r=>r.json()).then(data=>{
                if(!data||!data.data||!data.data.service){
                    chartDiv.innerHTML='<div style="text-align:center;color:var(--critical);">Error loading availability</div>';
                    return;
                }
                // Draw summary donut first
                drawChart(data.data.service);

                // Build tab navigation and trends container similar to host implementation
                const existingNav = chartDiv.querySelector('.availability-tabs');
                if (!existingNav) {
                    const nav = document.createElement('div');
                    nav.className = 'availability-tabs';
                    nav.innerHTML = `
                        <button class="availability-tab ${activeTab==='summary'?'active':''}" data-tab="summary">Availability</button>
                        <button class="availability-tab ${activeTab==='trends'?'active':''}" data-tab="trends">Trends</button>`;
                    chartDiv.prepend(nav);

                    const summaryWrapper = document.getElementById('svc-availability-summary');
                    let trendsDiv = document.getElementById('svc-availability-trends');
                    if (!trendsDiv) {
                        trendsDiv = document.createElement('div');
                        trendsDiv.id = 'svc-availability-trends';
                        trendsDiv.style.display = 'none';
                        chartDiv.appendChild(trendsDiv);
                    }

                    nav.querySelectorAll('.availability-tab').forEach(btn => {
                        btn.addEventListener('click', () => {
                            nav.querySelectorAll('.availability-tab').forEach(b => b.classList.remove('active'));
                            btn.classList.add('active');
                            const tab = btn.getAttribute('data-tab');
                            activeTab = tab;
                            if (tab === 'summary') {
                                summaryWrapper.style.display = 'flex';
                                trendsDiv.style.display = 'none';
                            } else {
                                summaryWrapper.style.display = 'none';
                                trendsDiv.style.display = 'block';
                            }
                        });
                    });

                    // Reset zoom handler
                    const resetBtn = nav.querySelector('.availability-reset');
                    if (resetBtn) {
                        resetBtn.addEventListener('click', () => {
                            const now = new Date();
                            endInput.value = toLocalIso(now);
                            startInput.value = toLocalIso(new Date(now.getTime() - 24*60*60*1000));
                            fetchAndRender();
                        });
                    }

                    // Ensure correct initial visibility after rerender
                    nav.querySelector(`[data-tab="${activeTab}"]`).click();
                }

                // Always redraw timeline data for current range
                fetchTimelineData(startTs,endTs).then(changes=>{
                    drawTimeline(changes,startTs,endTs);
                });
            }).catch(() => {
                chartDiv.innerHTML='<div style="text-align:center;color:var(--critical);">Error loading availability</div>';
            });
        }

        // Initial fetch
        fetchAndRender();
    } catch(e){
        console.error('Error in service availability tab:',e);
    }
}

// Function to load the host availability tab
function loadHostAvailabilityTab() {
    try {
        const host = window.hostAvailabilityHostName;
        if (!host) return;

        const startInput = document.getElementById('host-availability-start');
        const endInput = document.getElementById('host-availability-end');
        const refreshBtn = document.getElementById('host-availability-refresh');
        const chartDiv = document.getElementById('host-availability-chart');
        const titleEl = document.getElementById('host-availability-title');

        if (!startInput || !endInput || !refreshBtn || !chartDiv) return;

        // Helpers
        const toLocalIso = (dateObj) => {
            const tzOffset = dateObj.getTimezoneOffset() * 60000;
            return new Date(dateObj.getTime() - tzOffset).toISOString().slice(0,16);
        };
        const getCssVar = (name) => getComputedStyle(document.documentElement).getPropertyValue(name) || '#ccc';
        const formatDuration = (sec) => {
            const h = Math.floor(sec / 3600);
            const m = Math.floor((sec % 3600) / 60);
            const s = sec % 60;
            return `${h}h ${m}m ${s}s`;
        };
        const getStateColor = (state) => {
            switch (state) {
                case 'up':
                    return getCssVar('--success');
                case 'down':
                    return getCssVar('--critical');
                case 'unreachable':
                    return getCssVar('--unknown');
                default:
                    return getCssVar('--pending');
            }

        };

        // Track active sub-tab (summary | trends)
        let activeTab = 'summary';

        // Initialise default dates if empty
        if (!startInput.value || !endInput.value) {
            const now = new Date();
            endInput.value = toLocalIso(now);
            startInput.value = toLocalIso(new Date(now.getTime() - 24*60*60*1000));
        }

        function setTitle(startSec, endSec) {
            if (!titleEl) return;
            const diffMs = (endSec - startSec) * 1000;
            const diffH = diffMs / 3600000;
            const diffD = diffH / 24;
            titleEl.textContent = 'Availability';
        }

        function drawChart(hostData) {
            chartDiv.innerHTML = '';
            const wrapper = document.createElement('div');
            wrapper.className = 'availability-flex';
            wrapper.id = 'host-availability-summary';
            chartDiv.appendChild(wrapper);
            const donutContainer = document.createElement('div');
            donutContainer.className = 'availability-donut';
            wrapper.appendChild(donutContainer);
            const legendContainer = document.createElement('div');
            legendContainer.className = 'availability-legend';
            wrapper.appendChild(legendContainer);

            // sum real and scheduled times so we show realistic availability
            const up = (+hostData.time_up || 0) + (+hostData.scheduled_time_up || 0);
            const down = (+hostData.time_down || 0) + (+hostData.scheduled_time_down || 0);
            const unreach = (+hostData.time_unreachable || 0) + (+hostData.scheduled_time_unreachable || 0);
            const total = up + down + unreach;

            const dataset = [
                { label: 'Up', value: up, color: getCssVar('--success') },
                { label: 'Down', value: down, color: getCssVar('--critical') },
                { label: 'Unreachable', value: unreach, color: getCssVar('--unknown') }
            ].filter(d => d.value > 0);

            if (total === 0 || dataset.length === 0) {
                chartDiv.innerHTML = '<div style="text-align:center;color:var(--text);">No availability data for selected range</div>';
                return;
            }

            const size = 140;
            const radius = size / 2;
            const innerRadius = radius * 0.55;

            const svg = d3.select(donutContainer)
                .append('svg')
                .attr('viewBox', `0 0 ${size} ${size}`)
                .attr('preserveAspectRatio', 'xMidYMid meet');

            const g = svg.append('g').attr('transform', `translate(${radius},${radius})`);

            const pie = d3.pie().value(d => d.value);
            const arc = d3.arc().innerRadius(innerRadius).outerRadius(radius);

            g.selectAll('path')
                .data(pie(dataset))
                .enter()
                .append('path')
                .attr('d', arc)
                .attr('fill', d => d.data.color)
                .append('title')
                .text(d => `${d.data.label}: ${formatDuration(d.data.value)} (${((d.data.value / total) * 100).toFixed(1)}%)`);

            // Legend (HTML for easier styling)
            dataset.forEach(d => {
                const item = document.createElement('div');
                item.className = 'availability-legend-item';

                const box = document.createElement('span');
                box.className = 'availability-legend-box';
                box.style.background = d.color;

                const text = document.createElement('span');
                text.textContent = `${d.label} (${((d.value / total) * 100).toFixed(1)}%)`;

                item.appendChild(box);
                item.appendChild(text);
                legendContainer.appendChild(item);
            });
        }

        function drawTimeline(changes, startSec, endSec) {
            const container = document.getElementById('host-availability-trends');
            if (!container) return;

            container.innerHTML = '';

            if (!changes || changes.length === 0) {
                container.innerHTML = '<div style="text-align:center;color:var(--text);">No state changes for selected range</div>';
                return;
            }

            const width = container.clientWidth || 400;
            const height = 60;

            const svg = d3.select(container)
                .append('svg')
                .attr('viewBox', `0 0 ${width} ${height}`)
                .attr('preserveAspectRatio', 'xMidYMid meet');

            const x = d3.scaleTime()
                .domain([new Date(startSec * 1000), new Date(endSec * 1000)])
                .range([0, width]);

            for (let i = 0; i < changes.length; i++) {
                const cur = changes[i];
                const segStart = new Date(cur.timestamp);
                const segEnd = (i < changes.length - 1) ? new Date(changes[i + 1].timestamp) : new Date(endSec * 1000);

                // Skip segments fully outside range
                if (segEnd < new Date(startSec * 1000) || segStart > new Date(endSec * 1000)) continue;

                const xStart = x(segStart);
                const xEnd = x(segEnd);
                const segWidth = Math.max(xEnd - xStart, 1);

                svg.append('rect')
                    .attr('x', xStart)
                    .attr('y', 10)
                    .attr('width', segWidth)
                    .attr('height', 20)
                    .attr('fill', getStateColor(cur.state))
                    .append('title')
                    .text(`${cur.state.toUpperCase()} - ${new Date(cur.timestamp).toLocaleString()}`);
            }

            const axis = d3.axisBottom(x).ticks(5);
            svg.append('g')
                .attr('transform', `translate(0,35)`)
                .call(axis)
                .selectAll('text')
                .attr('font-size', '10px')
                .attr('fill', getCssVar('--text'));

            // Add brush interaction for zooming into a custom time range
            const brush = d3.brushX()
                .extent([[0, 0], [width, height]])
                .on('end', ({ selection }) => {
                    if (!selection) return; // Ignore empty selections
                    const [x0, x1] = selection;
                    const newStart = x.invert(x0);
                    const newEnd   = x.invert(x1);

                    startInput.value = toLocalIso(newStart);
                    endInput.value   = toLocalIso(newEnd);

                    // Keep Trends tab active after zoom
                    activeTab = 'trends';

                    fetchAndRender();
                });
            svg.append('g').attr('class', 'availability-brush').call(brush);
        }

        function fetchTimelineData(startSec, endSec) {
            const url = `/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=host&hostname=${encodeURIComponent(host)}&starttime=${startSec}&endtime=${endSec}`;
            return fetch(url, { credentials: 'include' })
                .then(r => r.json())
                .then(js => js?.data?.statechangelist || [])
                .catch(err => {
                    console.error('Timeline fetch error', err);
                    return [];
                });
        }

        function fetchAndRender() {
            const startTs = Math.floor(new Date(startInput.value).getTime() / 1000);
            const endTs = Math.floor(new Date(endInput.value).getTime() / 1000);
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
                console.warn('Invalid time range for availability graph.');
                return;
            }

            setTitle(startTs, endTs);

            chartDiv.innerHTML = '<div style="text-align:center;padding:20px;"><i class="fa fa-spinner fa-spin"></i></div>';

            const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&hostname=${encodeURIComponent(host)}&starttime=${startTs}&endtime=${endTs}`;

            fetch(url, { credentials: 'include' })
                .then(r => r.json())
                .then(data => {
                    if (!data || !data.data || !data.data.host) {
                        chartDiv.innerHTML = '<div style="text-align:center;color:var(--critical);">Error loading availability</div>';
                        return;
                    }

                    // Draw summary donut first
                    drawChart(data.data.host);

                    // Build tab navigation and trends container
                    const existingNav = chartDiv.querySelector('.availability-tabs');
                    if (!existingNav) {
                        const nav = document.createElement('div');
                        nav.className = 'availability-tabs';
                        nav.innerHTML = `
                            <button class="availability-tab ${activeTab==='summary'?'active':''}" data-tab="summary">Availability</button>
                            <button class="availability-tab ${activeTab==='trends'?'active':''}" data-tab="trends">Trends</button>
`;

                        chartDiv.prepend(nav);

                        const summaryWrapper = document.getElementById('host-availability-summary');
                        let trendsDiv = document.getElementById('host-availability-trends');
                        if (!trendsDiv) {
                            trendsDiv = document.createElement('div');
                            trendsDiv.id = 'host-availability-trends';
                            trendsDiv.style.display = 'none';
                            chartDiv.appendChild(trendsDiv);
                        }

                        nav.querySelectorAll('.availability-tab').forEach(btn => {
                            btn.addEventListener('click', () => {
                                nav.querySelectorAll('.availability-tab').forEach(b => b.classList.remove('active'));
                                btn.classList.add('active');
                                const tab = btn.getAttribute('data-tab');
                                    activeTab = tab;
                                if (tab === 'summary') {
                                    summaryWrapper.style.display = 'flex';
                                    trendsDiv.style.display = 'none';
                                } else {
                                    summaryWrapper.style.display = 'none';
                                    trendsDiv.style.display = 'block';
                                }
                            });
                        });

                        // Reset zoom handler
                        const resetBtn = nav.querySelector('.availability-reset');
                        if (resetBtn) {
                            resetBtn.addEventListener('click', () => {
                                const now = new Date();
                                endInput.value = toLocalIso(now);
                                startInput.value = toLocalIso(new Date(now.getTime() - 24*60*60*1000));
                                fetchAndRender();
                            });
                        }

                        // Ensure correct visibility after render
                        nav.querySelector(`[data-tab="${activeTab}"]`).click();
                    }

                    // Always redraw timeline data for current range
                    fetchTimelineData(startTs, endTs).then(changes => {
                        drawTimeline(changes, startTs, endTs);
                    });
                })
                .catch(err => {
                    console.error('Availability fetch error', err);
                    chartDiv.innerHTML = '<div style="text-align:center;color:var(--critical);">Error loading availability</div>';
                });
        }

        // Set up event listeners
        refreshBtn.addEventListener('click', () => {
            const now = new Date();
            endInput.value = toLocalIso(now);
            startInput.value = toLocalIso(new Date(now.getTime() - 24*60*60*1000));
            fetchAndRender();
        });
        [startInput, endInput].forEach(el => el.addEventListener('change', fetchAndRender));

        // Initial fetch
        fetchAndRender();
    } catch(e) {
        console.error('Error in host availability tab:', e);
    }
}

// Function to refresh the information displayed inside the modal
async function refreshModalContent() {
    try {
        if (!window.currentModalContext) return;

        const modalBody = document.getElementById('modal-body');
        if (modalBody) {
            modalBody.innerHTML = '<div class="loading">Refreshing data...</div>';
        }

        const hostname = window.currentModalContext.hostname;
        const fullStatusUrl = `get_full_status.php?hostname=${encodeURIComponent(hostname)}`;
        const hostInfoUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=host&hostname=${encodeURIComponent(hostname)}`;

        const [fullStatusResp, hostInfoResp] = await Promise.all([
            fetch(fullStatusUrl),
            fetch(hostInfoUrl, { credentials: 'include' })
        ]);

        if (!fullStatusResp.ok || !hostInfoResp.ok) throw new Error('Failed to fetch refreshed data');

        const fullStatus = await fullStatusResp.json();
        const hostInfo = await hostInfoResp.json();
        
        if (fullStatus.error) throw new Error(fullStatus.error);
        if (hostInfo.result.type_text !== 'Success') throw new Error('Invalid host info response');


        if (window.currentModalContext.type === 'host') {
            const host = hostInfo.data.host;
            const hostStats = fullStatus.hoststatus;

            const statusMapHostDat = {
                '0': { class: 'ok', text: 'UP' },
                '1': { class: 'critical', text: 'DOWN' },
                '2': { class: 'unknown', text: 'UNREACHABLE' }
            };
            const statusEntry = statusMapHostDat[hostStats.current_state] || { class: 'unknown', text: 'UNKNOWN' };
            
            const statuses = {
                activeChecks: hostStats.active_checks_enabled === '1' ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
                passiveChecks: hostStats.passive_checks_enabled === '1' ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
                obsessing: hostStats.obsess === '1' ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
                notifications: hostStats.notifications_enabled === '1' ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
                eventHandler: hostStats.event_handler_enabled === '1' ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' },
                flapDetection: hostStats.flap_detection_enabled === '1' ? { text: 'ENABLED', class: 'ok' } : { text: 'DISABLED', class: 'critical' }
            };

            const hostStatsForModal = mapHostStatsForModal(hostStats);

            // We need to fetch the action_url for the host to show the performance tab
            const hostObjectInfoUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=host&hostname=${encodeURIComponent(hostname)}`;
            try {
                const objResp = await fetch(hostObjectInfoUrl, { credentials: 'include' });
                if (objResp.ok) {
                    const objInfo = await objResp.json();
                    if (objInfo.result.type_text === 'Success' && objInfo.data.host) {
                        host.action_url = objInfo.data.host.action_url || null;
                    }
                }
            } catch (e) {
                console.error('Error fetching host object info for refresh:', e);
            }

            populateHostModal(host, hostStatsForModal, statuses, statusEntry);

        } else if (window.currentModalContext.type === 'service') {
            const serviceName = window.currentModalContext.serviceName;
            const serviceData = fullStatus.servicestatus.find(s => s.service_description === serviceName);

            if (serviceData) {
                const serviceForModal = mapServiceForModal(serviceData);

                // Fetch service object info to get action_url for performance graphs
                const serviceObjectUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=service&hostname=${encodeURIComponent(hostname)}&servicedescription=${encodeURIComponent(serviceName)}`;
                try {
                    const response = await fetch(serviceObjectUrl, { credentials: 'include' });
                    if (response.ok) {
                        const serviceObjectInfo = await response.json();
                        if (serviceObjectInfo.result.type_text === 'Success' &&
                            serviceObjectInfo.data.service &&
                            serviceObjectInfo.data.service.action_url) {
                            serviceForModal.action_url = serviceObjectInfo.data.service.action_url;
                        }
                    }
                } catch (error) {
                    console.error('Error fetching service action URL on refresh:', error);
                }
                
                populateServiceModal(serviceForModal);
            }
        }
    } catch (error) {
        console.error('Error refreshing modal:', error);
        const modalBody = document.getElementById('modal-body');
        if (modalBody) {
            modalBody.innerHTML = `<p>Error refreshing data: ${error.message}</p>`;
        }
    }
} 