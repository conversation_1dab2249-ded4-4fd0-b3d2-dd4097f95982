function launchTroubleshooting(targetDivId) {
    // Open the protocol availability modal instead of using accordion
    openProtocolAvailabilityModal();
}

function openProtocolAvailabilityModal() {
    const modal = document.getElementById("service-modal");
    const modalTitle = document.getElementById("modal-title");
    const modalBody = document.getElementById("modal-body");
    const modalClose = document.getElementById("modal-close");
    
    // Hide modal options, delete buttons, and refresh button
    document.getElementById('modal-options').style.display = 'none';
    document.getElementById('modal-delete').style.display = 'none';
    document.getElementById('modal-refresh').style.display = 'none';
    
    document.body.style.overflow = "hidden";
    modal.style.display = "flex";
    modalTitle.innerHTML = "Protocol Availability Check";
    
    // Create the modal content with protocol options
    modalBody.innerHTML = `
        <div class="protocol-check-container">
            <div class="protocol-options">
                <h3>Select Protocol Check Type:</h3>
                <div class="protocol-option" onclick="runProtocolCheck('wmi')">
                    <div class="protocol-icon">
                        <i class="fa fa-cog" aria-hidden="true"></i>
                    </div>
                    <div class="protocol-info">
                        <h4>WMI/SNMP Check</h4>
                        <p>Check host availability using WMI and SNMP protocols</p>
                    </div>
                </div>
                <div class="protocol-option" onclick="runProtocolCheck('vmware')">
                    <div class="protocol-icon">
                        <i class="fa fa-cloud" aria-hidden="true"></i>
                    </div>
                    <div class="protocol-info">
                        <h4>VMware Check</h4>
                        <p>Check VMware host availability using HTTPS/vSphere API</p>
                    </div>
                </div>
            </div>
            <div id="protocol-results" class="protocol-results" style="display: none;">
                <div id="protocol-loading" class="protocol-loading" style="display: none;">
                    <i class="fa fa-spinner fa-spin"></i>
                    <p>Checking protocol availability...<br><small>This may take a minute or more</small></p>
                </div>
                <div id="protocol-content"></div>
            </div>
        </div>
    `;
    
    // Add event listener for modal close
    modalClose.onclick = () => {
        modal.style.display = "none";
        document.body.style.overflow = "auto";
    };
    
    // Close modal when clicking outside
    window.onclick = (event) => {
        if (event.target === modal) {
            modal.style.display = "none";
            document.body.style.overflow = "auto";
        }
    };
}

async function runProtocolCheck(checkType) {
    const hostIp = urlParams.get('hostip') || urlParams.get('ip') || urlParams.get('hostname');
    const resultsDiv = document.getElementById('protocol-results');
    const loadingDiv = document.getElementById('protocol-loading');
    const contentDiv = document.getElementById('protocol-content');
    
    // Show results section and loading
    resultsDiv.style.display = 'block';
    loadingDiv.style.display = 'block';
    contentDiv.innerHTML = '';
    
    // Hide protocol options
    document.querySelector('.protocol-options').style.display = 'none';
    
    try {
        let result;
        
        if (checkType === 'wmi') {
            // Run WMI check first, then SNMP if needed
            result = await runWMIAndSNMPCheck(hostIp);
        } else if (checkType === 'vmware') {
            // Run VMware check
            result = await runVMwareCheck(hostIp);
        }
        
        // Hide loading and show results
        loadingDiv.style.display = 'none';
        contentDiv.innerHTML = result;
        
    } catch (error) {
        loadingDiv.style.display = 'none';
        contentDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
    }
}

async function runWMIAndSNMPCheck(hostIp) {
    // First try WMI check
    const wmiResponse = await fetch(`wmic_check.php?host=${encodeURIComponent(hostIp)}`, {
        method: 'GET',
        headers: {
            'Accept': 'text/html'
        }
    });

    if (!wmiResponse.ok) {
        throw new Error(`HTTP error! status: ${wmiResponse.status}`);
    }

    const wmiResult = await wmiResponse.text();

    if (!wmiResult.includes("This tool is designed for Windows hosts only")) {
        return formatWMIResponse(wmiResult);
    } else {
        // If WMI fails, try SNMP
        const snmpResponse = await fetch(`snmpCheck.php?host=${encodeURIComponent(hostIp)}`);
        if (!snmpResponse.ok) {
            throw new Error(`SNMP check failed: ${snmpResponse.status}`);
        }
        const snmpResult = await snmpResponse.text();
        return formatSNMPResponse(snmpResult);
    }
}

async function runVMwareCheck(hostIp) {
    const response = await fetch(`vmware_check.php?host=${encodeURIComponent(hostIp)}`);
    
    if (!response.ok) {
        throw new Error(`VMware check failed: ${response.status}`);
    }
    
    const result = await response.text();
    return formatVMwareResponse(result);
}

function formatVMwareResponse(text) {
    return `<div class="section">
            <h3>VMware Check Results</h3>
            ${processText(text)}
        </div>`;
}

function formatWMIResponse(text) {
    return `<div class="section">
            <h3>Windows WMI Check Results</h3>
            ${processText(text)}
        </div>`;
}

function isValidIP(ip) {
    return /^(?:\d{1,3}\.){3}\d{1,3}$/.test(ip);
}

function formatSNMPResponse(text) {
    const sections = text.split('\nNow checking snmpv3\n');
    let html = '';

    if (sections[0]) {
        html += `<div class="section">
                        <h3>SNMP Results</h3>
                        ${processText(sections[0])}
                     </div>`;
    }

    if (sections[1]) {
        html += `<div class="section">
                <h3>SNMPv3 Results</h3>
                ${processText(sections[1])}
            </div>`;
    }

    return html;
}

function processText(text) {
    return text
        .split('\n')
        .map(line => {
            line = line.trim();
            if (!line) return '';
            if (line.includes('✓') || line.includes('SNMP Data')) {
                return `<div class="success">${line}</div>`;
            }
            if (line.startsWith('✗') || line.includes('fail')) {
                return `<div class="error">${line}</div>`;
            }
            return `<div>${line}</div>`;
        })
        .join('');
}