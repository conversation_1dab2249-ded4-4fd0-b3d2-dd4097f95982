// Function to fetch host status info from status.dat via PHP endpoint
async function fetchHostStatusDatInfo(host, hostStats, statuses, statusEntry) {
    const modal = document.getElementById('service-modal');
    const modalBody = document.getElementById('modal-body');
    const statusDatUrl = `get_status_dat_info.php?type=host&hostname=${encodeURIComponent(host.name)}`;

    try {
        const response = await fetch(statusDatUrl);
        if (!response.ok) {
            throw new Error(`Network response was not ok: ${response.statusText}`);
        }
        const data = await response.json();

        if (data.error) {
            console.error('Error fetching host info from status.dat:', data.error);
            // Populate with original data even though plugin_output was initially empty
            populateHostModal(host, hostStats, statuses, statusEntry);
        } else {
            // Preserve action_url from host object
            data.action_url = host.action_url;
            
            // Populate modal with data including status.dat info
            populateHostModal(host, hostStats, statuses, statusEntry, data);
        }
    } catch (error) {
        console.error('Fetch Error for host status.dat:', error);
        // Populate with original data on error
        populateHostModal(host, hostStats, statuses, statusEntry);
        modalBody.innerHTML += `<p style="color: red;">Could not fetch details from status.dat: ${error.message}</p>`;
    }
}

// Function to fetch status info from status.dat via PHP endpoint
async function fetchStatusDatInfo(hostName, serviceDescription, originalServiceData) {
    const modal = document.getElementById('service-modal');
    const modalBody = document.getElementById('modal-body');
    const statusDatUrl = `get_status_dat_info.php?hostname=${encodeURIComponent(hostName)}&serviceDescription=${encodeURIComponent(serviceDescription)}`;

    try {
        const response = await fetch(statusDatUrl);
        if (!response.ok) {
            throw new Error(`Network response was not ok: ${response.statusText}`);
        }
        const data = await response.json();

        if (data.error) {
             console.error('Error fetching from status.dat:', data.error);
             // Populate with original data even though outputs were initially empty
             populateServiceModal(originalServiceData);
        } else {

            
            // Preserve action_url from original service data if it exists
            if (originalServiceData.action_url) {
                data.action_url = originalServiceData.action_url;
            }
            
            // Populate modal with data including status.dat info
            populateServiceModal(originalServiceData, data);
        }
    } catch (error) {
        console.error('Fetch Error for status.dat:', error);
         // Populate with original data on error
         populateServiceModal(originalServiceData);
         modalBody.innerHTML += `<p style="color: red;">Could not fetch details from status.dat: ${error.message}</p>`;
    }
} 