async function generateContextMenu(serviceEncodedName, hostname, isHost = false, statusData = null) {
    const baseUrl = `https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi`;
    
    let itemData = statusData;

    // If data is not passed directly, fetch it (fallback for other pages)
    if (!itemData) {
        const url = isHost ?
            `get_status_dat_info.php?type=host&hostname=${hostname}` :
            `get_status_dat_info.php?type=service&hostname=${hostname}&serviceDescription=${decodeURIComponent(serviceEncodedName)}`;
        
        try {
            const response = await fetch(url);
            if (!response.ok) throw new Error('Failed to fetch details from status.dat');
            const data = await response.json();
            if (data.error) throw new Error(data.error);
            itemData = data;
        } catch (error) {
            console.error('Error fetching data for context menu:', error);
            return `<div style="background: #fff; border-radius: 8px; padding: 12px; width: 260px; font-family: Arial, sans-serif; color: #ff4444;">Error loading commands: ${error.message}</div>`;
        }
    }

    try {
        if (isHost) {
            const host = itemData;
            const hostCommandList = [{
                icon: "fa-toggle-off",
                cmd: 47,
                text: "Enable active checks of this host",
                condition: host.active_checks_enabled === '0'
            },
            {
                icon: "fa-toggle-on",
                cmd: 48,
                text: "Disable active checks of this host",
                condition: host.active_checks_enabled === '1'
            },
            {
                icon: "fa-clock-o",
                cmd: 96,
                text: "Re-schedule the next check of this host"
            },
            {
                icon: "fa-play-circle",
                cmd: 92,
                text: "Start accepting passive checks for this host",
                condition: host.passive_checks_enabled === '0'
            },
            {
                icon: "fa-stop-circle-o",
                cmd: 93,
                text: "Stop accepting passive checks for this host",
                condition: host.passive_checks_enabled === '1'
            },
            {
                icon: "fa-play-circle",
                cmd: 101,
                text: "Start obsessing over this host",
                condition: host.obsess === '0'
            },
            {
                icon: "fa-stop-circle-o",
                cmd: 102,
                text: "Stop obsessing over this host",
                condition: host.obsess === '1'
            },
            {
                icon: "fa-gavel",
                cmd: 33,
                text: "Acknowledge this host problem",
                condition: host.current_state != '0' && host.problem_has_been_acknowledged === '0'
            },
            {
                icon: "fa-gavel",
                cmd: 51,
                text: "Remove problem acknowledgement",
                condition: host.problem_has_been_acknowledged === '1'
            },
            {
                icon: "fa-microphone-slash",
                cmd: 24,
                text: "Enable notifications for this host",
                condition: host.notifications_enabled === '0'
            },
            {
                icon: "fa-microphone",
                cmd: 25,
                text: "Disable notifications for this host",
                condition: host.notifications_enabled === '1'
            },
            {
                icon: "fa-bullhorn",
                cmd: 159,
                text: "Send custom host notification"
            },
            {
                icon: "fa-moon-o",
                cmd: 55,
                text: "Schedule downtime for this host"
            },
            {
                icon: "fa-moon-o",
                cmd: 86,
                text: "Schedule downtime for all services on this host"
            },
            {
                icon: "fa-microphone-slash",
                cmd: 29,
                text: "Disable notifications for all services on this host"
            },
            {
                icon: "fa-microphone",
                cmd: 28,
                text: "Enable notifications for all services on this host"
            },
            {
                icon: "fa-clock-o",
                cmd: 17,
                text: "Schedule a check of all services on this host"
            },
            {
                icon: "fa-dot-circle-o",
                cmd: 16,
                text: "Disable checks of all services on this host"
            },
            {
                icon: "fa-circle-o",
                cmd: 15,
                text: "Enable checks of all services on this host"
            },
            {
                icon: "fa-toggle-off",
                cmd: 43,
                text: "Enable event handler for this host",
                condition: host.event_handler_enabled === '0'
            },
            {
                icon: "fa-toggle-on",
                cmd: 44,
                text: "Disable event handler for this host",
                condition: host.event_handler_enabled === '1'
            },
            {
                icon: "fa-toggle-off",
                cmd: 57,
                text: "Enable flap detection for this host",
                condition: host.flap_detection_enabled === '0'
            },
            {
                icon: "fa-toggle-on",
                cmd: 58,
                text: "Disable flap detection for this host",
                condition: host.flap_detection_enabled === '1'
            },
            {
                icon: "fa-eraser",
                cmd: 173,
                text: "Clear flapping state for this host",
                condition: host.flap_detection_enabled === '1'
            },
            {
                icon: "fa-sitemap",
                cmd: 10003, // Custom command for parent options
                text: "Parent Options"
            },
            {
                icon: "fa-arrows",
                cmd: 10002, // Custom command for relocate
                text: "Relocate to"
            },
            {
                icon: "fa-edit",
                cmd: 10001, // Custom command for rename
                text: "Rename Host"
            },
            {
                icon: "fa-trash",
                cmd: 10000, // Custom command for blacklist
                text: "Delete Host"
            }
            ];

            return `
                    <div class="commands-card">
                        <h5 class="card-title">Host Commands</h5>
                        <ul class="command-list">
                            ${hostCommandList
                    .filter(cmd => cmd.condition !== undefined ? cmd.condition : true)
                    .map(cmd => {
                        if (cmd.cmd === 10000) {
                            // Special handling for blacklist command
                            return `
                                        <a href="#" class="command-link" onclick="blacklistHostApm('${hostname}'); return false;">
                                            <li class="command-item" style="color: #ff4444;">
                                                <i class="fa ${cmd.icon} command-icon"></i>
                                                ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        } else if (cmd.cmd === 10001) {
                            // Special handling for rename command
                            return `
                                        <a href="#" class="command-link" onclick="renameHostApm('${hostname}'); return false;">
                                            <li class="command-item" style="color: #007bff;">
                                                <i class="fa ${cmd.icon} command-icon"></i>
                                                ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        } else if (cmd.cmd === 10002) {
                            // Special handling for relocate command
                            return `
                                        <a href="#" class="command-link" onclick="showHostGroupModalApm('${hostname}'); return false;">
                                            <li class="command-item" style="color: #28a745;">
                                                <i class="fa ${cmd.icon} command-icon"></i>
                                                ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        } else if (cmd.cmd === 10003) {
                            // Special handling for parent options command
                            return `
                                        <a href="#" class="command-link" onclick="openParentOptionsApm('${hostname}'); return false;">
                                            <li class="command-item" style="color: #6c757d;">
                                                <i class="fa ${cmd.icon} command-icon"></i>
                                                ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        } else {
                            return `
                                        <a href="#" class="command-link" onclick="openModalWithIframe('${baseUrl}?cmd_typ=${cmd.cmd}&host=${hostname}', '${cmd.text}'); return false;">
                                            <li class="command-item">
                                                <i class="fa ${cmd.icon} command-icon"></i>
                                                ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        }
                    }).join("")}
                        </ul>
                    </div>`;
        } else {
            const service = itemData;

            // Check if service is renameable (async operation)
            let isRenameable = false;
            try {
                const hostip = urlParams.get('hostip');
                if (hostip && service.service_description) {
                    const renameCheckResponse = await fetch(`check_service_renameable.php?ip=${encodeURIComponent(hostip)}&service_name=${encodeURIComponent(service.service_description)}`);
                    if (renameCheckResponse.ok) {
                        const renameCheckData = await renameCheckResponse.json();
                        isRenameable = renameCheckData.renameable;
                    }
                }
            } catch (error) {
                console.error('Error checking service renameable:', error);
            }

            const commandList = [{
                icon: "fa-toggle-on",
                cmd: 6,
                text: "Disable active checks",
                condition: service.active_checks_enabled === '1'
            },
            {
                icon: "fa-toggle-off",
                cmd: 5,
                text: "Enable active checks of this service",
                condition: service.active_checks_enabled === '0'
            },
            {
                icon: "fa-clock-o",
                cmd: 7,
                text: "Re-schedule next check"
            },
            {
                icon: "fa-external-link-square",
                cmd: 30,
                text: "Submit passive check result",
                condition: service.passive_checks_enabled === '1'
            },
            {
                icon: "fa-stop-circle-o",
                cmd: 40,
                text: "Stop accepting passive checks",
                condition: service.passive_checks_enabled === '1'
            },
            {
                icon: "fa-play-circle-o",
                cmd: 39,
                text: "Start accepting passive checks",
                condition: service.passive_checks_enabled === '0'
            },
            {
                icon: "fa-stop-circle-o",
                cmd: 100,
                text: "Stop obsessing over service",
                condition: service.obsess === '1'
            },
            {
                icon: "fa-play-circle-o",
                cmd: 99,
                text: "Start obsessing over this service",
                condition: service.obsess === '0'
            },
            {
                icon: "fa-gavel",
                cmd: 34,
                text: "Acknowledge this service problem",
                condition: service.current_state != '0' && service.problem_has_been_acknowledged === '0'
            },
            {
                icon: "fa-gavel",
                cmd: 52,
                text: "Remove problem acknowledgement",
                condition: service.problem_has_been_acknowledged === '1'
            },
            {
                icon: "fa-microphone",
                cmd: 23,
                text: "Disable notifications",
                condition: service.notifications_enabled === '1'
            },
            {
                icon: "fa-microphone-slash",
                cmd: 22,
                text: "Enable notifications for this service",
                condition: service.notifications_enabled === '0'
            },
            {
                icon: "fa-bullhorn",
                cmd: 160,
                text: "Send custom notification"
            },
            {
                icon: "fa-moon-o",
                cmd: 56,
                text: "Schedule downtime"
            },
            {
                icon: "fa-toggle-on",
                cmd: 46,
                text: "Disable event handler",
                condition: service.event_handler_enabled === '1'
            },
            {
                icon: "fa-toggle-off",
                cmd: 45,
                text: "Enable event handler",
                condition: service.event_handler_enabled === '0'
            },
            {
                icon: "fa-toggle-on",
                cmd: 60,
                text: "Disable flap detection",
                condition: service.flap_detection_enabled === '1'
            },
            {
                icon: "fa-toggle-off",
                cmd: 59,
                text: "Enable flap detection",
                condition: service.flap_detection_enabled === '0'
            },
            {
                icon: "fa-eraser",
                cmd: 174,
                text: "Clear flapping state",
                condition: service.flap_detection_enabled === '1'
            },
            {
                icon: "fa-edit",
                cmd: 9998, // Custom command for rename
                text: "Rename Service",
                condition: isRenameable
            },
            {
                icon: "fa-trash",
                cmd: 9999, //random
                text: "Delete Service"
            }
            ];

            return `
                <div class="commands-card">
                    <h5 class="card-title">Service Commands</h5>
                    <ul class="command-list">
                        ${commandList
                    .filter(cmd => cmd.condition !== undefined ? cmd.condition : true)
                    .map(cmd => {
                        if (cmd.cmd === 9999) {
                            hostip = urlParams.get('hostip');
                            return `
                                        <a href="#" class="command-link" onclick="callDeleteService('${hostip}', '${service.service_description}'); return false;">
                                            <li class="command-item" style="color: #ff4444;">
                                                    <i class="fa ${cmd.icon} command-icon"></i>
                                                    ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        } else if (cmd.cmd === 9998) {
                            hostip = urlParams.get('hostip');
                            return `
                                        <a href="#" class="command-link" onclick="renameServiceApm('${service.service_description}', '${hostip}'); return false;">
                                            <li class="command-item" style="color: #007bff;">
                                                    <i class="fa ${cmd.icon} command-icon"></i>
                                                    ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        } else {
                            return `
                                        <a href="#" class="command-link" onclick="openModalWithIframe('${baseUrl}?cmd_typ=${cmd.cmd}&host=${hostname}&service=${service.service_description}', '${cmd.text}'); return false;">
                                            <li class="command-item">
                                                    <i class="fa ${cmd.icon} command-icon"></i>
                                                    ${cmd.text}
                                            </li>
                                         </a>
                                    `;
                        }
                    }).join("")}
                    </ul>
                </div>`;
        }
    } catch (error) {
        console.error('Error generating context menu:', error);
        return `<div style="background: #fff; border-radius: 8px; padding: 12px; width: 260px; font-family: Arial, sans-serif; color: #ff4444;">Error loading commands</div>`;
    }
}

const handleContextMenu = async (e, triggerType) => {
    const modalBody = document.getElementById('modal-body');
    const modalTitle = document.getElementById('modal-title');
    const contextMenu = document.getElementById('custom-context-menu');

    const showContextMenu = (content, e) => {
        e.preventDefault();
        e.stopImmediatePropagation(); // Prevent default browser context menu on macOS Safari
        if (triggerType === 'click') {
            e.stopPropagation();
        }
        contextMenu.innerHTML = content;

        // Position adjustments
        contextMenu.style.display = 'block';
        contextMenu.style.visibility = 'hidden';
        contextMenu.style.left = '-9999px';
        contextMenu.style.top = '0px';

        const menuWidth = contextMenu.offsetWidth;
        const menuHeight = contextMenu.offsetHeight;

        contextMenu.style.display = 'none';
        contextMenu.style.visibility = 'visible';

        let adjustedX, adjustedY;

        if (triggerType === 'contextmenu') {
            // Include current page scroll offsets so the menu is positioned correctly
            const scrollX = window.scrollX || window.pageXOffset;
            const scrollY = window.scrollY || window.pageYOffset;

            // Initial position based on click coordinates plus scroll offset
            adjustedX = e.clientX + scrollX;
            adjustedY = e.clientY + scrollY;

            // Calculate viewport bounds (including scroll)
            const viewportRight = scrollX + window.innerWidth;
            const viewportBottom = scrollY + window.innerHeight;

            // Ensure the menu stays within the visible viewport
            if (adjustedX + menuWidth > viewportRight) adjustedX = viewportRight - menuWidth;
            if (adjustedY + menuHeight > viewportBottom) adjustedY = viewportBottom - menuHeight;

            // Prevent negative positioning
            adjustedX = Math.max(adjustedX, scrollX);
            adjustedY = Math.max(adjustedY, scrollY);
        } else {
            const optionsButton = document.getElementById('modal-options');
            const buttonRect = optionsButton.getBoundingClientRect();
            const scrollX = window.scrollX || window.pageXOffset;
            const scrollY = window.scrollY || window.pageYOffset;

            // Calculate viewport bounds
            const viewportRight = scrollX + window.innerWidth;
            const viewportBottom = scrollY + window.innerHeight;

            // Position menu below the button, aligned to the left edge of the button
            adjustedX = buttonRect.left + scrollX;
            adjustedY = buttonRect.bottom + 5 + scrollY;

            // If menu would go off the right edge of viewport, align it to the right edge of the button
            if (adjustedX + menuWidth > viewportRight) {
                adjustedX = buttonRect.right + scrollX - menuWidth;
            }

            // If menu would go off the bottom of viewport, position it above the button
            if (adjustedY + menuHeight > viewportBottom) {
                adjustedY = buttonRect.top + scrollY - menuHeight - 5;
            }

            // Ensure menu doesn't go off the left edge of viewport
            if (adjustedX < scrollX) {
                adjustedX = scrollX + 5;
            }

            // Ensure menu doesn't go off the top of viewport
            if (adjustedY < scrollY) {
                adjustedY = scrollY + 5;
            }
        }

        contextMenu.style.left = `${adjustedX}px`;
        contextMenu.style.top = `${adjustedY}px`;
        contextMenu.style.display = 'block';

        const closeMenu = (event) => {
            if (!contextMenu.contains(event.target)) {
                contextMenu.style.display = 'none';
                document.removeEventListener('click', closeMenu);
            }
        };
        document.addEventListener('click', closeMenu);
    };

    let content;
    if (modalBody.textContent.includes('Host Name')) {
        const hostNameMatch = modalBody.textContent.match(/Host Name:\s*([^\s]+)/);
        const hostname = hostNameMatch ? hostNameMatch[1] : '';
        content = `
            <div class="commands-card">
                <h5 class="card-title">Advanced Host Commands</h5>
                <ul class="command-list">
                    <a href="#" class="command-link" onclick="openCommandModal('checkStatusFormHost.php?hostname=${encodeURIComponent(hostname)}', 'Check Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-cog command-icon"></i>
                            Check Settings
                        </li>
                    </a>
                    <a href="#" class="command-link" onclick="openCommandModal('alarmSettingsHost.php?hostname=${encodeURIComponent(hostname)}', 'Alarm Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-bell command-icon"></i>
                            Alarm Settings
                        </li>
                    </a>
                    <a href="#" class="command-link" onclick="openCommandModal('checkCommandHost.php?hostname=${encodeURIComponent(hostname)}', 'Command Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-terminal command-icon"></i>
                            Command Settings
                        </li>
                    </a>
                </ul>
            </div>
        `;
    } else if (modalBody.textContent.includes('Status Information:')) {
        const serviceName = modalTitle.textContent;
        content = `
            <div class="commands-card">
                <h5 class="card-title">Advanced Service Commands</h5>
                <ul class="command-list">
                    <a href="#" class="command-link" onclick="openCommandModal('checkStatusFormService.php?servicename=${encodeURIComponent(serviceName)}&hostname=${encodeURIComponent(realHostName)}', 'Check Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-cog command-icon"></i>
                            Check Settings
                        </li>
                    </a>
                    <a href="#" class="command-link" onclick="openCommandModal('alarmSettingsService.php?servicename=${encodeURIComponent(serviceName)}&hostname=${encodeURIComponent(realHostName)}', 'Alarm Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-bell command-icon"></i>
                            Alarm Settings
                        </li>
                    </a>
                    <a href="#" class="command-link" onclick="openCommandModal('checkCommandService.php?servicename=${encodeURIComponent(serviceName)}&hostname=${encodeURIComponent(realHostName)}', 'Command Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-terminal command-icon"></i>
                            Command Settings
                        </li>
                    </a>
                </ul>
            </div>
        `;
    }

    if (content) {
        showContextMenu(content, e);
    }
};

// Function to fetch all hostgroups for relocate functionality
async function fetchAllHostGroups() {
    const url = 'get_hostgroups_from_db.php';
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'text/plain',
            },
        });
        
        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
        
        const text = await response.text();
        return text.trim().split('\n').filter(line => line.length > 0);
    } catch (error) {
        console.error('Error fetching all host groups:', error);
        return [];
    }
}

// Function to open parent options for a host
function openParentOptionsApm(hostname) {
    // Close any open context menus
    const contextMenu = document.getElementById('custom-context-menu');
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
    
    // Get the infra parameter from URL
    const urlParams = new URLSearchParams(window.location.search);
    const infra = encodeURIComponent(urlParams.get('infra') || '');
    
    // Open the parent options modal
    const url = `checkCommandHost.php?hostname=${encodeURIComponent(hostname)}&infra=${infra}&section=parents`;
    openCommandModal(url, 'Parent Options');
}

// Function to show hostgroup selection modal for relocate functionality
async function showHostGroupModalApm(hostname) {
    // Close any open context menus
    const contextMenu = document.getElementById('custom-context-menu');
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
    
    try {
        const hostGroups = await fetchAllHostGroups();
        
        // Get host IP from URL parameters for the move operation
        const urlParams = new URLSearchParams(window.location.search);
        const hostIp = urlParams.get('hostip') || urlParams.get('ip') || '';
        
        if (!hostIp) {
            alert('Error: Could not determine host IP address.');
            return;
        }
        
        // Create modal HTML using the new host-context-relocate-modal classes
        const modalHTML = `
            <div id="hostgroup-modal-apm" class="host-context-relocate-modal" style="display: flex;">
                <div class="host-context-relocate-modal-content">
                    <span class="host-context-relocate-modal-close" onclick="closeHostGroupModalApm()">&times;</span>
                    <h3>Select Host Group</h3>
                    <div class="context-info">Moving host: ${hostname}${hostIp !== hostname ? ` (${hostIp})` : ''}</div>
                    <div style="display: flex; gap: 8px; margin-bottom: 15px;">
                        <input type="text" id="hostgroup-search-apm" placeholder="Search host groups..." style="flex: 1;">
                        <button id="add-hostgroup-btn-apm" title="Add new hostgroup">+</button>
                    </div>
                    <div id="hostgroup-list-apm" style="max-height: 300px; overflow-y: auto;"></div>
                </div>
            </div>
        `;
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Prevent body scrolling when modal is open
        document.body.style.overflow = 'hidden';
        
        // Setup event listeners
        const modal = document.getElementById('hostgroup-modal-apm');
        const searchInput = document.getElementById('hostgroup-search-apm');
        const addBtn = document.getElementById('add-hostgroup-btn-apm');
        const listContainer = document.getElementById('hostgroup-list-apm');
        
        // Render host groups
        const renderGroups = (filter = '') => {
            const filteredGroups = hostGroups.filter(group => {
                const [, alias] = group.split('. ', 2);
                return alias.toLowerCase().includes(filter.toLowerCase());
            });
            
            listContainer.innerHTML = filteredGroups.length ? 
                filteredGroups.map(group => {
                    const [id, alias] = group.split('. ', 2);
                    return `<div class="hostgroup-item" data-id="${id}">${alias}</div>`;
                }).join('') :
                '<div style="padding: 8px 12px; color: var(--text-secondary);">No host groups found</div>';
        };
        
        // Search functionality
        searchInput.addEventListener('input', (e) => renderGroups(e.target.value));
        
        // Add hostgroup functionality
        addBtn.addEventListener('click', async () => {
            const name = prompt('Enter new hostgroup name:');
            if (name) {
                try {
                    const response = await fetch('add_hostgroup.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `hostgroup_name=${encodeURIComponent(name)}&alias=${encodeURIComponent(name)}&config_id=1`
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        // Refresh the host groups list
                        const newHostGroups = await fetchAllHostGroups();
                        hostGroups.length = 0; // Clear the array
                        hostGroups.push(...newHostGroups); // Add new data
                        renderGroups(searchInput.value); // Re-render with current filter
                        
                        // Show success message
                        const successMsg = document.createElement('div');
                        successMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:var(--success);color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
                        successMsg.textContent = `Hostgroup "${name}" added successfully!`;
                        document.body.appendChild(successMsg);
                        setTimeout(() => successMsg.remove(), 3000);
                    } else {
                        alert(`Error: ${data.message}`);
                    }
                } catch (error) {
                    console.error('Error adding hostgroup:', error);
                    alert('Error adding hostgroup');
                }
            }
        });
        
        // Host group selection
        listContainer.addEventListener('click', async (e) => {
            if (e.target.classList.contains('hostgroup-item')) {
                const hostGroupId = e.target.dataset.id;
                const hostGroupName = e.target.textContent;
                
                const confirmed = confirm(`Move host "${hostname}" to hostgroup "${hostGroupName}"?`);
                if (!confirmed) return;
                
                // Close modal first to restore scrolling
                closeHostGroupModalApm();
                await moveHostToGroupApm(hostIp, hostGroupId, hostGroupName);
            }
        });
        
        // Close on outside click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeHostGroupModalApm();
        });
        
        // Initial render
        renderGroups();
        searchInput.focus();
        
    } catch (error) {
        console.error('Error showing host group modal:', error);
        alert('Error loading host groups');
    }
}

// Function to close hostgroup modal
function closeHostGroupModalApm() {
    const modal = document.getElementById('hostgroup-modal-apm');
    if (modal) {
        modal.remove();
        // Restore body scrolling
        document.body.style.overflow = 'auto';
    }
}

// Function to move host to group
async function moveHostToGroupApm(hostIp, hostGroupId, hostGroupName) {
    const loadingDiv = document.createElement('div');
    loadingDiv.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.8);color:white;padding:20px;border-radius:8px;z-index:10000;';
    loadingDiv.innerHTML = `Moving host... <div style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:30px;height:30px;animation:spin 1s linear infinite;margin:10px auto;"></div>`;
    document.body.appendChild(loadingDiv);
    
    // Add spin animation if not already present
    if (!document.getElementById('spinner-styles-apm')) {
        const style = document.createElement('style');
        style.id = 'spinner-styles-apm';
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }
    
    try {
        const response = await fetch('move_host_to_group.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `ips=${encodeURIComponent(JSON.stringify([hostIp]))}&hostGroupId=${encodeURIComponent(hostGroupId)}`
        });
        
        const result = await response.json();
        if (result.success) {
            // Show success message
            const successMsg = document.createElement('div');
            successMsg.style.cssText = 'position:fixed;top:20px;right:20px;background:var(--success);color:white;padding:10px 15px;border-radius:4px;z-index:10001;font-size:14px;';
            successMsg.textContent = `Host moved to "${hostGroupName}" successfully!`;
            document.body.appendChild(successMsg);
            setTimeout(() => successMsg.remove(), 5000);
            
            if (result.details?.errorCount > 0) {
                alert(`${result.message}\n\nErrors:\n${result.details.errors.join('\n')}`);
            }
        } else {
            alert(`Error: ${result.message}`);
        }
    } catch (error) {
        console.error('Error moving host:', error);
        alert('An error occurred while moving the host');
    } finally {
        document.body.removeChild(loadingDiv);
    }
}

// Make functions globally accessible
window.openParentOptionsApm = openParentOptionsApm;
window.showHostGroupModalApm = showHostGroupModalApm;
window.closeHostGroupModalApm = closeHostGroupModalApm;
window.moveHostToGroupApm = moveHostToGroupApm;

// Attach event listeners
document.getElementById('modal-body').addEventListener('contextmenu', (e) => handleContextMenu(e, 'contextmenu'));
document.getElementById('modal-options').addEventListener('click', (e) => handleContextMenu(e, 'click'));
document.addEventListener('click', () => {
    document.getElementById('custom-context-menu').style.display = 'none';
});