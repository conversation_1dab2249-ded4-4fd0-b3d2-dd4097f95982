<?php
// generateAvailabilityReport.php - generates host-specific availability reports
// Expected POST params: hostname, startTs, endTs, hostIp (optional)

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Load environment variables (DB/Nagios credentials)
require_once dirname(__DIR__, 2) . '/loadenv.php';

// ------------------------- Helper functions (shared with sendScheduledReport.php) -------------------------------
/**
 * Get DB connection to 'blesk' for retrieving user credentials
 */
function getDatabaseConnectionAdminUser(): mysqli {
    $conn = new mysqli($_ENV['DB_SERVER'], $_ENV['DB_USER'], $_ENV['DB_PASSWORD'], 'blesk');
    if ($conn->connect_error) {
        die('DB connection failed: ' . $conn->connect_error);
    }
    return $conn;
}

/**
 * Fetch Nagios HTTP basic auth credentials (user_id=1)
 */
function getUserCredentials(): ?array {
    $conn = getDatabaseConnectionAdminUser();
    $result = $conn->query("SELECT username, password FROM users WHERE user_id = 1 LIMIT 1");
    $cred = null;
    if ($result && $row = $result->fetch_assoc()) {
        $cred = ['user' => $row['username'], 'pass' => $row['password']];
    }
    $conn->close();
    return $cred;
}

/**
 * Determine self IP address (stored by bubblemaps)
 */
function getSelfIp(): string {
    $ip = trim(@file_get_contents('/etc/sysconfig/ipaddr'));
    if (!$ip) die("Unable to determine self IP");
    return $ip;
}

// -----------------------------------------------------------------------------
// Configuration / constants
// -----------------------------------------------------------------------------
$FPDF_PATH     = __DIR__ . '/../reportsFunctions/fpdf.php'; // FPDF library path
$NAGIOS_BASE   = 'https://' . getSelfIp(); // Use real host/IP instead of localhost
$TMP_DIR       = sys_get_temp_dir();

// -----------------------------------------------------------------------------
// Get POST parameters
// -----------------------------------------------------------------------------
$hostname = isset($_POST['hostname']) ? trim($_POST['hostname']) : '';
$startTs = isset($_POST['startTs']) ? intval($_POST['startTs']) : 0;
$endTs = isset($_POST['endTs']) ? intval($_POST['endTs']) : 0;
$hostIp = isset($_POST['hostIp']) ? trim($_POST['hostIp']) : '';

// Validate parameters
if (empty($hostname)) {
    echo json_encode(['success' => false, 'message' => 'Hostname is required']);
    exit;
}

if ($startTs <= 0 || $endTs <= 0 || $endTs <= $startTs) {
    echo json_encode(['success' => false, 'message' => 'Invalid time range']);
    exit;
}

// -----------------------------------------------------------------------------
// Helper to fetch Nagios availability data (same as sendScheduledReport.php)
// -----------------------------------------------------------------------------
function fetchJson(string $path): array
{
    $url = $GLOBALS['NAGIOS_BASE'] . $path;

    static $creds = null;
    if ($creds === null) $creds = getUserCredentials();

    // Initialise cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    // Allow self-signed certificates (internal Nagios)
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // Pass basic-auth credentials if available
    if ($creds) {
        curl_setopt($ch, CURLOPT_USERPWD, $creds['user'] . ':' . $creds['pass']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    }

    $raw = curl_exec($ch);
    if ($raw === false) {
        error_log('[generateAvailabilityReport] cURL error: ' . curl_error($ch));
        curl_close($ch);
        return [];
    }

    $http = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($http !== 200) {
        error_log('[generateAvailabilityReport] HTTP ' . $http . ' fetching ' . $url);
        return [];
    }

    $json = json_decode($raw, true);
    if (!is_array($json)) return [];
    return $json['data'] ?? [];
}

// -----------------------------------------------------------------------------
// State label helper function (replicate JavaScript behavior)
// -----------------------------------------------------------------------------
function stateLabel($val, $isHost) {
    if ($val === null || $val === '') return '';

    // Handle both string and numeric values
    if (is_string($val)) {
        return strtoupper($val);
    }

    $num = intval($val);

    if ($isHost) {
        // Host state mapping (based on JavaScript statusMappingsText)
        $states = [
            0 => 'UP',
            1 => 'DOWN',
            2 => 'UNREACHABLE',
            3 => 'PENDING'
        ];
        return $states[$num] ?? 'UNKNOWN';
    } else {
        // Service state mapping (based on JavaScript statusMappingsText)
        $states = [
            0 => 'OK',
            1 => 'WARNING',
            2 => 'CRITICAL',
            3 => 'UNKNOWN',
            4 => 'PENDING'
        ];
        return $states[$num] ?? 'UNKNOWN';
    }
}

// -----------------------------------------------------------------------------
// Fetch data from Nagios APIs (exactly like JavaScript version)
// -----------------------------------------------------------------------------
try {
    // Build URLs exactly like JavaScript
    $hostAvailUrl = "/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&hostname=" . urlencode($hostname) . "&starttime={$startTs}&endtime={$endTs}";
    $svcAvailUrl = "/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=services&hostname=" . urlencode($hostname) . "&starttime={$startTs}&endtime={$endTs}";
    $timelineUrl = "/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=host&hostname=" . urlencode($hostname) . "&starttime={$startTs}&endtime={$endTs}";

    // Fetch data concurrently (simulate Promise.all)
    $hostJs = fetchJson($hostAvailUrl);
    $svcJs = fetchJson($svcAvailUrl);
    $tlJs = fetchJson($timelineUrl);

    // Filter timeline data exactly like JavaScript (remove pseudo-state entries)
    $rawTimelineArr = $tlJs['statechangelist'] ?? [];
    $timelineArr = array_filter($rawTimelineArr, function($entry) {
        return !isset($entry['plugin_output']) || !str_contains($entry['plugin_output'], 'Pseudo-State');
    });

    // Sort by timestamp
    usort($timelineArr, function($a, $b) {
        return $a['timestamp'] - $b['timestamp'];
    });

    // Extract data exactly like JavaScript
    $hostData = $hostJs['host'] ?? null;
    $services = $svcJs['services'] ?? [];

    // Debug: Log first few timeline entries to understand structure
    if (!empty($timelineArr)) {
        error_log('[generateAvailabilityReport] Sample timeline entry: ' . json_encode(array_slice($timelineArr, 0, 2)));
    }

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error fetching data: ' . $e->getMessage()]);
    exit;
}

// -----------------------------------------------------------------------------
// Generate PDF report using FPDF
// -----------------------------------------------------------------------------
$fpdfAvailable = false;
if (!class_exists('FPDF') && file_exists($FPDF_PATH)) {
    require_once $FPDF_PATH;
}
if (class_exists('FPDF')) {
    $fpdfAvailable = true;
}

if (!$fpdfAvailable) {
    echo json_encode(['success' => false, 'message' => 'FPDF library not available']);
    exit;
}

// After FPDF is available, define subclass for header/footer (exactly like JavaScript version)
if (!class_exists('BleskAvailabilityPDF')) {
    class BleskAvailabilityPDF extends FPDF {
        public string $titleText = '';
        public string $rangeText = '';
        public string $logoPath = '';
        public string $hostDisplay = '';
        public int $pageMargin = 20;
        public int $headingY = 20;

        function Header() {
            // Logo (optional) - like reports.php
            if ($this->logoPath && file_exists($this->logoPath)) {
                $this->Image($this->logoPath, 10, 6, 25);
            }

            // Title - like reports.php
            $this->SetFont('Arial', 'B', 16);
            $this->Cell(0, 8, $this->titleText, 0, 1, 'C');

            // Range text - like reports.php
            $this->SetFont('Arial', '', 10);
            $this->Cell(0, 6, $this->rangeText, 0, 1, 'C');

            $this->Ln(5);
        }

        function Footer() {
            $this->SetY(-12);
            $this->SetFont('Arial', 'I', 8);
            $this->Cell(0, 10, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'C');
        }

        // Public helper to get printable width between margins (like reports.php)
        public function ContentWidth(): float {
            return $this->GetPageWidth() - 40; // 20mm margins on each side
        }


        // Helper to get content start Y position
        public function getContentStartY(): int {
            return $this->headingY + 32;
        }
    }
}

// Calculate date range information exactly like JavaScript
$rangeDays = ceil(($endTs - $startTs) / 86400);
$titleRangeTxt = $rangeDays === 1 ? 'Last 24 Hours' : ('Last ' . $rangeDays . ' Days');

// Determine host display name exactly like JavaScript
$ipRegex = '/^(?:\d{1,3}\.){3}\d{1,3}$/';
$hostDisplay = preg_match($ipRegex, $hostname) ? $hostname : ($hostIp ? "{$hostname} ({$hostIp})" : $hostname);

// Create PDF instance exactly like JavaScript (landscape orientation)
$pdf = new BleskAvailabilityPDF('L', 'mm', 'A4');
$pdf->AliasNbPages();
$pdf->SetAuthor('Blesk');
$pdf->titleText = "{$hostDisplay} Availability Report ({$titleRangeTxt})";
$pdf->rangeText = 'Range: ' . date('Y-m-d H:i', $startTs) . ' - ' . date('Y-m-d H:i', $endTs);
$pdf->hostDisplay = $hostDisplay;

// Use logo path (try to find the logo)
$logoPath = __DIR__ . '/../reportsFunctions/blesk-logo-black_ret.png';
if (file_exists($logoPath)) {
    $pdf->logoPath = $logoPath;
}

$pdf->SetTitle($pdf->titleText);
$pdf->AddPage();

$contentStartY = $pdf->getContentStartY();
$pageWidth = $pdf->GetPageWidth();
$pageMargin = $pdf->pageMargin;

// -----------------------------------------------------------------------------
// Host availability summary (like reports.php)
// -----------------------------------------------------------------------------
if ($hostData) {
    $up = ($hostData['time_up'] ?? 0) + ($hostData['scheduled_time_up'] ?? 0);
    $down = ($hostData['time_down'] ?? 0) + ($hostData['scheduled_time_down'] ?? 0);
    $unreach = ($hostData['time_unreachable'] ?? 0) + ($hostData['scheduled_time_unreachable'] ?? 0);
    $total = $up + $down + $unreach;

    $pct = function($v) use ($total) {
        return $total ? number_format(($v / $total) * 100, 1) : '0.0';
    };

    // Section title like reports.php
    $pdf->SetY($contentStartY);
    $pdf->SetFont('Arial', 'B', 14);
    $pdf->SetFillColor(240, 240, 240);
    $pdf->SetTextColor(51, 51, 51);
    $pdf->Cell(0, 10, 'Host Availability Summary', 0, 1, 'L', true);
    $pdf->Ln(3);

    // Table headers like reports.php
    $headers = ['Host', 'Up %', 'Down %', 'Unreach %'];
    $contentWidth = $pdf->ContentWidth();
    $pctW = 25; // fixed width for percentage columns
    $hostW = $contentWidth - (3 * $pctW);
    $widths = [$hostW, $pctW, $pctW, $pctW];

    $pdf->SetFont('Arial', 'B', 9);
    $pdf->SetFillColor(245, 245, 245);
    $pdf->SetTextColor(51, 51, 51);
    foreach ($headers as $idx => $hdr) {
        $pdf->Cell($widths[$idx], 7, $hdr, 1, 0, 'C', true);
    }
    $pdf->Ln();

    // Helper function for percentage cells with color highlighting
    $pctCell = function($value, $width, $type) use ($pdf) {
        $fill = false;
        if ($value > 0) {
            switch ($type) {
                case 'up':
                    $pdf->SetFillColor(165, 214, 167); // green
                    break;
                case 'down':
                    $pdf->SetFillColor(229, 115, 115); // red
                    break;
                case 'unknown':
                    $pdf->SetFillColor(169, 182, 201); // grey
                    break;
            }
            $fill = true;
            $pdf->SetTextColor(51, 51, 51);
        }
        $pdf->Cell($width, 6, number_format($value, 1), 1, 0, 'R', $fill);
        if ($fill) $pdf->SetTextColor(0); // reset
    };

    // Table data with color highlighting
    $pdf->SetFont('Arial', '', 9);
    $pdf->Cell($widths[0], 6, $hostDisplay, 1);
    $pctCell(floatval($pct($up)), $widths[1], 'up');
    $pctCell(floatval($pct($down)), $widths[2], 'down');
    $pctCell(floatval($pct($unreach)), $widths[3], 'unknown');
    $pdf->Ln();
    $pdf->Ln(5);
}

// -----------------------------------------------------------------------------
// Host state changes (timeline + table) - like reports.php
// -----------------------------------------------------------------------------
if (!empty($timelineArr)) {
    // Section title like reports.php
    $pdf->SetFont('Arial', 'B', 14);
    $pdf->SetFillColor(240, 240, 240);
    $pdf->SetTextColor(51, 51, 51);
    $pdf->Cell(0, 10, 'Host State Timeline', 0, 1, 'L', true);
    $pdf->Ln(3);

    $curY = $pdf->GetY();

    // Draw timeline bar exactly like JavaScript
    $barHeight = 8;
    $barWidth = $pdf->ContentWidth();
    $barX = $pdf->GetX();
    $startMs = $startTs * 1000;
    $endMs = $endTs * 1000;
    $duration = $endMs - $startMs;

    // Color map exactly like JavaScript (handle both string and numeric states)
    $colorMap = [
        // String states
        'UP' => [165, 214, 167], 'up' => [165, 214, 167],
        'DOWN' => [229, 115, 115], 'down' => [229, 115, 115],
        'UNREACHABLE' => [169, 182, 201], 'unreachable' => [169, 182, 201],
        'PENDING' => [255, 193, 7], 'pending' => [255, 193, 7],
        // Numeric states (0=UP, 1=DOWN, 2=UNREACHABLE, 3=PENDING)
        '0' => [165, 214, 167], 0 => [165, 214, 167],
        '1' => [229, 115, 115], 1 => [229, 115, 115],
        '2' => [169, 182, 201], 2 => [169, 182, 201],
        '3' => [255, 193, 7], 3 => [255, 193, 7]
    ];

    // Draw timeline background
    $pdf->SetFillColor(245, 245, 245);
    $pdf->Rect($barX, $curY, $barWidth, $barHeight, 'F');
    $pdf->SetDrawColor(220, 220, 220);
    $pdf->SetLineWidth(0.5);
    $pdf->Rect($barX, $curY, $barWidth, $barHeight, 'D');

    // Draw timeline segments exactly like JavaScript
    for ($i = 0; $i < count($timelineArr); $i++) {
        $cur = $timelineArr[$i];
        // Handle timestamp format - convert to milliseconds if needed
        $curTimestamp = $cur['timestamp'];
        if ($curTimestamp <= 9999999999) { // If timestamp is in seconds
            $curTimestamp = $curTimestamp * 1000;
        }
        $segStart = $i === 0 ? $startMs : $curTimestamp;

        $nextTimestamp = $endMs;
        if ($i < count($timelineArr) - 1) {
            $nextTimestamp = $timelineArr[$i + 1]['timestamp'];
            if ($nextTimestamp <= 9999999999) { // If timestamp is in seconds
                $nextTimestamp = $nextTimestamp * 1000;
            }
        }
        $segEnd = $nextTimestamp;

        // Clip to range
        $s = max($segStart, $startMs);
        $e = min($segEnd, $endMs);
        if ($e <= $s) continue;

        $x = $barX + (($s - $startMs) / $duration) * $barWidth;
        $w = max(1, (($e - $s) / $duration) * $barWidth);

        // Get state value - try multiple field names
        $stateVal = $cur['state'] ?? $cur['current_state'] ?? $cur['state_text'] ?? 'unknown';
        $col = $colorMap[$stateVal] ?? [200, 200, 200];

        // Draw segment with gradient effect like JavaScript
        $pdf->SetFillColor($col[0], $col[1], $col[2]);
        $pdf->Rect($x, $curY + 1, $w, $barHeight - 2, 'F');

        // Add subtle highlight on top
        $lighterCol = array_map(function($c) { return min(255, $c + 20); }, $col);
        $pdf->SetFillColor($lighterCol[0], $lighterCol[1], $lighterCol[2]);
        $pdf->Rect($x, $curY + 1, $w, 2, 'F');

        // Add segment borders for definition
        if ($w > 2) {
            $pdf->SetDrawColor(0, 0, 0);
            $pdf->SetLineWidth(0.2);
            $pdf->Line($x, $curY + 1, $x, $curY + $barHeight - 1);
            $pdf->Line($x + $w, $curY + 1, $x + $w, $curY + $barHeight - 1);
        }
    }

    $curY += $barHeight + 6;

    // Table of changes like reports.php
    $pdf->SetY($curY);
    $pdf->SetFont('Arial', 'B', 9);
    $pdf->SetFillColor(245, 245, 245);
    $pdf->SetTextColor(51, 51, 51);

    // Headers with proper widths
    $contentWidth = $pdf->ContentWidth();
    $stateColWidth = 40;
    $timeColWidth = $contentWidth - $stateColWidth;
    $pdf->Cell($timeColWidth, 7, 'Timestamp', 1, 0, 'C', true);
    $pdf->Cell($stateColWidth, 7, 'State', 1, 0, 'C', true);
    $pdf->Ln();

    // Data rows
    $pdf->SetFont('Arial', '', 9);
    $pdf->SetFillColor(255, 255, 255);
    foreach ($timelineArr as $sc) {
        // Try multiple ways to get the state text
        $stTxt = '';
        if (isset($sc['state_text']) && !empty($sc['state_text'])) {
            $stTxt = strtoupper($sc['state_text']);
        } elseif (isset($sc['state'])) {
            $stTxt = stateLabel($sc['state'], true);
        } elseif (isset($sc['current_state'])) {
            $stTxt = stateLabel($sc['current_state'], true);
        } else {
            $stTxt = 'UNKNOWN';
        }

        // Handle timestamp - if it's in milliseconds, convert to seconds
        $timestamp = $sc['timestamp'];
        if ($timestamp > 9999999999) { // If timestamp is in milliseconds (> year 2286 in seconds)
            $timestamp = $timestamp / 1000;
        }
        $pdf->Cell($timeColWidth, 6, date('Y-m-d H:i:s', $timestamp), 1, 0, 'L');
        $pdf->Cell($stateColWidth, 6, $stTxt, 1, 0, 'C');
        $pdf->Ln();
    }
    $pdf->Ln(5);
}

// -----------------------------------------------------------------------------
// Service availability summary (like reports.php)
// -----------------------------------------------------------------------------
if (!empty($services)) {
    // Add new page for clarity
    $pdf->AddPage();

    // Section title like reports.php
    $pdf->SetY($contentStartY);
    $pdf->SetFont('Arial', 'B', 14);
    $pdf->SetFillColor(240, 240, 240);
    $pdf->SetTextColor(51, 51, 51);
    $pdf->Cell(0, 10, 'Service Availability Summary', 0, 1, 'L', true);
    $pdf->Ln(3);

    // Host title row like reports.php
    $pdf->SetFont('Arial', 'B', 11);
    $pdf->SetFillColor(250, 250, 250);
    $pdf->SetTextColor(51, 51, 51);
    $contentWidth = $pdf->ContentWidth();
    $pdf->Cell($contentWidth, 8, $hostDisplay, 0, 1, 'L', true);

    // Table headers like reports.php
    $headers = ['Service', 'OK %', 'Warn %', 'Crit %', 'Unk %'];
    $pctW = 25; // fixed width for percentage columns
    $svcW = $contentWidth - (4 * $pctW);
    $widths = [$svcW, $pctW, $pctW, $pctW, $pctW];

    $pdf->SetFont('Arial', 'B', 9);
    $pdf->SetFillColor(245, 245, 245);
    $pdf->SetTextColor(51, 51, 51);
    foreach ($headers as $idx => $hdr) {
        $pdf->Cell($widths[$idx], 7, $hdr, 1, 0, 'C', true);
    }
    $pdf->Ln();

    // Helper function for percentage cells with color highlighting
    $pctCell = function($value, $width, $type) use ($pdf) {
        $fill = false;
        if ($value > 0) {
            switch ($type) {
                case 'ok':
                    $pdf->SetFillColor(165, 214, 167); // green
                    break;
                case 'warn':
                    $pdf->SetFillColor(255, 224, 130); // yellow
                    break;
                case 'crit':
                    $pdf->SetFillColor(229, 115, 115); // red
                    break;
                case 'unknown':
                    $pdf->SetFillColor(169, 182, 201); // grey
                    break;
            }
            $fill = true;
            $pdf->SetTextColor(51, 51, 51);
        }
        $pdf->Cell($width, 6, number_format($value, 1), 1, 0, 'R', $fill);
        if ($fill) $pdf->SetTextColor(0); // reset
    };

    // Data rows with color highlighting
    $pdf->SetFont('Arial', '', 9);
    foreach ($services as $svc) {
        // Include scheduled time values in total calculation
        $total = ($svc['time_ok'] ?? 0) + ($svc['time_warning'] ?? 0) + ($svc['time_critical'] ?? 0) + ($svc['time_unknown'] ?? 0) +
                 ($svc['scheduled_time_ok'] ?? 0) + ($svc['scheduled_time_warning'] ?? 0) + ($svc['scheduled_time_critical'] ?? 0) + ($svc['scheduled_time_unknown'] ?? 0);
        $okPct = $total ? ((($svc['time_ok'] ?? 0) + ($svc['scheduled_time_ok'] ?? 0)) / $total) * 100 : 0;
        $warnPct = $total ? ((($svc['time_warning'] ?? 0) + ($svc['scheduled_time_warning'] ?? 0)) / $total) * 100 : 0;
        $critPct = $total ? ((($svc['time_critical'] ?? 0) + ($svc['scheduled_time_critical'] ?? 0)) / $total) * 100 : 0;
        $unkPct = $total ? ((($svc['time_unknown'] ?? 0) + ($svc['scheduled_time_unknown'] ?? 0)) / $total) * 100 : 0;

        $pdf->Cell($widths[0], 6, $svc['description'] ?? '-', 1);
        $pctCell($okPct, $widths[1], 'ok');
        $pctCell($warnPct, $widths[2], 'warn');
        $pctCell($critPct, $widths[3], 'crit');
        $pctCell($unkPct, $widths[4], 'unknown');
        $pdf->Ln();
    }
    $pdf->Ln(5);
}

// -----------------------------------------------------------------------------
// Service state change timelines (exactly like JavaScript version)
// -----------------------------------------------------------------------------
if (!empty($services)) {
    // Fetch service timelines exactly like JavaScript
    $svcColorMap = [
        // String states
        'OK' => [165, 214, 167], 'ok' => [165, 214, 167],
        'WARNING' => [255, 224, 130], 'warning' => [255, 224, 130],
        'CRITICAL' => [229, 115, 115], 'critical' => [229, 115, 115],
        'UNKNOWN' => [169, 182, 201], 'unknown' => [169, 182, 201],
        'PENDING' => [255, 193, 7], 'pending' => [255, 193, 7],
        // Numeric states (0=OK, 1=WARNING, 2=CRITICAL, 3=UNKNOWN, 4=PENDING)
        '0' => [165, 214, 167], 0 => [165, 214, 167],
        '1' => [255, 224, 130], 1 => [255, 224, 130],
        '2' => [229, 115, 115], 2 => [229, 115, 115],
        '3' => [169, 182, 201], 3 => [169, 182, 201],
        '4' => [255, 193, 7], 4 => [255, 193, 7]
    ];

    foreach ($services as $svc) {
        // Fetch timeline for this service
        $svcTimelineUrl = "/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=service&hostname=" . urlencode($hostname) . "&servicedescription=" . urlencode($svc['description']) . "&starttime={$startTs}&endtime={$endTs}";
        $svcTlJs = fetchJson($svcTimelineUrl);

        // Filter and sort exactly like JavaScript
        $rawList = $svcTlJs['statechangelist'] ?? [];
        $filteredList = array_filter($rawList, function($entry) {
            return !isset($entry['plugin_output']) || !str_contains($entry['plugin_output'], 'Pseudo-State');
        });
        usort($filteredList, function($a, $b) {
            return $a['timestamp'] - $b['timestamp'];
        });

        if (empty($filteredList)) continue;

        // Add new page for each service like JavaScript
        $pdf->AddPage();
        $curY = $contentStartY;

        // Service title like reports.php
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->SetFillColor(240, 240, 240);
        $pdf->SetTextColor(51, 51, 51);
        $pdf->Cell(0, 10, "Service Timeline - {$svc['description']}", 0, 1, 'L', true);
        $pdf->Ln(3);
        $curY = $pdf->GetY();

        // Draw timeline bar exactly like JavaScript
        $barHeight = 8;
        $barWidth = $pdf->ContentWidth();
        $barX = $pdf->GetX();
        $startMs = $startTs * 1000;
        $endMs = $endTs * 1000;
        $duration = $endMs - $startMs;

        // Draw timeline background
        $pdf->SetFillColor(245, 245, 245);
        $pdf->Rect($barX, $curY, $barWidth, $barHeight, 'F');
        $pdf->SetDrawColor(220, 220, 220);
        $pdf->SetLineWidth(0.5);
        $pdf->Rect($barX, $curY, $barWidth, $barHeight, 'D');

        // Draw timeline segments
        for ($i = 0; $i < count($filteredList); $i++) {
            $ev = $filteredList[$i];
            // Handle timestamp format - convert to milliseconds if needed
            $evTimestamp = $ev['timestamp'];
            if ($evTimestamp <= 9999999999) { // If timestamp is in seconds
                $evTimestamp = $evTimestamp * 1000;
            }
            $segStart = $i === 0 ? $startMs : $evTimestamp;

            $nextTimestamp = $endMs;
            if ($i < count($filteredList) - 1) {
                $nextTimestamp = $filteredList[$i + 1]['timestamp'];
                if ($nextTimestamp <= 9999999999) { // If timestamp is in seconds
                    $nextTimestamp = $nextTimestamp * 1000;
                }
            }
            $segEnd = $nextTimestamp;
            $s = max($segStart, $startMs);
            $e = min($segEnd, $endMs);
            if ($e <= $s) continue;

            $x = $barX + (($s - $startMs) / $duration) * $barWidth;
            $w = max(1, (($e - $s) / $duration) * $barWidth);

            // Get state value - try multiple field names
            $stateVal = $ev['state'] ?? $ev['current_state'] ?? $ev['state_text'] ?? 'unknown';
            $col = $svcColorMap[$stateVal] ?? [200, 200, 200];

            // Draw segment with gradient effect
            $pdf->SetFillColor($col[0], $col[1], $col[2]);
            $pdf->Rect($x, $curY + 1, $w, $barHeight - 2, 'F');

            // Add subtle highlight on top
            $lighterCol = array_map(function($c) { return min(255, $c + 20); }, $col);
            $pdf->SetFillColor($lighterCol[0], $lighterCol[1], $lighterCol[2]);
            $pdf->Rect($x, $curY + 1, $w, 2, 'F');

            // Add segment borders for definition
            if ($w > 2) {
                $pdf->SetDrawColor(0, 0, 0);
                $pdf->SetLineWidth(0.2);
                $pdf->Line($x, $curY + 1, $x, $curY + $barHeight - 1);
                $pdf->Line($x + $w, $curY + 1, $x + $w, $curY + $barHeight - 1);
            }
        }

        $curY += $barHeight + 6;

        // Table of changes like reports.php
        $pdf->SetY($curY);
        $pdf->SetFont('Arial', 'B', 9);
        $pdf->SetFillColor(245, 245, 245);
        $pdf->SetTextColor(51, 51, 51);

        // Headers with proper widths
        $contentWidth = $pdf->ContentWidth();
        $stateColWidth = 40;
        $timeColWidth = $contentWidth - $stateColWidth;
        $pdf->Cell($timeColWidth, 7, 'Timestamp', 1, 0, 'C', true);
        $pdf->Cell($stateColWidth, 7, 'State', 1, 0, 'C', true);
        $pdf->Ln();

        // Data rows
        $pdf->SetFont('Arial', '', 9);
        $pdf->SetFillColor(255, 255, 255);
        foreach ($filteredList as $ev) {
            // Try multiple ways to get the state text
            $stTxt = '';
            if (isset($ev['state_text']) && !empty($ev['state_text'])) {
                $stTxt = strtoupper($ev['state_text']);
            } elseif (isset($ev['state'])) {
                $stTxt = stateLabel($ev['state'], false);
            } elseif (isset($ev['current_state'])) {
                $stTxt = stateLabel($ev['current_state'], false);
            } else {
                $stTxt = 'UNKNOWN';
            }

            // Handle timestamp - if it's in milliseconds, convert to seconds
            $timestamp = $ev['timestamp'];
            if ($timestamp > 9999999999) { // If timestamp is in milliseconds (> year 2286 in seconds)
                $timestamp = $timestamp / 1000;
            }
            $pdf->Cell($timeColWidth, 6, date('Y-m-d H:i:s', $timestamp), 1, 0, 'L');
            $pdf->Cell($stateColWidth, 6, $stTxt, 1, 0, 'C');
            $pdf->Ln();
        }
    }
}

// -----------------------------------------------------------------------------
// Finalize PDF and output (exactly like JavaScript version)
// -----------------------------------------------------------------------------
// Headers and footers are automatically handled by FPDF Header() method

// Generate filename exactly like JavaScript
$ts = date('Y-m-d-H-i-s');
$filename = "availability_report_{$hostname}_{$ts}.pdf";
$filepath = $TMP_DIR . '/' . $filename;

// Output PDF to file
$pdf->Output('F', $filepath);

// Check if file was created successfully
if (!file_exists($filepath)) {
    echo json_encode(['success' => false, 'message' => 'Failed to generate PDF file']);
    exit;
}

// Return success response with download information
echo json_encode([
    'success' => true,
    'message' => 'Report generated successfully',
    'filename' => $filename,
    'filepath' => $filepath,
    'download_url' => 'functions/hostApmFunctions/downloadReport.php?file=' . urlencode($filename)
]);
?>