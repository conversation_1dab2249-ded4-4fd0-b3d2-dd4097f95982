// SPM (Switch Port Manager) Handler
// Handles fetching and displaying network connectivity information from Netdisco

// Function to fetch SPM data for a host
async function fetchSpmData(hostIp) {
    try {
        const response = await fetch(`get_spm_data.php?ip=${encodeURIComponent(hostIp)}`);
        
        // Handle 500 status code specifically
        if (response.status === 500) {
            const errorData = await response.json();
            return {
                success: false,
                error: errorData.error || 'SPM module is not available',
                parent_connections: [],
                child_connections: []
            };
        }
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        return data;
    } catch (error) {
        console.error('Error fetching SPM data:', error);
        return {
            success: false,
            error: error.message,
            parent_connections: [],
            child_connections: []
        };
    }
}

// Function to format port status with icons and colors
function formatPortStatus(status) {
    if (status === 'Up') {
        return '<span class="spm-status-badge spm-status-up"><i class="fa fa-arrow-up"></i></span>';
    } else {
        return '<span class="spm-status-badge spm-status-down"><i class="fa fa-arrow-down"></i></span>';
    }
}

/* --- Connectivity Host Linking Enhancements --- */
// Cache of bubblemaps hostnames indexed by IP
let bubbleHostnamesMap = null;
// Cache of Nagios hostnames indexed by IP
let nagiosHostnamesMap = null;

/**
 * Fetch bubblemaps hostnames map (IP => nickname) once per page load.
 */
async function fetchBubbleHostnames() {
    if (bubbleHostnamesMap !== null) {
        return bubbleHostnamesMap;
    }
    try {
        const resp = await fetch('get_bubble_hostnames.php');
        if (resp.ok) {
            bubbleHostnamesMap = await resp.json();
        } else {
            bubbleHostnamesMap = {};
        }
    } catch (e) {
        console.error('Failed loading bubblemaps hostnames:', e);
        bubbleHostnamesMap = {};
    }
    return bubbleHostnamesMap;
}

/**
 * Fetch all Nagios hosts once per page load.
 */
async function fetchNagiosHostnames() {
    if (nagiosHostnamesMap !== null) return nagiosHostnamesMap;
    nagiosHostnamesMap = {};
    try {
        const url = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true`;
        const resp = await fetch(url);
        if (resp.ok) {
            const data = await resp.json();
            for (const hn in data.data.hostlist) {
                const host = data.data.hostlist[hn];
                nagiosHostnamesMap[host.address] = host.name;
            }
        }
    } catch (e) {
        console.warn('Failed to preload Nagios hostlist', e);
    }
    return nagiosHostnamesMap;
}

/**
 * Generate a host.php link for the given IP if the IP exists in bubblemaps.
 * Otherwise returns the plain IP string.
 */
function generateHostLink(ipString) {
    if (!ipString) return '';
    // Split in case multiple IPs separated by comma / semicolon / whitespace
    const ipParts = ipString.split(/[,;\s]+/).filter(Boolean);
    const linkedParts = ipParts.map(rawIp => {
        const cleanIp = rawIp.trim();
        if (cleanIp.toLowerCase() === 'n/a') return cleanIp;
        if (nagiosHostnamesMap && nagiosHostnamesMap[cleanIp]) {
            return `<a style="color: #4a9eff;" href="#" data-hostip="${cleanIp}" onclick="openHostFromConnectivity(event, '${cleanIp}')">${cleanIp}</a>`;
        }
        return cleanIp;
    });
    return linkedParts.join(', ');
}

// Opens host.php for the given IP, resolving Nagios hostname first
async function openHostFromConnectivity(event, ip) {
    if (event) event.preventDefault();
    await fetchBubbleHostnames();
    const nickname = (bubbleHostnamesMap && bubbleHostnamesMap[ip]) ? bubbleHostnamesMap[ip] : ip;
    
    // Get the real Nagios hostname
    let realHostName = null;
    if (typeof getHostnameByIP === 'function') {
        try {
            realHostName = await getHostnameByIP(ip);
        } catch (e) {
            console.warn('Hostname lookup failed', e);
        }
    }
    
    if (!realHostName) {
        alert('Host not found in Nagios yet. Please wait until it is added.');
        return;
    }
    
    const url = `host.php?nickname=${encodeURIComponent(nickname)}&ip=${encodeURIComponent(realHostName)}&infra=null&hostip=${encodeURIComponent(ip)}&subnet=all`;
    window.location.href = url;
}
/* --- End Connectivity Host Linking Enhancements --- */

// Function to create SPM table HTML
function createSpmTable(connections, title, emptyMessage, isChildConnections = false) {
    if (!connections || connections.length === 0) {
        return `
            <div class="spm-section">
                <h3>${title}</h3>
                <div class="spm-empty">
                    <i class="fa fa-info-circle"></i>
                    <p>${emptyMessage}</p>
                </div>
            </div>
        `;
    }
    
    // Sort connections alphabetically by port
    connections.sort((a, b) => {
        const portA = a.port || '';
        const portB = b.port || '';
        return portA.localeCompare(portB, undefined, { numeric: true, sensitivity: 'base' });
    });
    
    // Generate a unique table ID for CSV export
    const tableId = isChildConnections ? 'spm-child-table' : 'spm-parent-table';
    
    let tableHTML = `
        <div class="spm-section">
            <div class="spm-header-with-export">
                <h3>${title} <i class="fa fa-download spm-export-icon" onclick="exportSpmTableToCSV('${tableId}', '${title.replace(/'/g, "\\'")}')" title="Export to CSV"></i></h3>
            </div>
            <div class="spm-table-container">
                <table class="spm-table" id="${tableId}">
                    <thead>
                        <tr>
                            <th title="Status">Status</th>
                            ${isChildConnections ? '<th title="Switch IP">Switch IP</th><th title="Switch Name">Switch Name</th>' : ''}
                            <th title="Port">Port</th>
                            <th title="Name">Name</th>
                            <th title="Description">Description</th>
                            <th title="Speed">Speed</th>
                            <th title="Native VLAN">Native VLAN</th>
                            <th title="VLAN Membership">VLAN Membership</th>
                            ${isChildConnections ? '' : '<th title="Device IP">Device IP</th>'}
                            <th title="Type">Type</th>
                            <th title="Last Seen">Last Seen</th>
                        </tr>
                    </thead>
                    <tbody>
    `;
    
    connections.forEach(conn => {
        const lastSeen = conn.last_seen ? new Date(conn.last_seen).toLocaleString() : 'N/A';
        const deviceIp = conn.child_ip || 'N/A';
        let connectionType = conn.connection_type === 'Unknown' ? 'N/A' : conn.connection_type;
        
        // Convert type to first letter only
        if (connectionType !== 'N/A') {
            connectionType = connectionType.charAt(0).toUpperCase();
        }
        
        if (isChildConnections) {
            // For child connections, show the switch (parent) information
            tableHTML += `
                <tr>
                    <td>${formatPortStatus(conn.port_status)}</td>
                    <td><div class="spm-cell-content">${generateHostLink(conn.parent_ip)}</div></td>
                    <td><div class="spm-cell-content">${conn.parent_name}</div></td>
                    <td><div class="spm-cell-content">${conn.port}</div></td>
                    <td><div class="spm-cell-content">${conn.port_name || '-'}</div></td>
                    <td><div class="spm-cell-content">${conn.port_description || '-'}</div></td>
                    <td><div class="spm-cell-content">${conn.port_speed}</div></td>
                    <td><div class="spm-cell-content">${conn.native_vlan || '-'}</div></td>
                    <td><div class="spm-cell-content">${conn.vlan_membership || '-'}</div></td>
                    <td><span class="spm-type-badge spm-type-${conn.connection_type.toLowerCase()}" title="${conn.connection_type}">${connectionType}</span></td>
                    <td><div class="spm-cell-content">${lastSeen}</div></td>
                </tr>
            `;
        } else {
            // For parent connections, show the connected device information (removed Connected Device column)
            tableHTML += `
                <tr>
                    <td>${formatPortStatus(conn.port_status)}</td>
                    <td><div class="spm-cell-content">${conn.port}</div></td>
                    <td><div class="spm-cell-content">${conn.port_name || '-'}</div></td>
                    <td><div class="spm-cell-content">${conn.port_description || '-'}</div></td>
                    <td><div class="spm-cell-content">${conn.port_speed}</div></td>
                    <td><div class="spm-cell-content">${conn.native_vlan || '-'}</div></td>
                    <td><div class="spm-cell-content">${conn.vlan_membership || '-'}</div></td>
                    <td><div class="spm-cell-content">${generateHostLink(deviceIp)}</div></td>
                    <td><span class="spm-type-badge spm-type-${conn.connection_type.toLowerCase()}" title="${conn.connection_type}">${connectionType}</span></td>
                    <td><div class="spm-cell-content">${lastSeen}</div></td>
                </tr>
            `;
        }
    });
    
    tableHTML += `
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    return tableHTML;
}

// Function to load and display SPM data
async function loadSpmData(hostIp) {
    const spmContainer = document.getElementById('spm-container');
    if (!spmContainer) return;
    
    // Loading state is already shown by initSpm, so we don't need to show it again here
    
    try {
        // Preload host maps (Bubblemaps + Nagios) first
        await fetchBubbleHostnames();
        await fetchNagiosHostnames();
        const data = await fetchSpmData(hostIp);
        
        if (!data.success) {
                    // Check if the error is due to SPM module not being available
        if (data.error && data.error.includes('SPM module is not available')) {
            // Show marketing message in the marketing container next to host card
            const marketingContainer = document.getElementById('spm-marketing-container');
            if (marketingContainer) {
                marketingContainer.innerHTML = `
                    <div class="spm-header">
                        <h2><i class="fa fa-network-wired"></i> Network Connectivity Information</h2>
                    </div>
                    <div class="spm-marketing">
                        <i class="fa fa-info-circle"></i>
                        <p><strong>Switch Port Manager module</strong> is required to view network connectivity information.</p>
                        <p>Upgrade your license to access switch port mappings and device connectivity details.</p>
                    </div>
                `;
                marketingContainer.style.display = 'block';
            }
            // Hide the main SPM container
            spmContainer.style.display = 'none';
            return;
        }
            
            spmContainer.innerHTML = `
                <div class="spm-error">
                    <i class="fa fa-exclamation-triangle"></i>
                    <p>Error loading SPM data: ${data.error}</p>
                </div>
            `;
            return;
        }
        
        // Create the SPM content
        let spmHTML = '';
        
        // Add parent connections (this host as a switch) - always show this section
        if (data.parent_connections && data.parent_connections.length > 0) {
            spmHTML += createSpmTable(
                data.parent_connections,
                'Network interface(s) information on this host',
                'No network interface(s) information found for this host',
                false // isChildConnections = false
            );
        } else {
            // Show message when no parent connections
            spmHTML += `
                <div class="spm-section">
                    <h3>Network interface(s) information on this host</h3>
                    <div class="spm-empty">
                        <i class="fa fa-info-circle"></i>
                        <p>No network interface(s) information found for this host</p>
                    </div>
                </div>
            `;
        }
        
        // Add child connections (this host as a connected device) - always show this section
        if (data.child_connections && data.child_connections.length > 0) {
            // Show child connections in the marketing container next to host card
            const marketingContainer = document.getElementById('spm-marketing-container');
            if (marketingContainer) {
                const childConnectionsHTML = createSpmTable(
                    data.child_connections,
                    'Switch port interface(s) this host is connected to',
                    'No switch port connections found for this host',
                    true // isChildConnections = true
                );
                marketingContainer.innerHTML = childConnectionsHTML;
                marketingContainer.style.display = 'block';
            }
        } else {
            // Show message when no child connections
            const marketingContainer = document.getElementById('spm-marketing-container');
            if (marketingContainer) {
                marketingContainer.innerHTML = `
                    <div class="spm-section">
                        <h3>Switch port interface(s) this host is connected to</h3>
                        <div class="spm-empty">
                            <i class="fa fa-info-circle"></i>
                            <p>No switch port connections found for this host</p>
                        </div>
                    </div>
                `;
                marketingContainer.style.display = 'block';
            }
        }
        
        // If no connections at all, show empty messages in both containers
        if ((!data.parent_connections || data.parent_connections.length === 0) && 
            (!data.child_connections || data.child_connections.length === 0)) {
            // Show empty message in main SPM container
            spmHTML = `
                <div class="spm-section">
                    <h3>Network interface(s) information on this host</h3>
                    <div class="spm-empty">
                        <i class="fa fa-info-circle"></i>
                        <p>No network interface(s) information found for this host</p>
                    </div>
                </div>
            `;
            spmContainer.innerHTML = spmHTML;
            spmContainer.style.display = 'block';
            
            // Show empty message in marketing container
            const marketingContainer = document.getElementById('spm-marketing-container');
            if (marketingContainer) {
                marketingContainer.innerHTML = `
                    <div class="spm-section">
                        <h3>Switch port interface(s) this host is connected to</h3>
                        <div class="spm-empty">
                            <i class="fa fa-info-circle"></i>
                            <p>No switch port connections found for this host</p>
                        </div>
                    </div>
                `;
                marketingContainer.style.display = 'block';
            }
            return;
        }
        
        // Always show the SPM container if we have any data (even if just empty messages)
        spmContainer.style.display = 'block';
        
        spmContainer.innerHTML = spmHTML;
        
        // Always show the marketing container when we have parent connections
        if (data.parent_connections && data.parent_connections.length > 0) {
            const marketingContainer = document.getElementById('spm-marketing-container');
            if (marketingContainer) {
                marketingContainer.style.display = 'block';
            }
        }
        
    } catch (error) {
        console.error('Error in loadSpmData:', error);
        spmContainer.innerHTML = `
            <div class="spm-error">
                <i class="fa fa-exclamation-triangle"></i>
                <p>Failed to load SPM data: ${error.message}</p>
            </div>
        `;
    }
}

// Function to initialize SPM functionality
async function initSpm() {
    const spmContainer = document.getElementById('spm-container');
    if (!spmContainer) return;
    
    // Show loading state immediately to prevent layout shifts
    spmContainer.style.display = 'block';
    spmContainer.innerHTML = `
        <div class="spm-loading">
            <i class="fa fa-spinner fa-spin"></i>
            <p>Loading network connectivity data...</p>
        </div>
    `;
    
    // Also show loading in marketing container
    const marketingContainer = document.getElementById('spm-marketing-container');
    if (marketingContainer) {
        marketingContainer.style.display = 'block';
        marketingContainer.innerHTML = `
            <div class="spm-loading">
                <i class="fa fa-spinner fa-spin"></i>
                <p>Loading network connectivity data...</p>
            </div>
        `;
    }
    
    // Get the host IP from the URL parameter 'hostip' which contains the actual IP
    const hostIp = new URLSearchParams(window.location.search).get('hostip');
    if (!hostIp) {
        // Fallback to 'ip' parameter if 'hostip' is not available
        const fallbackIp = new URLSearchParams(window.location.search).get('ip');
        if (!fallbackIp) {
            // No IP available, hide container
            spmContainer.style.display = 'none';
            return;
        }
        
        // Check module availability first before showing loading
        try {
            const response = await fetch(`get_spm_data.php?ip=${encodeURIComponent(fallbackIp)}`);
            if (response.status === 500) {
                const errorData = await response.json();
                if (errorData.error && errorData.error.includes('SPM module is not available')) {
                    // Show marketing message in the marketing container next to host card
                    const marketingContainer = document.getElementById('spm-marketing-container');
                    if (marketingContainer) {
                        marketingContainer.innerHTML = `
                            <div class="spm-header">
                                <h2><i class="fa fa-network-wired"></i> Network Connectivity Information</h2>
                            </div>
                            <div class="spm-marketing">
                                <i class="fa fa-info-circle"></i>
                                <p><strong>Switch Port Manager module</strong> is required to view network connectivity information.</p>
                                <p>Upgrade your license to access switch port mappings and device connectivity details.</p>
                            </div>
                        `;
                        marketingContainer.style.display = 'block';
                    }
                    // Hide the main SPM container
                    spmContainer.style.display = 'none';
                    return;
                }
            }
            // Module is available, load data
            loadSpmData(fallbackIp);
        } catch (error) {
            // If we can't check availability, proceed with loading
            loadSpmData(fallbackIp);
        }
        return;
    }
    
    // Check module availability first before showing loading
    try {
        const response = await fetch(`get_spm_data.php?ip=${encodeURIComponent(hostIp)}`);
        if (response.status === 500) {
            const errorData = await response.json();
            if (errorData.error && errorData.error.includes('SPM module is not available')) {
                // Show marketing message in the marketing container next to host card
                const marketingContainer = document.getElementById('spm-marketing-container');
                if (marketingContainer) {
                    marketingContainer.innerHTML = `
                        <div class="spm-header">
                            <h2><i class="fa fa-network-wired"></i> Network Connectivity Information</h2>
                        </div>
                        <div class="spm-marketing">
                            <i class="fa fa-info-circle"></i>
                            <p><strong>Switch Port Manager module</strong> is required to view network connectivity information.</p>
                            <p>Upgrade your license to access switch port mappings and device connectivity details.</p>
                        </div>
                    `;
                    marketingContainer.style.display = 'block';
                }
                // Hide the main SPM container
                spmContainer.style.display = 'none';
                return;
            }
        }
        // Module is available, load data
        loadSpmData(hostIp);
    } catch (error) {
        // If we can't check availability, proceed with loading
        loadSpmData(hostIp);
    }
}

// Function to export SPM table to CSV (only visible rows)
function exportSpmTableToCSV(tableId, tableName) {
    const table = document.getElementById(tableId);
    if (!table) {
        console.error('Table not found:', tableId);
        return;
    }
    
    const csvContent = [];
    
    // Get table headers
    const headerRow = table.querySelector('thead tr');
    if (headerRow) {
        const headers = [];
        const headerCells = headerRow.querySelectorAll('th');
        headerCells.forEach(cell => {
            headers.push('"' + (cell.textContent || cell.innerText).replace(/"/g, '""') + '"');
        });
        csvContent.push(headers.join(','));
    }
    
    // Get visible table rows (not filtered out by search)
    const bodyRows = table.querySelectorAll('tbody tr');
    bodyRows.forEach(row => {
        // Skip rows that are hidden by filtering
        if (row.style.display === 'none') {
            return;
        }
        
        const rowData = [];
        const cells = row.querySelectorAll('td');
        cells.forEach(cell => {
            let cellText = '';
            
            // Handle different cell content types
            if (cell.querySelector('.spm-status-badge')) {
                // Status cell - extract status from icon
                const statusBadge = cell.querySelector('.spm-status-badge');
                if (statusBadge.classList.contains('spm-status-up')) {
                    cellText = 'Up';
                } else {
                    cellText = 'Down';
                }
            } else if (cell.querySelector('.spm-type-badge')) {
                // Type cell - get the title attribute which has full text
                const typeBadge = cell.querySelector('.spm-type-badge');
                cellText = typeBadge.getAttribute('title') || typeBadge.textContent || '';
            } else if (cell.querySelector('a')) {
                // Cell with links - extract just the text content
                const links = cell.querySelectorAll('a');
                const linkTexts = Array.from(links).map(link => link.textContent || link.innerText);
                cellText = linkTexts.join(', ');
                
                // If no links found, get plain text
                if (!cellText) {
                    cellText = cell.textContent || cell.innerText || '';
                }
            } else {
                // Regular cell
                cellText = cell.textContent || cell.innerText || '';
            }
            
            // Clean up the text and escape quotes
            cellText = cellText.trim().replace(/\s+/g, ' ').replace(/"/g, '""');
            rowData.push('"' + cellText + '"');
        });
        
        if (rowData.length > 0) {
            csvContent.push(rowData.join(','));
        }
    });
    
    if (csvContent.length <= 1) {
        alert('No data to export. The table may be empty or all rows are filtered out.');
        return;
    }
    
    // Create and download the CSV file
    const csvString = csvContent.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    
    // Generate filename with current date and hostname
    const urlParams = new URLSearchParams(window.location.search);
    const hostname = urlParams.get('nickname') || urlParams.get('ip') || 'host';
    const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const filename = `${hostname}_${tableName.replace(/[^a-zA-Z0-9]/g, '_')}_${currentDate}.csv`;
    
    // Create download link
    const link = document.createElement('a');
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    } else {
        alert('CSV export is not supported in your browser.');
    }
}

// Export functions for global access
window.loadSpmData = loadSpmData;
window.initSpm = initSpm;
window.exportSpmTableToCSV = exportSpmTableToCSV; 