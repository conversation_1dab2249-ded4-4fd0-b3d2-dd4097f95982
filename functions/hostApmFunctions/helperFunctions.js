const statusMap = {
    1: {
        class: 'pending',
        text: 'PENDING'
    },
    2: {
        class: 'ok',
        text: 'OK'
    },
    4: {
        class: 'warning',
        text: 'Warning'
    },
    8: {
        class: 'unknown',
        text: 'Unknown'
    },
    16: {
        class: 'critical',
        text: 'Critical'
    }
};

// Cache for host action URLs to avoid repeated fetches
const hostActionUrlCache = new Map();

// Function to get the host action URL with caching
async function getHostActionUrl(hostname) {
    // Check cache first
    if (hostActionUrlCache.has(hostname)) {
        return hostActionUrlCache.get(hostname);
    }
    
    // Otherwise fetch it
    const hostObjectInfoUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=host&hostname=${hostname}`;
    try {
        const response = await fetch(hostObjectInfoUrl, { credentials: 'include' });
        if (!response.ok) {
            throw new Error(`Host Object Info Failed: ${response.status}`);
        }
        const hostObjectInfo = await response.json();
        
        if (hostObjectInfo.result.type_text === 'Success' && hostObjectInfo.data.host && hostObjectInfo.data.host.action_url) {
            // Store in cache for future use
            const actionUrl = hostObjectInfo.data.host.action_url;
            hostActionUrlCache.set(hostname, actionUrl);
            return actionUrl;
        }
        // Cache null result too to avoid repeated failed fetches
        hostActionUrlCache.set(hostname, null);
        return null;
    } catch (error) {
        console.error('Error fetching host action URL:', error);
        return null;
    }
}

const statusMapHost = {
    1: {
        class: 'pending',
        text: 'PENDING'
    },
    2: {
        class: 'ok',
        text: 'UP'
    },
    4: {
        class: 'critical',
        text: 'DOWN'
    },
    8: {
        class: 'unknown',
        text: 'UNREACHABLE'
    }
};

function getStatusBackground(statusClass) {
    // Get the computed style from the :root element to access CSS variables
    const style = getComputedStyle(document.documentElement);
    
    // Map status classes to their corresponding CSS variables
    const statusColorMap = {
        'pending': style.getPropertyValue('--pending'),
        'ok': style.getPropertyValue('--success'),
        'warning': style.getPropertyValue('--warning'),
        'critical': style.getPropertyValue('--critical'),
        'unknown': style.getPropertyValue('--unknown')
    };
    
    return statusColorMap[statusClass] || style.getPropertyValue('--background'); // Fallback to theme background
}

function calculateDuration(timestamp) {
    // Check for invalid or missing timestamp data
    if (!timestamp || timestamp === 0 || timestamp === '0' || isNaN(timestamp)) {
        return 'N/A';
    }
    
    const lastChange = new Date(timestamp);
    const now = new Date();
    
    // Check if the date is valid
    if (isNaN(lastChange.getTime())) {
        return 'N/A';
    }
    
    const diff = now - lastChange;

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return `${days}d ${hours}h ${minutes}m ${seconds}s`;
}

function getStyle(statusClass) {
    return `color: var(--surface); font-weight: 600; padding: 2px 8px; border-radius: 4px; background-color: ${getStatusBackground(statusClass)}`;
}

function generateStatusRow(label, status, isHost = false, hostname = '', serviceName = '', currentState = null) {
    // Determine the command type based on label, current state, and whether it's host or service
    let cmdType = null;
    let actionText = '';
    
    const baseUrl = `https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi`;
    
    switch (label) {
        case 'ACTIVE CHECKS':
            if (status.class === 'ok') { // Currently enabled, so show disable option
                cmdType = isHost ? 48 : 6;
                actionText = `Disable active checks of this ${isHost ? 'host' : 'service'}`;
            } else { // Currently disabled, so show enable option
                cmdType = isHost ? 47 : 5;
                actionText = `Enable active checks of this ${isHost ? 'host' : 'service'}`;
            }
            break;
        case 'PASSIVE CHECKS':
            if (status.class === 'ok') { // Currently enabled, so show disable option
                cmdType = isHost ? 93 : 40;
                actionText = `Stop accepting passive checks for this ${isHost ? 'host' : 'service'}`;
            } else { // Currently disabled, so show enable option
                cmdType = isHost ? 92 : 39;
                actionText = `Start accepting passive checks for this ${isHost ? 'host' : 'service'}`;
            }
            break;
        case 'NOTIFICATIONS':
            if (status.class === 'ok') { // Currently enabled, so show disable option
                cmdType = isHost ? 25 : 23;
                actionText = `Disable notifications for this ${isHost ? 'host' : 'service'}`;
            } else { // Currently disabled, so show enable option
                cmdType = isHost ? 24 : 22;
                actionText = `Enable notifications for this ${isHost ? 'host' : 'service'}`;
            }
            break;
        case 'EVENT HANDLER':
            if (status.class === 'ok') { // Currently enabled, so show disable option
                cmdType = isHost ? 44 : 46;
                actionText = `Disable event handler for this ${isHost ? 'host' : 'service'}`;
            } else { // Currently disabled, so show enable option
                cmdType = isHost ? 43 : 45;
                actionText = `Enable event handler for this ${isHost ? 'host' : 'service'}`;
            }
            break;
        case 'FLAP DETECTION':
            if (status.class === 'ok') { // Currently enabled, so show disable option
                cmdType = isHost ? 58 : 60;
                actionText = `Disable flap detection for this ${isHost ? 'host' : 'service'}`;
            } else { // Currently disabled, so show enable option
                cmdType = isHost ? 57 : 59;
                actionText = `Enable flap detection for this ${isHost ? 'host' : 'service'}`;
            }
            break;
        case 'OBSESSING':
            if (status.class === 'ok') { // Currently enabled, so show disable option
                cmdType = isHost ? 102 : 100;
                actionText = `Stop obsessing over this ${isHost ? 'host' : 'service'}`;
            } else { // Currently disabled, so show enable option
                cmdType = isHost ? 101 : 99;
                actionText = `Start obsessing over this ${isHost ? 'host' : 'service'}`;
            }
            break;
    }
    
    // Build the command URL
    let commandUrl = '';
    if (cmdType) {
        if (isHost) {
            commandUrl = `${baseUrl}?cmd_typ=${cmdType}&host=${encodeURIComponent(hostname)}`;
        } else {
            commandUrl = `${baseUrl}?cmd_typ=${cmdType}&host=${encodeURIComponent(hostname)}&service=${encodeURIComponent(serviceName)}`;
        }
    }
    
    // Make the status text clickable if we have a command
    const statusSpan = cmdType ? 
        `<span style="${getStyle(status.class)}; cursor: pointer;" 
          onclick="openCommandModal('${commandUrl}', '${actionText}', this); return false;" 
          title="Click to ${actionText.toLowerCase()}">${status.text}</span>` :
        `<span style="${getStyle(status.class)}">${status.text}</span>`;
    
    return `
            <div style="display: flex; align-items: center; margin-bottom: 2px;">
                <span style="flex: 0 0 140px; font-weight: 600; color: var(--text);"><strong>${label}:</strong></span>
                ${statusSpan}
            </div>
        `;
}

async function updateServiceCards(hostname) {
    document.querySelectorAll('.service-card').forEach(async card => {
        const serviceName = decodeURIComponent(card.getAttribute('data-service'));
        const serviceUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=service&hostname=${hostname}&servicedescription=${serviceName}`;
        try {
            const response = await fetch(serviceUrl);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            const iconContainer = card.querySelector('.service-icon-container');
            const iconMappings = [{
                condition: data.data.service.checks_enabled === false,
                iconClass: 'fa-times-circle',
                tooltip: 'Active checks are off'
            },
            {
                condition: data.data.service.flap_detection_enabled === false,
                iconClass: 'fa-flag',
                tooltip: 'Flap detection is off'
            },
            {
                condition: data.data.service.notifications_enabled === false,
                iconClass: 'fa-bell-slash',
                tooltip: 'Notifications are off'
            },
            {
                condition: data.data.service.accept_passive_checks === false,
                iconClass: 'fa-eye-slash',
                tooltip: 'Passive checks are off'
            },
            {
                condition: data.data.service.event_handler_enabled === false,
                iconClass: 'fa-toggle-off',
                tooltip: 'Event handler is off'
            },
            {
                condition: data.data.service.scheduled_downtime_depth !== 0,
                iconClass: 'fa-moon-o',
                tooltip: 'In schedule downtime'
            },
            {
                condition: data.data.service.obsess === false,
                iconClass: 'fa-meh-o',
                tooltip: 'Obsessing is off'
            },
            {
                condition: data.data.service.problem_has_been_acknowledged === true,
                iconClass: 'fa-gavel',
                tooltip: 'Problem has been acknowledged'
            },
            {
                condition: data.data.service.is_flapping === true,
                iconClass: 'fa-random',
                tooltip: 'Service is flapping'
            }
            ];
            iconMappings.forEach(({
                condition,
                iconClass,
                tooltip
            }) => {
                if (condition) {
                    const icon = document.createElement('i');
                    icon.className = `fa ${iconClass} service-icon`;
                    icon.title = tooltip;
                    iconContainer.appendChild(icon);
                }
            });
            
            // Check if performance graphs are available for this service
            checkServiceGraphAvailability(hostname, serviceName, card);
            
        } catch (error) {
            console.error('Error fetching service data:', error);
        }
    });
}

async function updateHostCard(hostname) {
    const card = document.querySelector('#host-card');
    if (!card) return;

    const hostUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=host&hostname=${hostname}`;

    try {
        const response = await fetch(hostUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();
        const hostData = data.data.host;

        // Ensure icons are not duplicated
        card.querySelectorAll('.host-icon').forEach(icon => icon.remove());

        // Define host-specific icon mappings
        const iconMappings = [{
            condition: hostData.checks_enabled === false,
            iconClass: 'fa-times-circle',
            tooltip: 'Active checks are off'
        },
        {
            condition: hostData.flap_detection_enabled === false,
            iconClass: 'fa-flag',
            tooltip: 'Flap detection is off'
        },
        {
            condition: hostData.notifications_enabled === false,
            iconClass: 'fa-bell-slash',
            tooltip: 'Notifications are off'
        },
        {
            condition: hostData.accept_passive_checks === false,
            iconClass: 'fa-eye-slash',
            tooltip: 'Passive checks are off'
        },
        {
            condition: hostData.event_handler_enabled === false,
            iconClass: 'fa-toggle-off',
            tooltip: 'Event handler is off'
        },
        {
            condition: hostData.scheduled_downtime_depth !== 0,
            iconClass: 'fa-moon-o',
            tooltip: 'In scheduled downtime'
        },
        {
            condition: hostData.obsess === false,
            iconClass: 'fa-meh-o',
            tooltip: 'Obsessing is off'
        },
        {
            condition: hostData.problem_has_been_acknowledged === true,
            iconClass: 'fa-gavel',
            tooltip: 'Problem has been acknowledged'
        },
        {
            condition: hostData.is_flapping === true,
            iconClass: 'fa-random',
            tooltip: 'Host is flapping'
        }
        ];

        // Append icons directly into the host card
        iconMappings.forEach(({
            condition,
            iconClass,
            tooltip
        }) => {
            if (condition) {
                const icon = document.createElement('i');
                icon.className = `fa ${iconClass} host-icon`;
                icon.title = tooltip;
                card.appendChild(icon);
            }
        });
        
        // Check if performance graphs are available
        checkHostGraphAvailability(hostname, card);

    } catch (error) {
        console.error('Error fetching host data:', error);
    }
}

// Updated host graph check to only check for action URL existence
async function checkHostGraphAvailability(hostname, card) {
    try {
        // Use the existing getHostActionUrl function 
        const actionUrl = await getHostActionUrl(hostname);
        
        // If action URL exists, add the graph icon
        if (actionUrl) {
            addGraphIconToHost(card, hostname);
        }
    } catch (error) {
        // Silently handle errors
    }
}

// Helper function to add graph icon to host card
function addGraphIconToHost(card, hostname) {
    const statusElement = card.querySelector('.host-details');
    if (!statusElement) return;
    
    // Check if icon already exists to avoid duplicates
    const existingIcon = statusElement.querySelector('.host-graphs-available');
    if (existingIcon) return;
    
    // Find the element containing "Status:" text
    const statusLine = Array.from(statusElement.childNodes)
        .filter(node => node.nodeType === Node.TEXT_NODE)
        .find(node => node.textContent && node.textContent.includes('Status:'));
    
    if (statusLine) {
        // Insert graph icon after "Status:" text
        const statusText = statusLine.textContent;
        const graphIcon = document.createElement('i');
        graphIcon.className = 'fa fa-area-chart host-graphs-available';
        graphIcon.style.marginLeft = '5px';
        graphIcon.style.color = 'var(--text-secondary)';
        graphIcon.title = 'Performance graphs available';
        
        // Create wrapper to hold text and icon
        const wrapper = document.createElement('span');
        wrapper.appendChild(document.createTextNode(statusText));
        wrapper.appendChild(graphIcon);
        
        // Replace text node with wrapper
        statusLine.parentNode.replaceChild(wrapper, statusLine);
    }
}

// Function to check if service performance graphs are available
// This function is kept for backward compatibility but is no longer called directly
async function checkServiceGraphAvailability(hostname, serviceName, card) {
    try {
        // Fetch service object info to check for service-specific action_url
        const encodedServiceName = encodeURIComponent(serviceName.replace(/\+/g, ' '));
        const serviceObjectUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=service&hostname=${hostname}&servicedescription=${encodedServiceName}`;
        
        const response = await fetch(serviceObjectUrl, { credentials: 'include' });
        if (!response.ok) {
            throw new Error(`Service Object Info Failed: ${response.status}`);
        }
        
        const serviceObjectInfo = await response.json();
        
        // Check if the service has its own action_url
        if (serviceObjectInfo.result.type_text === 'Success' && 
            serviceObjectInfo.data.service && 
            serviceObjectInfo.data.service.action_url) {
            
            // Add graph icon if the service has an action_url
            addGraphIconToService(card, serviceName);
        }
    } catch (error) {
        // Silently handle any errors
        console.log(`Error checking service action URL for ${serviceName}: ${error}`);
    }
}

// Helper function for fetching without console errors
function silentFetch(url) {
    return new Promise(resolve => {
        // Create an AbortController to handle timeouts
        const controller = new AbortController();
        const signal = controller.signal;
        
        // Set a 5 second timeout
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        fetch(url, { 
            credentials: 'include',
            signal: signal
        })
        .then(response => {
            clearTimeout(timeoutId);
            
            if (response.ok) {
                return response.text().then(content => {
                    resolve({ ok: true, content });
                });
            } else {
                // For any error status (including 500), just return not ok
                resolve({ ok: false });
            }
        })
        .catch(() => {
            // Silently catch any errors (network, abort, etc.)
            clearTimeout(timeoutId);
            resolve({ ok: false });
        });
    });
}

// Helper function to add graph icon to service card
function addGraphIconToService(card, serviceName) {
    const statusElement = card.querySelector('.service-details');
    if (!statusElement) {
        return;
    }
    
    // Add graph icon after Status text
    const statusLine = Array.from(statusElement.childNodes)
        .filter(node => node.nodeType === Node.TEXT_NODE)
        .find(node => node.textContent && node.textContent.includes('Status:'));
    
    if (!statusLine) {
        return;
    }
    
    // Check if icon already exists to avoid duplicates
    const existingIcon = statusElement.querySelector('.service-graphs-available');
    if (existingIcon) {
        return;
    }
    
    // Insert graph icon after "Status:" text
    const statusText = statusLine.textContent;
    const graphIcon = document.createElement('i');
    graphIcon.className = 'fa fa-area-chart service-graphs-available';
    graphIcon.style.marginLeft = '5px';
    graphIcon.style.color = 'var(--text-secondary)';
    graphIcon.title = 'Performance graphs available';
    
    // Create wrapper to hold text and icon
    const wrapper = document.createElement('span');
    wrapper.appendChild(document.createTextNode(statusText));
    wrapper.appendChild(graphIcon);
    
    // Replace text node with wrapper
    statusLine.parentNode.replaceChild(wrapper, statusLine);
}

function mapHostStatsForModal(hostStats) {
    // from status.dat format to the format populateHostModal expects
    return {
        ...hostStats,
        plugin_output: hostStats.plugin_output,
        last_state_change: parseInt(hostStats.last_state_change, 10) * 1000,
        last_check: parseInt(hostStats.last_check, 10) * 1000,
        is_flapping: hostStats.is_flapping === '1',
        scheduled_downtime_depth: parseInt(hostStats.scheduled_downtime_depth, 10)
    };
}

function mapServiceForModal(service) {
    // Map the status.dat current_state values to the unified status codes used elsewhere
    const statusMapSvcDatToApi = {
        '0': 2,  // OK
        '1': 4,  // Warning
        '2': 16, // Critical
        '3': 8,  // Unknown
    };

    // Detect "pending" services (never checked yet).  In status.dat these still have current_state = 0
    // but will have has_been_checked == 0 (or last_check == 0).
    const isPending = (service.has_been_checked === '0') || (service.last_check === '0');

    // Prepare timestamps (convert to ms, but keep 0 as 0 so we can detect later)
    const lastStateChangeMs = parseInt(service.last_state_change, 10) * 1000;
    const lastCheckMs = parseInt(service.last_check, 10) * 1000;

    return {
        description: service.service_description,
        status: isPending ? 1 : (statusMapSvcDatToApi[service.current_state] || 1), // 1 = Pending
        plugin_output: service.plugin_output,
        long_plugin_output: service.long_plugin_output || "",
        last_state_change: lastStateChangeMs,
        last_check: lastCheckMs,
        current_attempt: service.current_attempt,
        max_attempts: service.max_attempts,
        percent_state_change: service.percent_state_change,
        is_flapping: service.is_flapping === '1',
        scheduled_downtime_depth: parseInt(service.scheduled_downtime_depth, 10),
        checks_enabled: service.active_checks_enabled === '1',
        accept_passive_checks: service.passive_checks_enabled === '1',
        obsess: service.obsess === '1',
        notifications_enabled: service.notifications_enabled === '1',
        event_handler_enabled: service.event_handler_enabled === '1',
        flap_detection_enabled: service.flap_detection_enabled === '1',
        problem_has_been_acknowledged: service.problem_has_been_acknowledged === '1'
    };
}

// Helper function to parse Azure plugin output and format it for display
function formatAzurePluginOutput(pluginOutput) {
    if (!pluginOutput || !pluginOutput.includes('OK - ')) {
        return null;
    }
    
    // Extract the part after "OK - "
    const azureInfo = pluginOutput.replace('OK - ', '');
    
    // Split by comma and format each key-value pair, excluding VM Name and Resource Name
    const formattedInfo = azureInfo.split(', ').map(item => {
        const [key, value] = item.split('=');
        if (key && value) {
            // Skip VM Name and Resource Name fields
            if (key === 'VM Name' || key === 'Resource Name') {
                return null;
            }
            // Keep the original key format
            return `${key}: ${value}`;
        }
        return item;
    }).filter(Boolean).join('<br>'); // Remove null entries
    
    return formattedInfo;
}