(function(){
    // Initialize PDF export functionality once DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        initializePdfExportModal();
    });

    // Keep track of current host name (populated from hostLoaded event)
    let currentHost = null;
    document.addEventListener('hostLoaded', e => {
        currentHost = e.detail.hostname;
    });

    function initializePdfExportModal() {
        const modal = document.getElementById('pdf-export-modal');
        const closeBtn = document.getElementById('pdf-export-modal-close');
        const cancelBtn = document.getElementById('pdf-export-cancel-btn');
        const generateBtn = document.getElementById('pdf-export-generate-btn');
        const startInput = document.getElementById('pdf-modal-start-date');
        const endInput = document.getElementById('pdf-modal-end-date');

        // Set default date range (last 24 hours)
        if (startInput && endInput) {
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            
            const formatDateForInput = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                return `${year}-${month}-${day}T${hours}:${minutes}`;
            };
            
            startInput.value = formatDateForInput(yesterday);
            endInput.value = formatDateForInput(now);
        }

        // Close modal functions
        function closeModal() {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', closeModal);
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', closeModal);
        }

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });

        // Generate PDF function
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                exportPdf(generateBtn, startInput, endInput);
            });
        }
    }

    // Global function to open PDF export modal
    window.openPdfExportModal = function() {
        const modal = document.getElementById('pdf-export-modal');
        if (modal) {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
    };

    // ---------------- PHP-based PDF generation -----------------
    async function exportPdf(buttonEl, startInput, endInput){
        try {
            if (!currentHost){
                alert('Please load a host first');
                return;
            }

            if (!startInput || !endInput) {
                alert('Date controls not found');
                return;
            }

            const startTs = Math.floor(new Date(startInput.value).getTime() / 1000);
            const endTs = Math.floor(new Date(endInput.value).getTime() / 1000);
            
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs){
                alert('Please select a valid date range');
                return;
            }

            // Disable button + spinner
            buttonEl.disabled = true;
            buttonEl.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Generating PDF...';

            // Get host IP from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const hostIp = urlParams.get('hostip') || urlParams.get('ip') || '';

            // Prepare form data for PHP request
            const formData = new FormData();
            formData.append('hostname', currentHost);
            formData.append('startTs', startTs);
            formData.append('endTs', endTs);
            formData.append('hostIp', hostIp);

            // Make POST request to PHP report generator
            const response = await fetch('functions/hostApmFunctions/generateAvailabilityReport.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // Trigger download using the download URL
                const downloadLink = document.createElement('a');
                downloadLink.href = result.download_url;
                downloadLink.download = result.filename;
                downloadLink.style.display = 'none';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                
                // Show success message and close modal
                console.log('PDF report generated successfully:', result.filename);
                document.getElementById('pdf-export-modal').style.display = 'none';
                document.body.style.overflow = 'auto';
            } else {
                alert('Error generating PDF: ' + result.message);
            }

        } catch (err){
            console.error('PDF export error', err);
            alert('Error generating PDF: ' + err.message);
        } finally {
            if (buttonEl){
                buttonEl.disabled = false;
                buttonEl.innerHTML = '<i class="fa fa-file-pdf-o"></i> Generate PDF';
            }
        }
    }
})();
