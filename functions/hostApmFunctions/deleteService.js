function callDeleteService(ipAddress, serviceName) {
    // Check if serviceName is an array for multiple services
    const isMultiple = Array.isArray(serviceName);
    const serviceCount = isMultiple ? serviceName.length : 1;
    const serviceText = isMultiple ? 
        (serviceCount > 1 ? `${serviceCount} selected services` : serviceName[0]) : 
        serviceName;
        
    if (!confirm(`Delete ${serviceText}?`)) {
        return;
    }
    const phpScriptUrl = 'delete_service.php';

    // Create a floating message element with consistent styling
    const floatingMessage = document.createElement('div');
    floatingMessage.id = 'service-delete-loading';
    floatingMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 8px;
        z-index: 10000;
        text-align: center;
    `;
    floatingMessage.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i>
        </div>
        <div>Deleting ${serviceText}...</div>
    `;

    document.body.appendChild(floatingMessage);

    const formData = new FormData();
    formData.append('ip', ipAddress);
    
    if (isMultiple) {
        // Add each service as a separate form field
        serviceName.forEach((service, index) => {
            formData.append(`servicetodelete[${index}]`, service);
        });
    } else {
        formData.append('servicetodelete', serviceName);
    }

    fetch(phpScriptUrl, {
        method: 'POST',
        body: formData,
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(data => {
            // Remove loading message
            const loadingElement = document.getElementById('service-delete-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            if (typeof data === 'string' && data.includes("Success")) {
                // Show success message briefly then reload
                const successDiv = document.createElement('div');
                successDiv.style.cssText = floatingMessage.style.cssText;
                successDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-check-circle" style="font-size: 24px; color: #4caf50;"></i>
                    </div>
                    <div>${serviceText} deleted successfully!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Refreshing...</div>
                `;
                document.body.appendChild(successDiv);
                
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            } else {
                // Show error message
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = floatingMessage.style.cssText;
                errorDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #ff9800;"></i>
                    </div>
                    <div>Service deletion failed!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Please try again</div>
                `;
                document.body.appendChild(errorDiv);
                
                setTimeout(() => {
                    errorDiv.remove();
                }, 3000);
            }
        })
        .catch(error => {
            // Remove loading message
            const loadingElement = document.getElementById('service-delete-loading');
            if (loadingElement) {
                loadingElement.remove();
            }

            console.error('There was an error calling the PHP script:', error);
            
            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = floatingMessage.style.cssText;
            errorDiv.innerHTML = `
                <div style="margin-bottom: 10px;">
                    <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #f44336;"></i>
                </div>
                <div>An error occurred during service deletion</div>
                <div style="font-size: 12px; margin-top: 5px;">Please try again</div>
            `;
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 3000);
        });
}

// Add multiselection functionality
let selectedServices = [];

// Function to update select all button text
function updateSelectAllButtonText() {
    const selectAllButton = document.getElementById('select-all-button');
    if (!selectAllButton) return;
    
    // Only consider visible (non-filtered) services
    const visibleServiceCards = document.querySelectorAll('.service-card:not(.filtered)');
    const visibleServices = [];
    visibleServiceCards.forEach(card => {
        const serviceName = card.querySelector('.service-title').textContent;
        visibleServices.push(serviceName);
    });
    
    const allVisibleSelected = visibleServices.length > 0 && visibleServices.every(service => selectedServices.includes(service));
    
    if (allVisibleSelected) {
        selectAllButton.innerHTML = '<i class="fa fa-square-o"></i> Deselect All';
    } else {
        selectAllButton.innerHTML = '<i class="fa fa-check-square-o"></i> Select All';
    }
}

// Make function globally accessible
window.updateSelectAllButtonText = updateSelectAllButtonText;

function toggleServiceSelection(serviceName, element) {
    const index = selectedServices.indexOf(serviceName);
    
    if (index === -1) {
        // Add to selection
        selectedServices.push(serviceName);
        element.classList.add('selected');
    } else {
        // Remove from selection
        selectedServices.splice(index, 1);
        element.classList.remove('selected');
    }
    
    // Update corresponding row selection visual in list view (if present)
    try {
        const encodedName = serviceName.replace(/\s+/g, '+');
        const row = document.querySelector(`tr[data-service="${encodedName}"]`);
        if (row) {
            const nowSelected = selectedServices.includes(serviceName);
            row.classList.toggle('selected', nowSelected);
            
            // Update checkbox in table view
            const checkbox = row.querySelector('.service-checkbox');
            if (checkbox) {
                checkbox.checked = nowSelected;
            }
        }
    } catch (e) {
        // Fail silently if any error occurs
    }

    // Update the delete button visibility
    updateDeleteButtonVisibility();
    
    // Update select all checkbox state
    updateSelectAllCheckbox();
    
    // Update select all button text
    updateSelectAllButtonText();
}

function updateDeleteButtonVisibility() {
    const deleteButton = document.getElementById('multi-delete-button');
    if (deleteButton) {
        if (selectedServices.length > 0) {
            deleteButton.style.display = 'flex';
            deleteButton.textContent = `Delete (${selectedServices.length})`;
        } else {
            deleteButton.style.display = 'none';
        }
    }
    
    // Also update select all button text
    updateSelectAllButtonText();
}

function deleteSelectedServices() {
    if (selectedServices.length > 0) {
        const hostip = urlParams.get('hostip');
        callDeleteService(hostip, selectedServices);
    }
}

function toggleSelectionMode() {
    const servicesContainer = document.querySelector('.services-grid');
    const serviceCards = document.querySelectorAll('.service-card');
    const selectionButton = document.getElementById('selection-mode-button');
    const servicesTable = document.querySelector('.services-table');
    
    // Clear any previous selections
    selectedServices = [];
    serviceCards.forEach(card => card.classList.remove('selected'));
    // Clear selection highlight from any table rows
    const selectedRows = document.querySelectorAll('.services-table tbody tr.selected');
    selectedRows.forEach(row => row.classList.remove('selected'));
    
    if (servicesContainer.classList.contains('selection-mode')) {
        // Turn off selection mode
        servicesContainer.classList.remove('selection-mode');
        serviceCards.forEach(card => {
            card.removeEventListener('click', handleSelectionClick);
        });
        selectionButton.innerHTML = '<i class="fa fa-check-square-o"></i>';
        selectionButton.title = 'Enable selection mode';
        
        // Hide delete button and select all button
        const deleteButton = document.getElementById('multi-delete-button');
        if (deleteButton) {
            deleteButton.style.display = 'none';
        }
        const selectAllButton = document.getElementById('select-all-button');
        if (selectAllButton) {
            selectAllButton.remove();
        }
        
        // Remove checkboxes from table view
        if (servicesTable) {
            const checkboxCells = servicesTable.querySelectorAll('.checkbox-cell');
            checkboxCells.forEach(cell => cell.remove());
            const checkboxHeader = servicesTable.querySelector('.checkbox-header');
            if (checkboxHeader) {
                checkboxHeader.remove();
            }
        }
    } else {
        // Turn on selection mode
        servicesContainer.classList.add('selection-mode');
        serviceCards.forEach(card => {
            const serviceName = card.querySelector('.service-title').textContent;
            card.addEventListener('click', handleSelectionClick);
        });
        selectionButton.innerHTML = '<i class="fa fa-times"></i>';
        selectionButton.title = 'Cancel selection mode';
        
        // Show delete button if not already present
        let deleteButton = document.getElementById('multi-delete-button');
        if (!deleteButton) {
            deleteButton = document.createElement('button');
            deleteButton.id = 'multi-delete-button';
            deleteButton.className = 'multi-delete-button';
            deleteButton.innerHTML = 'Delete (0)';
            deleteButton.style.display = 'none';
            deleteButton.addEventListener('click', deleteSelectedServices);
            document.querySelector('.status-header').appendChild(deleteButton);
        }
        
        // Add select all button
        let selectAllButton = document.getElementById('select-all-button');
        if (!selectAllButton) {
            selectAllButton = document.createElement('button');
            selectAllButton.id = 'select-all-button';
            selectAllButton.className = 'select-all-button';
            selectAllButton.innerHTML = '<i class="fa fa-check-square-o"></i> Select All';
            selectAllButton.addEventListener('click', selectAllServices);
            document.querySelector('.status-header').appendChild(selectAllButton);
        }
        
        // Add checkboxes to table view if it exists
        if (servicesTable) {
            addCheckboxesToTable(servicesTable);
        }
    }
}

// Function to add checkboxes to table view
function addCheckboxesToTable(table) {
    const thead = table.querySelector('thead tr');
    const tbody = table.querySelector('tbody');
    
    if (!thead || !tbody) return;
    
    // Check if we're in selection mode
    const servicesContainer = document.querySelector('.services-grid');
    if (!servicesContainer || !servicesContainer.classList.contains('selection-mode')) {
        return; // Don't add checkboxes if not in selection mode
    }
    
    // Check if checkboxes already exist
    const existingCheckboxHeader = thead.querySelector('.checkbox-header');
    if (existingCheckboxHeader) {
        return; // Checkboxes already exist, don't add again
    }
    
    // Add checkbox header
    const checkboxHeader = document.createElement('th');
    checkboxHeader.className = 'checkbox-header';
    checkboxHeader.style.width = '40px';
    checkboxHeader.innerHTML = '<input type="checkbox" id="select-all-checkbox" title="Select All">';
    thead.insertBefore(checkboxHeader, thead.firstChild);
    
    // Add checkbox to each row
    const rows = tbody.querySelectorAll('tr');
    rows.forEach(row => {
        const serviceName = row.dataset.serviceName;
        if (!serviceName) return;
        
        // Check if checkbox already exists for this row
        const existingCheckbox = row.querySelector('.checkbox-cell');
        if (existingCheckbox) return;
        
        const checkboxCell = document.createElement('td');
        checkboxCell.className = 'checkbox-cell';
        checkboxCell.style.textAlign = 'center';
        checkboxCell.innerHTML = `<input type="checkbox" class="service-checkbox" data-service="${serviceName}" title="Select ${serviceName}">`;
        row.insertBefore(checkboxCell, row.firstChild);
        
        // Add click handler for the checkbox
        const checkbox = checkboxCell.querySelector('.service-checkbox');
        checkbox.addEventListener('change', (e) => {
            e.stopPropagation();
            const isChecked = checkbox.checked;
            if (isChecked) {
                if (!selectedServices.includes(serviceName)) {
                    selectedServices.push(serviceName);
                    row.classList.add('selected');
                }
            } else {
                const index = selectedServices.indexOf(serviceName);
                if (index > -1) {
                    selectedServices.splice(index, 1);
                    row.classList.remove('selected');
                }
            }
            updateDeleteButtonVisibility();
            updateSelectAllCheckbox();
            updateSelectAllButtonText();
        });
    });
    
    // Add select all checkbox functionality
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', (e) => {
            e.stopPropagation();
            const isChecked = selectAllCheckbox.checked;
            
            if (isChecked) {
                // Select only visible services
                const visibleCheckboxes = Array.from(tbody.querySelectorAll('.service-checkbox')).filter(checkbox => {
                    const row = checkbox.closest('tr');
                    return row && row.style.display !== 'none';
                });
                
                visibleCheckboxes.forEach(checkbox => {
                    checkbox.checked = true;
                    const serviceName = checkbox.dataset.service;
                    const row = checkbox.closest('tr');
                    
                    if (!selectedServices.includes(serviceName)) {
                        selectedServices.push(serviceName);
                    }
                    row.classList.add('selected');
                });
            } else {
                // Deselect all services (both visible and filtered)
                const allCheckboxes = tbody.querySelectorAll('.service-checkbox');
                allCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                    const serviceName = checkbox.dataset.service;
                    const row = checkbox.closest('tr');
                    
                    const index = selectedServices.indexOf(serviceName);
                    if (index > -1) {
                        selectedServices.splice(index, 1);
                    }
                    row.classList.remove('selected');
                });
            }
            
            updateDeleteButtonVisibility();
            updateSelectAllButtonText();
        });
    }
}

// Make function globally accessible
window.addCheckboxesToTable = addCheckboxesToTable;

// Function to select all services
function selectAllServices() {
    // Only select visible (non-filtered) services
    const visibleServiceCards = document.querySelectorAll('.service-card:not(.filtered)');
    const visibleTableRows = document.querySelectorAll('.services-table tbody tr:not([style*="display: none"])');
    const selectAllButton = document.getElementById('select-all-button');
    
    // Check if all visible services are already selected
    const visibleServices = [];
    visibleServiceCards.forEach(card => {
        const serviceName = card.querySelector('.service-title').textContent;
        visibleServices.push(serviceName);
    });
    
    const allVisibleSelected = visibleServices.every(service => selectedServices.includes(service));
    
    if (allVisibleSelected) {
        // Deselect all
        selectedServices = [];
        
        // Deselect all service cards (both visible and filtered)
        const allServiceCards = document.querySelectorAll('.service-card');
        allServiceCards.forEach(card => {
            card.classList.remove('selected');
        });
        
        // Deselect all table rows and checkboxes
        const allTableRows = document.querySelectorAll('.services-table tbody tr');
        allTableRows.forEach(row => {
            row.classList.remove('selected');
            const checkbox = row.querySelector('.service-checkbox');
            if (checkbox) {
                checkbox.checked = false;
            }
        });
        
        // Update select all checkbox
        updateSelectAllCheckbox();
        
        // Update button text
        if (selectAllButton) {
            selectAllButton.innerHTML = '<i class="fa fa-check-square-o"></i> Select All';
        }
    } else {
        // Select all visible services
        selectedServices = [];
        
        // Select only visible service cards
        visibleServiceCards.forEach(card => {
            const serviceName = card.querySelector('.service-title').textContent;
            if (!selectedServices.includes(serviceName)) {
                selectedServices.push(serviceName);
            }
            card.classList.add('selected');
        });
        
        // Select only visible table rows and checkboxes
        visibleTableRows.forEach(row => {
            const serviceName = row.dataset.serviceName;
            if (serviceName && !selectedServices.includes(serviceName)) {
                selectedServices.push(serviceName);
            }
            row.classList.add('selected');
            
            const checkbox = row.querySelector('.service-checkbox');
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        
        // Update select all checkbox
        updateSelectAllCheckbox();
        
        // Update button text
        if (selectAllButton) {
            selectAllButton.innerHTML = '<i class="fa fa-square-o"></i> Deselect All';
        }
    }
    
    updateDeleteButtonVisibility();
}

// Function to update select all checkbox state
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (!selectAllCheckbox) return;
    
    // Only consider visible (non-filtered) checkboxes
    const visibleCheckboxes = document.querySelectorAll('.service-checkbox');
    const visibleCheckedCheckboxes = Array.from(visibleCheckboxes).filter(checkbox => {
        const row = checkbox.closest('tr');
        return row && row.style.display !== 'none';
    });
    const checkedVisibleCheckboxes = visibleCheckedCheckboxes.filter(checkbox => checkbox.checked);
    
    if (visibleCheckboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedVisibleCheckboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedVisibleCheckboxes.length === visibleCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// Make function globally accessible
window.updateSelectAllCheckbox = updateSelectAllCheckbox;

function handleSelectionClick(e) {
    // Prevent clicking on service options icon
    if (e.target.classList.contains('service-options') || 
        e.target.parentElement.classList.contains('service-options')) {
        return;
    }
    
    const card = this;
    const serviceName = card.querySelector('.service-title').textContent;
    toggleServiceSelection(serviceName, card);
}

// Add styles for selection mode
document.head.insertAdjacentHTML('beforeend', `
<style>
.selection-mode .service-card {
    cursor: pointer;
    position: relative;
}

.selection-mode .service-card::before {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border);
    border-radius: 4px;
    background-color: var(--surface);
}

.selection-mode .service-card.selected::before {
    content: '✓';
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
}

.multi-delete-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--critical);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 30px;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    z-index: 100;
    display: none;
    align-items: center;
    justify-content: center;
}

.multi-delete-button:hover {
    background-color: #d32f2f;
}

.select-all-button {
    position: fixed;
    bottom: 20px;
    right: 140px;
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 30px;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.select-all-button:hover {
    background-color: #0056b3;
}

.selection-mode-button {
    background-color: var(--surface);
    border: 1px solid var(--border);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 10px;
}

.selection-mode-button:hover {
    background-color: var(--hover);
}

/* Table checkbox styles */
.checkbox-cell {
    text-align: center !important;
    vertical-align: middle;
}

.checkbox-header {
    text-align: center !important;
    vertical-align: middle;
}

.service-checkbox, #select-all-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: var(--primary);
}

.service-checkbox:checked, #select-all-checkbox:checked {
    background-color: var(--primary);
    border-color: var(--primary);
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    .select-all-button {
        right: 20px;
        bottom: 80px;
        font-size: 12px;
        padding: 8px 16px;
    }
    
    .multi-delete-button {
        font-size: 12px;
        padding: 8px 16px;
    }
}
</style>
`);