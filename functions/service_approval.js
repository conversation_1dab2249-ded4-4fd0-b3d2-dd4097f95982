/**
 * Service Approval JavaScript Functions
 * Provides functionality for checking and managing services pending approval
 */

// Global variables
let pendingServices = [];
let serviceNotificationInterval = null;
let currentServiceInfra = '';
let servicesAdded = false;
let notificationObserver = null;

// Helper function to process service names and remove If-xx- prefix
function processServiceName(serviceName) {
    // Check if service name starts with "If-" followed by numbers and optional space-dash
    const portPattern = /^If-\d+\s*-\s*(.+)$/;
    const match = serviceName.match(portPattern);
    
    if (match) {
        // Return only the part after "If-xx-"
        return match[1];
    }
    
    // Return original service name if it doesn't match the pattern
    return serviceName;
}

// Helper function to process an array of service names
function processServiceNames(servicesList) {
    return servicesList.map(service => processServiceName(service));
}

// Initialize the service approval system
function initServiceApproval(infraName) {
    currentServiceInfra = infraName || '';
    servicesAdded = false;
    
    // Create notification container if it doesn't exist
    if (!document.getElementById('service-approval-notification')) {
        const notificationDiv = document.createElement('div');
        notificationDiv.id = 'service-approval-notification';
        notificationDiv.className = 'approval-notification hidden';
        document.body.appendChild(notificationDiv);
    }
    
    // Setup notification observer
    setupNotificationObserver();
    
    // Start checking for pending services
    checkPendingServices();
    
    // Set interval to check periodically
    if (serviceNotificationInterval) {
        clearInterval(serviceNotificationInterval);
    }
    serviceNotificationInterval = setInterval(checkPendingServices, 60000); // Check every minute
}

// Setup observer to watch for changes in notifications
function setupNotificationObserver() {
    // Disconnect existing observer if any
    if (notificationObserver) {
        notificationObserver.disconnect();
    }
    
    // Create a new observer
    notificationObserver = new MutationObserver(function(mutations) {
        // When mutations occur, reposition notifications with a delay
        setTimeout(positionNotifications, 50);
    });
    
    // Observe both notifications for changes
    const serviceNotification = document.getElementById('service-approval-notification');
    const hostNotification = document.getElementById('host-approval-notification');
    
    if (serviceNotification) {
        notificationObserver.observe(serviceNotification, { 
            attributes: true,
            attributeFilter: ['class']
        });
    }
    
    if (hostNotification) {
        notificationObserver.observe(hostNotification, {
            attributes: true,
            attributeFilter: ['class']
        });
    }
    
    // Also observe body for changes in child elements (new notifications being added)
    notificationObserver.observe(document.body, {
        childList: true
    });
}

// Check for services pending approval
function checkPendingServices() {
    const url = `service_approval.php?subnet=${urlParams.get('subnet')}&list=pending${currentServiceInfra ? '&infra=' + encodeURIComponent(currentServiceInfra) : ''}`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            const previousHadServices = pendingServices && pendingServices.length > 0;
            pendingServices = data.services || [];
            const totalServices = data.total || pendingServices.length;
            const limited = data.limited || false;

            if (pendingServices.length > 0) {
                showServiceNotification(totalServices, limited);
                
                // Position the service notification side by side with host notification with a delay
                setTimeout(positionNotifications, 300);
            } else {
                hideServiceNotification();
                if (servicesAdded) {
                    location.reload();
                }
            }
        })
        .catch(error => console.error('Error checking pending services:', error));
}

// Position notifications so service notification appears side by side with host notification
function positionNotifications() {
    const serviceNotification = document.getElementById('service-approval-notification');
    const hostNotification = document.getElementById('host-approval-notification');
    
    if (!serviceNotification) return;
    
    // Check if we're on a mobile device
    const isMobile = window.innerWidth <= 768;
    
    // If host notification exists, position service notification to the left of it
    if (hostNotification && !hostNotification.classList.contains('hidden')) {
        // Force both notifications to use fixed positioning
        serviceNotification.style.position = 'fixed';
        hostNotification.style.position = 'fixed';
        
        // Get host notification's dimensions and position
        const hostRect = hostNotification.getBoundingClientRect();
        const serviceWidth = serviceNotification.offsetWidth;
        
        // Adjust spacing based on screen size
        const spacing = isMobile ? 5 : 20;
        const margin = isMobile ? 5 : 20;
        
        // On mobile, ensure both notifications are properly aligned
        if (isMobile) {
            // Position host notification on the right
            hostNotification.style.right = `${margin}px`;
            hostNotification.style.left = 'auto';
            
            // Position service notification to the left of host notification
            const leftPosition = window.innerWidth - hostNotification.offsetWidth - serviceWidth - (margin * 2) - spacing;
            serviceNotification.style.left = `${leftPosition}px`;
            serviceNotification.style.right = 'auto';
        } else {
            // Desktop positioning - position service notification to the left of host notification
            const leftPosition = hostRect.left - serviceWidth - spacing;
            
            // Ensure service notification is fully visible
            if (leftPosition < margin) {
                serviceNotification.style.left = `${margin}px`;
                serviceNotification.style.right = 'auto';
            } else {
                serviceNotification.style.left = `${leftPosition}px`;
                serviceNotification.style.right = 'auto';
            }
        }
        
        // Keep both notifications at the same bottom position
        const bottomMargin = isMobile ? 5 : 20;
        serviceNotification.style.bottom = `${bottomMargin}px`;
        hostNotification.style.bottom = `${bottomMargin}px`;
        serviceNotification.style.top = 'auto';
        hostNotification.style.top = 'auto';
        
        console.log(`Service notification positioned: left=${serviceNotification.style.left}, bottom=${serviceNotification.style.bottom}`);
    } else {
        // If host notification isn't visible, position at the bottom right
        serviceNotification.style.position = 'fixed';
        const bottomMargin = isMobile ? 5 : 20;
        const rightMargin = isMobile ? 5 : 20;
        serviceNotification.style.bottom = `${bottomMargin}px`;
        serviceNotification.style.right = `${rightMargin}px`;
        serviceNotification.style.left = 'auto';
        serviceNotification.style.top = 'auto';
    }
}

// Show notification about pending services
function showServiceNotification(totalServices, limited) {
    const notification = document.getElementById('service-approval-notification');
    if (!notification) return;
    
    // Create notification content - always show the button to view all services
    notification.innerHTML = `
        <h3><i class="fa fa-cogs"></i> Services Pending Approval</h3>
        <p><span>${totalServices}</span> host${totalServices !== 1 ? 's' : ''} with pending services${limited ? ' (showing 50 of ' + totalServices + ')' : ''}</p>
        <button class="show-all" onclick="showAllPendingServices()"><i class="fa fa-list"></i> View All Services</button>
    `;
    
    // Show the notification
    notification.classList.remove('hidden');
}

// Hide notification
function hideServiceNotification() {
    const notification = document.getElementById('service-approval-notification');
    if (notification) {
        notification.classList.add('hidden');
    }
}

// Approve a service
function approveService(id, ip, infra) {
    const button = event.target.closest('button');
    if (button) {
        button.classList.add('loading');
        button.disabled = true;
    }
    sendServiceApprovalAction(id, ip, infra, 'approve');
}

// Reject a service
function rejectService(id) {
    const button = event.target.closest('button');
    if (button) {
        button.classList.add('loading');
        button.disabled = true;
    }
    sendServiceApprovalAction(id, null, null, 'reject');
}

// Send approval action to the server
function sendServiceApprovalAction(id, ip, infra, action) {
    let url = `service_approval.php?id=${encodeURIComponent(id)}&action=${action}`;
    
    if (action === 'approve' && ip && infra) {
        url += `&ip=${encodeURIComponent(ip)}&infra=${encodeURIComponent(infra)}`;
    }
    
    fetch(url)
        .then(response => response.text())
        .then(result => {
            console.log(result);
            // Refresh the list after action
            checkPendingServices();
            servicesAdded = true;
        })
        .catch(error => {
            console.error(`Error ${action}ing service:`, error);
            // Remove loading state from all buttons
            document.querySelectorAll('button.loading').forEach(btn => {
                btn.classList.remove('loading');
                btn.disabled = false;
            });
        });
}

// Show modal with all pending services
function showAllPendingServices() {
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';

    // Variables for pagination
    let currentPage = 1;
    let searchTerm = '';
    
    // Store currentPage in window for access by other functions
    window.currentPage = currentPage;

    // Function to generate table rows based on services
    const generateTableRows = (servicesToDisplay) => {
        if (servicesToDisplay.length === 0) {
            return '<tr><td colspan="7" class="text-center">No services found matching your search criteria</td></tr>';
        }
        
        return servicesToDisplay.map(service => {
            const servicesList = JSON.parse(service.services);
            // Process service names to remove If-xx- prefix
            const processedServicesList = processServiceNames(servicesList);
            // Create a unique ID for each services cell to use with the toggle
            const servicesCellId = `services-cell-${service.id}`;
            
            // Create a formatted services display
            let servicesDisplay = '';
            if (processedServicesList.length <= 3) {
                // If 3 or fewer services, just display them all
                servicesDisplay = processedServicesList.join(', ');
            } else {
                // For more than 3 services, create an expandable view
                servicesDisplay = `
                    <div class="services-preview">${processedServicesList.slice(0, 3).join(', ')} 
                        <a href="#" class="toggle-services" data-target="${servicesCellId}" data-expanded="false">
                            <i class="fa fa-angle-down"></i> Show all ${processedServicesList.length}
                        </a>
                    </div>
                    <div class="services-full" id="${servicesCellId}" style="display:none;">
                        ${processedServicesList.join('<br>')}
                        <div class="mt-2">
                            <a href="#" class="toggle-services" data-target="${servicesCellId}" data-expanded="true">
                                <i class="fa fa-angle-up"></i> Show less
                            </a>
                        </div>
                    </div>
                `;
            }
            
            return `
                <tr data-id="${service.id}">
                    <td><input type="checkbox" class="service-checkbox" data-id="${service.id}" data-ip="${service.host_ip}" data-infra="${service.infra}"></td>
                    <td>${service.host_name || service.host_ip}</td>
                    <td>${service.host_ip}</td>
                    <td>${service.infra || ''}</td>
                    <td>${servicesList.length} service${servicesList.length !== 1 ? 's' : ''}</td>
                    <td class="service-list">${servicesDisplay}</td>
                    <td class="host-service-action-cell">
                        <button class="approve" onclick="approveService('${service.id}', '${service.host_ip}', '${service.infra}'); this.closest('tr').remove();"><i class="fa fa-check"></i></button>
                        <button class="reject" onclick="rejectService('${service.id}'); this.closest('tr').remove();"><i class="fa fa-times"></i></button>
                    </td>
                </tr>
            `;
        }).join('');
    };
    
    // Function to fetch services from the server with search and pagination
    const fetchServices = (page, search = '') => {
        const tableBody = document.getElementById('pendingServicesTableBody');
        const paginationContainer = document.getElementById('servicesPagination');
        const searchResultsInfo = document.getElementById('searchResultsInfo');
        const searchInput = document.getElementById('pendingServiceSearch');
        const clearSearchButton = document.getElementById('clearPendingServiceSearch');

        if (tableBody) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</td></tr>';
        }

        // Show/hide clear button based on search input value
        if (searchInput && clearSearchButton) {
            clearSearchButton.style.display = searchInput.value ? 'inline-block' : 'none';
        }

        // Build the URL with all necessary parameters
        let url = `service_approval.php?subnet=${urlParams.get('subnet')}&list=pending&page=${page}`;

        if (currentServiceInfra) {
            url += `&infra=${encodeURIComponent(currentServiceInfra)}`;
        }

        if (search) {
            url += `&search=${encodeURIComponent(search)}`;
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                const services = data.services || [];
                const totalServices = data.total || 0;
                const totalPages = data.total_pages || 1;

                if (tableBody) {
                    tableBody.innerHTML = generateTableRows(services);
                    attachToggleListeners();
                }
                
                // Clear selection count when fetching new data
                updateServiceSelectedCount();

                // Update pagination
                if (paginationContainer) {
                    updatePagination(paginationContainer, page, totalPages, totalServices);
                }

                // Update search results info
                if (searchResultsInfo) {
                    if (search) {
                        searchResultsInfo.innerHTML = `Found ${totalServices} service${totalServices !== 1 ? 's' : ''} matching "${search}"`;
                        searchResultsInfo.style.display = 'block';
                    } else {
                        searchResultsInfo.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching services:', error);
                if (tableBody) {
                    tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Error loading services. Please try again.</td></tr>';
                }
            });
    };
    
    // Function to update pagination controls
    const updatePagination = (container, currentPage, totalPages, totalItems) => {
        // Don't show pagination if there's only one page
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        // Calculate which page numbers to show
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);
        
        // Adjust startPage if endPage is at max
        if (endPage === totalPages) {
            startPage = Math.max(1, endPage - 4);
        }
        
        let paginationHTML = `
            <div class="pagination-info">Showing ${(currentPage - 1) * 50 + 1}-${Math.min(currentPage * 50, totalItems)} of ${totalItems}</div>
            <div class="pagination-controls">
        `;
        
        // Previous button
        paginationHTML += `
            <button class="pagination-btn ${currentPage === 1 ? 'disabled' : ''}" 
                ${currentPage === 1 ? 'disabled' : `onclick="changePage(${currentPage - 1})"`}>
                <i class="fa fa-angle-left"></i>
            </button>
        `;
        
        // First page
        if (startPage > 1) {
            paginationHTML += `
                <button class="pagination-btn" onclick="changePage(1)">1</button>
                ${startPage > 2 ? '<span class="pagination-ellipsis">...</span>' : ''}
            `;
        }
        
        // Page numbers
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" 
                    onclick="changePage(${i})">${i}</button>
            `;
        }
        
        // Last page
        if (endPage < totalPages) {
            paginationHTML += `
                ${endPage < totalPages - 1 ? '<span class="pagination-ellipsis">...</span>' : ''}
                <button class="pagination-btn" onclick="changePage(${totalPages})">${totalPages}</button>
            `;
        }
        
        // Next button
        paginationHTML += `
            <button class="pagination-btn ${currentPage === totalPages ? 'disabled' : ''}" 
                ${currentPage === totalPages ? 'disabled' : `onclick="changePage(${currentPage + 1})"`}>
                <i class="fa fa-angle-right"></i>
            </button>
        </div>`;
        
        container.innerHTML = paginationHTML;
    };
    
    // Make updatePagination globally accessible
    window.updatePagination = updatePagination;
    
    // Initialize the modal
    modalOverlay.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-header">
                <h2><i class="fa fa-cogs"></i> Services Pending Approval</h2>
                <div style="display: flex; align-items: center;">
                    <div class="dropdown" style="margin-right: 10px; position: relative;">
                        <button class="dropdown-toggle" onclick="toggleServiceDropdown()" title="More options" style="background: none; border: none; cursor: pointer; margin-right: 10px; padding: 5px;">
                            <i class="fa fa-ellipsis-v" style="font-size: 18px;"></i>
                        </button>
                        <div id="serviceDropdownMenu" class="dropdown-menu" style="display: none;">
                            <button class="dropdown-item" onclick="rejectAllServicesGlobal()"><i class="fa fa-times"></i> Reject All</button>
                        </div>
                    </div>
                    <button onclick="refreshServicesTable()" title="Refresh" style="background: none; border: none; cursor: pointer; margin-right: 10px; padding: 5px;">
                        <i class="fa fa-refresh" style="font-size: 18px;"></i>
                    </button>
                    <div class="modal-separator"></div>
                    <button class="modal-close" onclick="closeServiceApprovalModal()" title="Close" style="background: none; border: none; cursor: pointer; padding: 5px;">
                        <i class="fa fa-times" style="font-size: 18px;"></i>
                    </button>
                </div>
            </div>
            <div class="modal-search-controls">
                <div class="search-input-wrapper">
                    <input type="text" id="pendingServiceSearch" placeholder="Search by hostname or IP..." class="modal-search-input">
                    <button type="button" id="clearPendingServiceSearch" class="clear-search-btn" style="display:none;">&times;</button>
                </div>
                <div id="searchResultsInfo" class="search-results-info" style="display: none;"></div>
            </div>
            <div class="modal-content-approval">
                <table class="host-table">
                    <thead>
                        <tr>
                            <th id="checkboxHeader"><input type="checkbox" id="serviceSelectAllCheckbox" onclick="toggleServiceSelectAll()"></th>
                            <th>Hostname</th>
                            <th>IP Address</th>
                            <th>Infrastructure</th>
                            <th>Count</th>
                            <th>Services</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="pendingServicesTableBody">
                        <tr><td colspan="7" class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</td></tr>
                    </tbody>
                </table>
            </div>
            <div id="servicesPagination" class="pagination-container"></div>
            <div class="modal-multi-select-controls">
                <div class="multi-select-info">
                    <span id="serviceSelectedCount">0</span> hosts selected
                </div>
                <div class="multi-select-actions">
                    <button class="approve" onclick="approveSelectedServices()"><i class="fa fa-check"></i> Approve Selected</button>
                    <button class="reject" onclick="rejectSelectedServices()"><i class="fa fa-times"></i> Reject Selected</button>
                </div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    `;
    
    document.body.appendChild(modalOverlay);
    
    modalOverlay.closeModal = function() {
        this.remove();
    };
    
    // Define changePage function in the global scope
    window.changePage = function(page) {
        currentPage = page;
        window.currentPage = page; // Update global currentPage
        
        // Clear selection when changing pages
        const selectAllCheckbox = document.getElementById('serviceSelectAllCheckbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
        }
        
        fetchServices(page, searchTerm);
    };
    
    // Add event listener for the search input with debounce
    const searchInput = document.getElementById('pendingServiceSearch');
    const clearSearchButton = document.getElementById('clearPendingServiceSearch');
    let searchTimeout = null;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            if (clearSearchButton) {
                clearSearchButton.style.display = this.value ? 'inline-block' : 'none';
            }
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchTerm = this.value.toLowerCase();
                currentPage = 1; // Reset to first page when searching
                window.currentPage = 1; // Update global currentPage
                
                // Clear selection when searching
                const selectAllCheckbox = document.getElementById('serviceSelectAllCheckbox');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
                
                fetchServices(currentPage, searchTerm);
            }, 500); // Debounce for 500ms
        });
    }

    if (clearSearchButton) {
        clearSearchButton.addEventListener('click', function() {
            if (searchInput) {
                searchInput.value = '';
            }
            searchTerm = '';
            currentPage = 1;
            window.currentPage = 1;
            
            // Clear selection when clearing search
            const selectAllCheckbox = document.getElementById('serviceSelectAllCheckbox');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
            }
            
            // Directly hide the clear button
            if (clearSearchButton) {
                clearSearchButton.style.display = 'none';
            }
            // Directly hide the search results info
            const searchResultsInfo = document.getElementById('searchResultsInfo');
            if (searchResultsInfo) {
                 searchResultsInfo.style.display = 'none';
            }
            // Fetch services once after clearing
            fetchServices(currentPage, searchTerm);
        });
    }
    
    // Initial fetch
    fetchServices(currentPage, searchTerm);
}

// Attach event listeners to toggle service list expand/collapse
function attachToggleListeners() {
    document.querySelectorAll('.toggle-services').forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('data-target');
            const isExpanded = this.getAttribute('data-expanded') === 'true';
            const targetElement = document.getElementById(targetId);
            
            if (isExpanded) {
                // If expanded, hide the full list and show the preview
                targetElement.style.display = 'none';
                targetElement.previousElementSibling.style.display = 'block';
            } else {
                // If collapsed, show the full list and hide the preview
                targetElement.style.display = 'block';
                targetElement.previousElementSibling.style.display = 'none';
            }
        });
    });
}

function closeServiceApprovalModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.closeModal();
        setTimeout(() => {
            if (servicesAdded) {
                location.reload();
            }
        }, 100);
    }
}



// Function to refresh the services table with latest data
function refreshServicesTable() {
    // Get current page and search term if available
    const currentPage = window.currentPage || 1;
    const searchTerm = document.getElementById('pendingServiceSearch')?.value || '';
    
    // Use the changePage function with current values
    if (typeof window.changePage === 'function') {
        window.changePage(currentPage);
    } else {
        // Fallback if changePage not defined yet
        const url = `service_approval.php?subnet=${urlParams.get('subnet')}&list=pending&page=${currentPage}${searchTerm ? '&search=' + encodeURIComponent(searchTerm) : ''}${currentServiceInfra ? '&infra=' + encodeURIComponent(currentServiceInfra) : ''}`;
        const tableBody = document.getElementById('pendingServicesTableBody');
        
        if (!tableBody) return;
        
        // Show loading indicator in the table
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="fa fa-spinner fa-spin"></i> Refreshing...</td></tr>';
        
        // Fetch the latest service data
        fetch(url)
            .then(response => response.json())
            .then(data => {
                const services = data.services || [];
                
                if (services.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="7" class="text-center">No services pending approval</td></tr>';
                    
                    // If no services left, close the modal
                    const modal = document.querySelector('.modal-overlay');
                    if (modal) {
                        setTimeout(() => {
                            modal.remove();
                            checkPendingServices();
                        }, 1000);
                    }
                    return;
                }
                
                // Generate table rows for the services
                tableBody.innerHTML = services.map(service => {
                    const servicesList = JSON.parse(service.services);
                    // Process service names to remove If-xx- prefix
                    const processedServicesList = processServiceNames(servicesList);
                    const servicesCellId = `services-cell-${service.id}`;
                    
                    let servicesDisplay = '';
                    if (processedServicesList.length <= 3) {
                        servicesDisplay = processedServicesList.join(', ');
                    } else {
                        servicesDisplay = `
                            <div class="services-preview">${processedServicesList.slice(0, 3).join(', ')} 
                                <a href="#" class="toggle-services" data-target="${servicesCellId}" data-expanded="false">
                                    <i class="fa fa-angle-down"></i> Show all ${processedServicesList.length}
                                </a>
                            </div>
                            <div class="services-full" id="${servicesCellId}" style="display:none;">
                                ${processedServicesList.join('<br>')}
                                <div class="mt-2">
                                    <a href="#" class="toggle-services" data-target="${servicesCellId}" data-expanded="true">
                                        <i class="fa fa-angle-up"></i> Show less
                                    </a>
                                </div>
                            </div>
                        `;
                    }
                    
                    return `
                        <tr data-id="${service.id}">
                            <td><input type="checkbox" class="service-checkbox" data-id="${service.id}" data-ip="${service.host_ip}" data-infra="${service.infra}"></td>
                            <td>${service.host_name || service.host_ip}</td>
                            <td>${service.host_ip}</td>
                            <td>${service.infra || ''}</td>
                            <td>${servicesList.length} service${servicesList.length !== 1 ? 's' : ''}</td>
                            <td class="service-list">${servicesDisplay}</td>
                            <td class="host-service-action-cell">
                                <button class="approve" onclick="approveService('${service.id}', '${service.host_ip}', '${service.infra}'); this.closest('tr').remove();"><i class="fa fa-check"></i></button>
                                <button class="reject" onclick="rejectService('${service.id}'); this.closest('tr').remove();"><i class="fa fa-times"></i></button>
                            </td>
                        </tr>
                    `;
                }).join('');
                
                // Reattach event listeners
                attachToggleListeners();
                
                // Update pagination if needed
                if (data.total_pages && data.total) {
                    const paginationContainer = document.getElementById('servicesPagination');
                    if (paginationContainer) {
                        window.updatePagination(paginationContainer, currentPage, data.total_pages, data.total);
                    }
                }
            })
            .catch(error => {
                console.error('Error refreshing services table:', error);
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Error refreshing services. Please try again.</td></tr>';
            });
    }
}

// Clean up on page unload
function cleanupServiceApproval() {
    if (serviceNotificationInterval) {
        clearInterval(serviceNotificationInterval);
        serviceNotificationInterval = null;
    }
    
    if (notificationObserver) {
        notificationObserver.disconnect();
        notificationObserver = null;
    }
}

// Update notifications when window is resized
window.addEventListener('resize', positionNotifications);

// Add CSS for service notification positioning
document.addEventListener('DOMContentLoaded', function() {
    // Add responsive CSS for mobile devices
    const style = document.createElement('style');
    style.textContent = `
        @media (max-width: 768px) {
            .approval-notification {
                max-width: 200px !important;
                min-width: 200px !important;
                font-size: 11px !important;
                padding: 8px !important;
            }
            
            .approval-notification h3 {
                font-size: 12px !important;
                margin: 0 0 6px 0 !important;
            }
            
            .approval-notification p {
                font-size: 10px !important;
                margin: 0 0 6px 0 !important;
            }
            
            .approval-notification button {
                font-size: 9px !important;
                padding: 4px 6px !important;
            }
            
            .approval-notification i {
                font-size: 10px !important;
            }
        }
        
        @media (max-width: 480px) {
            .approval-notification {
                max-width: 160px !important;
                min-width: 160px !important;
                font-size: 9px !important;
                padding: 6px !important;
            }
            
            .approval-notification h3 {
                font-size: 10px !important;
                margin: 0 0 4px 0 !important;
            }
            
            .approval-notification p {
                font-size: 8px !important;
                margin: 0 0 4px 0 !important;
            }
            
            .approval-notification button {
                font-size: 8px !important;
                padding: 3px 5px !important;
            }
            
            .approval-notification i {
                font-size: 9px !important;
            }
        }
        
        @media (max-width: 360px) {
            .approval-notification {
                max-width: 140px !important;
                min-width: 140px !important;
                font-size: 8px !important;
                padding: 5px !important;
            }
            
            .approval-notification h3 {
                font-size: 9px !important;
                margin: 0 0 3px 0 !important;
            }
            
            .approval-notification p {
                font-size: 7px !important;
                margin: 0 0 3px 0 !important;
            }
            
            .approval-notification button {
                font-size: 7px !important;
                padding: 2px 4px !important;
            }
            
            .approval-notification i {
                font-size: 8px !important;
            }
        }
    `;
    document.head.appendChild(style);
    
    // Get the current infrastructure from the URL or page context
    const urlParams = new URLSearchParams(window.location.search);
    const infraParam = urlParams.get('infra');
    initServiceApproval(infraParam);
    
    // Clean up when leaving the page
    window.addEventListener('beforeunload', cleanupServiceApproval);
});

// Function to approve all services globally (not just the current page)
function approveAllServicesGlobal() {
    if (confirm(`Are you sure you want to approve ALL pending services? This will affect all services, not just the ones on the current page.`)) {
        const button = event.target;
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }
        
        fetch(`service_approval.php?action=approve_all${currentServiceInfra ? '&infra=' + encodeURIComponent(currentServiceInfra) : ''}`)
            .then(response => response.text())
            .then(result => {
                console.log(result);
                // Update the underlying pending services data
                servicesAdded = true;
                
                // Refresh the table with the latest data
                refreshServicesTable();
                
                // Enable the button again
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error approving all services:', error);
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
                alert('An error occurred while approving all services. Please try again.');
            });
    }
}

// Function to reject all services globally (not just the current page)
function rejectAllServicesGlobal() {
    if (confirm(`Are you sure you want to reject ALL pending services? This will affect all services, not just the ones on the current page.`)) {
        const button = event.target;
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }
        
        fetch(`service_approval.php?action=reject_all${currentServiceInfra ? '&infra=' + encodeURIComponent(currentServiceInfra) : ''}`)
            .then(response => response.text())
            .then(result => {
                console.log(result);
                // Update the underlying pending services data
                servicesAdded = true;
                
                // Refresh the table with the latest data
                refreshServicesTable();
                
                // Enable the button again
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error rejecting all services:', error);
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
                alert('An error occurred while rejecting all services. Please try again.');
            });
    }
} 

// Multi-select functionality
let serviceMultiSelectMode = true; // Always enabled now

function toggleServiceSelectAll() {
    const selectAllCheckbox = document.getElementById('serviceSelectAllCheckbox');
    const checkboxes = document.querySelectorAll('.service-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateServiceSelectedCount();
}

function updateServiceSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.service-checkbox:checked');
    const selectedCount = document.getElementById('serviceSelectedCount');
    const multiSelectControls = document.querySelector('.modal-multi-select-controls');
    
    if (selectedCount) {
        selectedCount.textContent = selectedCheckboxes.length;
    }
    
    // Show/hide multi-select controls based on selection
    if (multiSelectControls) {
        if (selectedCheckboxes.length > 0) {
            multiSelectControls.style.display = 'flex';
        } else {
            multiSelectControls.style.display = 'none';
        }
    }
}

function approveSelectedServices() {
    const selectedCheckboxes = document.querySelectorAll('.service-checkbox:checked');
    const selectedServices = Array.from(selectedCheckboxes).map(checkbox => ({
        id: checkbox.getAttribute('data-id'),
        ip: checkbox.getAttribute('data-ip'),
        infra: checkbox.getAttribute('data-infra')
    }));
    
    if (selectedServices.length === 0) {
        alert('No services selected');
        return;
    }
    
    if (confirm(`Are you sure you want to approve services for ${selectedServices.length} selected hosts?`)) {
        const button = event.target;
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }
        
        const promises = selectedServices.map(service => 
            fetch(`service_approval.php?id=${encodeURIComponent(service.id)}&ip=${encodeURIComponent(service.ip)}&infra=${encodeURIComponent(service.infra)}&action=approve`)
                .then(response => response.text())
        );
        
        Promise.all(promises)
            .then(() => {
                servicesAdded = true;
                refreshServicesTable();
                
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
                

            })
            .catch(error => {
                console.error('Error approving selected services:', error);
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            });
    }
}

function rejectSelectedServices() {
    const selectedCheckboxes = document.querySelectorAll('.service-checkbox:checked');
    const selectedServices = Array.from(selectedCheckboxes).map(checkbox => ({
        id: checkbox.getAttribute('data-id'),
        ip: checkbox.getAttribute('data-ip'),
        infra: checkbox.getAttribute('data-infra')
    }));
    
    if (selectedServices.length === 0) {
        alert('No services selected');
        return;
    }
    
    if (confirm(`Are you sure you want to reject services for ${selectedServices.length} selected hosts?`)) {
        const button = event.target;
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }
        
        const promises = selectedServices.map(service => 
            fetch(`service_approval.php?id=${encodeURIComponent(service.id)}&action=reject`)
                .then(response => response.text())
        );
        
        Promise.all(promises)
            .then(() => {
                servicesAdded = true;
                refreshServicesTable();
                
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
                

            })
            .catch(error => {
                console.error('Error rejecting selected services:', error);
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            });
    }
}

// Add event listener for checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('service-checkbox')) {
        updateServiceSelectedCount();
    }
});

// Toggle service dropdown menu
function toggleServiceDropdown() {
    const dropdownMenu = document.getElementById('serviceDropdownMenu');
    if (dropdownMenu) {
        dropdownMenu.style.display = dropdownMenu.style.display === 'none' ? 'block' : 'none';
    }
}

// Close dropdown when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('.dropdown')) {
        const dropdownMenu = document.getElementById('serviceDropdownMenu');
        if (dropdownMenu) {
            dropdownMenu.style.display = 'none';
        }
    }
});