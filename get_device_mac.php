<?php
header('Content-Type: application/json');

// Check if SPM module is available (like in get_spm_data.php)
$spm_check = shell_exec("php " . __DIR__ . "/checkmods.php spm 2>&1");
if (trim($spm_check) !== "true") {
    http_response_code(500);
    echo json_encode(['error' => 'SPM module is not available']);
    exit;
}

// Get the host IP from the request
$hostIp = $_GET['ip'] ?? '';

if (empty($hostIp)) {
    http_response_code(400);
    echo json_encode(['error' => 'Host IP is required']);
    exit;
}

try {
    // Validate IP address format
    if (!filter_var($hostIp, FILTER_VALIDATE_IP)) {
        throw new Exception('Invalid IP address format');
    }
    
    // Build the MAC address query with proper escaping
    $sql = <<<SQL
SELECT mac, 'node_ip' AS source FROM node_ip WHERE ip = '$hostIp' AND time_last > NOW() - INTERVAL '2 hours'
UNION 
SELECT mac, 'device' AS source FROM device WHERE ip = '$hostIp';
SQL;

    // Execute the query via psql with proper formatting options
    // Use the sudoers configuration: apache ALL=(netdisco) NOPASSWD: /usr/bin/psql -U netdisco -d netdisco *
    $command = "sudo -u netdisco /usr/bin/psql -U netdisco -d netdisco -t -A -F'|' -c " . escapeshellarg($sql);
    
    $output = shell_exec($command . ' 2>&1');
    
    if ($output === null) {
        throw new Exception('Failed to execute database query');
    }
    
    // Check for psql errors in output
    if (strpos($output, 'ERROR:') !== false) {
        throw new Exception('Database error: ' . $output);
    }

    // Parse the output - pipe-separated without headers
    $lines = array_filter(explode("\n", trim($output)));
    
    $macAddresses = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '') continue;
        
        // Parse the pipe-separated line
        $parts = explode('|', $line);
        
        if (count($parts) >= 2) {
            $mac = trim($parts[0]);
            $source = trim($parts[1]);
            
            // Only include entries with valid MAC addresses
            if (!empty($mac) && $mac !== '') {
                $macAddresses[] = [
                    'mac' => $mac,
                    'source' => $source
                ];
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'ip' => $hostIp,
        'mac_addresses' => $macAddresses,
        'count' => count($macAddresses)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database query failed: ' . $e->getMessage(),
        'raw_output' => $output ?? 'No output'
    ]);
}
?>
