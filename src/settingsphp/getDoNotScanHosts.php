<?php
// Include database connection helper
require_once '../../loadenv.php';

// Function to get database connection
function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

// Get all DO-NOT-SCAN hosts grouped by subnet
function getDoNotScanHosts() {
    $conn = getDatabaseConnection();
    
    // Get all hosts where services = '["DO-NOT-SCAN"]'
    $sql = "SELECT sp.host_ip as ip, sp.host_name as hostname, sp.infra, 
            SUBSTRING_INDEX(sp.host_ip, '.', 3) as subnet 
            FROM servicesPending sp 
            WHERE sp.services = '[\"DO-NOT-SCAN\"]' 
            ORDER BY subnet, sp.host_ip";
    $result = $conn->query($sql);
    
    // Group hosts by subnet
    $subnets = [];
    if ($result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            $subnet = $row['subnet'] . '.0';
            if (!isset($subnets[$subnet])) {
                $subnets[$subnet] = [];
            }
            $subnets[$subnet][] = $row;
        }
    }
    
    $conn->close();
    return $subnets;
}

// Generate HTML for the DO-NOT-SCAN hosts table
function generateDoNotScanHostsHTML() {
    $subnets = getDoNotScanHosts();
    
    if (empty($subnets)) {
        return '<div class="message message-info">No hosts marked for service discovery exclusions.</div>';
    }
    
    $html = '<div class="service-modules-container">';
    
    foreach ($subnets as $subnet => $hosts) {
        $hostCount = count($hosts);
        
        $html .= '<details class="module-section" open>';
        $html .= '<summary class="module-header">';
        $html .= '<div class="module-name">';
        $html .= $subnet . ' <span class="module-status-indicator">' . $hostCount . ($hostCount === 1 ? ' host' : ' hosts') . '</span>';
        $html .= '</div>';
        $html .= '</summary>';
        
        $html .= '<table class="module-table">';
        $html .= '<thead><tr><th>IP</th><th>Hostname</th><th>Actions</th></tr></thead>';
        $html .= '<tbody>';
        
        foreach ($hosts as $host) {
            $html .= '<tr class="do-not-scan-host-row" data-ip="' . htmlspecialchars($host['ip']) . '" data-infra="' . htmlspecialchars($host['infra']) . '">';
            $html .= '<td>' . htmlspecialchars($host['ip']) . '</td>';
            $html .= '<td>' . htmlspecialchars($host['hostname'] ?? '') . '</td>';
            $html .= '<td>';
            $html .= '<div class="action-buttons">';
            $html .= '<button type="button" class="btn-restart btn-remove-do-not-scan" data-ip="' . htmlspecialchars($host['ip']) . '" data-infra="' . htmlspecialchars($host['infra']) . '">';
            $html .= '<i class="fa fa-check"></i>&nbsp;Enable Service Discovery';
            $html .= '</button>';
            $html .= '</div>';
            $html .= '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</details>';
    }
    
    $html .= '</div>';
    return $html;
}

// Output the HTML
echo generateDoNotScanHostsHTML();
?> 