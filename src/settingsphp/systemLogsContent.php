<?php
// src/settingsphp/systemLogsContent.php

// Ensure session variables are available if needed (already done in systemLogs.php)
$lines_to_show = $_SESSION['lines_to_show'] ?? 25;
$selected_log_file = $_SESSION['selected_log_file'] ?? '';
$log_contents = $_SESSION['log_contents'] ?? '';
$log_options = $_SESSION['log_options'] ?? [];

// Split log contents into lines for better formatting
$log_lines = $log_contents ? explode("\n", trim($log_contents)) : [];
?>

<div id="logs-content">
    <h2><i class="fa fa-file-text-o"></i> System Logs</h2>
    <p>View logs from various system components.</p>

    <form id="log-form" action="/bubblemaps/src/settingsphp/systemLogs.php" method="post">
        <fieldset>
            <legend>Log Options</legend>

            <div class="input-group">
                <label for="selected_log_file">Log File:</label>
                <select id="selected_log_file" name="selected_log_file" style="max-width: 250px;">
                    <option value="">All Log Files</option>
                    <?php foreach ($log_options as $file_path => $label): ?>
                        <option value="<?= htmlspecialchars($file_path) ?>" <?= $selected_log_file == $file_path ? 'selected' : '' ?>><?= htmlspecialchars($label) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="input-group">
                <label for="lines_to_show">Lines to Display:</label>
                <select id="lines_to_show" name="lines_to_show" style="max-width: 150px;">
                    <option value="25" <?= $lines_to_show == 25 ? 'selected' : '' ?>>25</option>
                    <option value="50" <?= $lines_to_show == 50 ? 'selected' : '' ?>>50</option>
                    <option value="100" <?= $lines_to_show == 100 ? 'selected' : '' ?>>100</option>
                    <option value="200" <?= $lines_to_show == 200 ? 'selected' : '' ?>>200</option>
                </select>
            </div>

            <button type="submit" class="btn-restart"><i class="fa fa-download"></i><span>&nbsp;Fetch</span></button>
        </fieldset>
    </form>

    <div id="log-display" style="margin-top: 20px;">
        <?php if ($log_lines): ?>
            <div class="log-display-container"><?php
                $current_section = '';
                $section_lines = 0;
                $current_file_path = '';
                $section_content = '';
                
                foreach ($log_lines as $line) {
                    // Check if the line is one of our custom headers
                    if (strpos($line, "<span class='log-header'>") === 0) {
                        // If we have a previous section, output it
                        if ($current_section !== '') {
                            echo "<div class='log-section' data-logfile='" . htmlspecialchars($current_file_path) . "'>\n";
                            echo "<pre class='log-section-content'>" . $section_content . "</pre>\n";
                            echo "</div>\n";
                        }
                        
                        // Start new section
                        $current_section = $line;
                        $section_lines = 0;
                        $section_content = $line . "\n";
                        
                        // Extract file path for data attribute
                        if (preg_match('/--- Logs for: (.+?) \((.+?)\) ---/', $line, $matches)) {
                            $current_file_path = $matches[2];
                        }
                    } else {
                        // Only count non-empty lines as actual log content
                        if (trim($line) !== '') {
                            $section_lines++;
                        }
                        $escaped_line = htmlspecialchars($line);
                        // Apply highlighting to important keywords
                        $escaped_line = preg_replace_callback(
                            '/(warning|alert|critical|failure|fail|error|exception|severe|fatal|emergency|panic|down|offline|unreachable|timeout|denied|rejected|blocked|invalid|unauthorized|forbidden|broken|corrupt|malformed|malicious|attack|intrusion|breach|compromise|exploit|vulnerability)/i',
                            function($matches) {
                                $class = 'log-warning';
                                if (preg_match('/error|fail|exception|fatal|emergency|panic|down|offline|unreachable|timeout|denied|rejected|blocked|invalid|unauthorized|forbidden|broken|corrupt|malformed|malicious|attack|intrusion|breach|compromise|exploit|vulnerability/i', $matches[0])) {
                                    $class = 'log-error';
                                } elseif (preg_match('/critical|severe|alert/i', $matches[0])) {
                                    $class = 'log-critical';
                                }
                                return '<span class="'.$class.'">'.$matches[0].'</span>';
                            },
                            $escaped_line
                        );
                        $section_content .= $escaped_line . "\n";
                    }
                }
                
                // Output the last section
                if ($current_section !== '') {
                    echo "<div class='log-section' data-logfile='" . htmlspecialchars($current_file_path) . "'>\n";
                    echo "<pre class='log-section-content'>" . $section_content . "</pre>\n";
                    echo "</div>\n";
                }
            ?></div>
        <?php elseif (strpos($log_contents, 'Error:') === 0): ?>
             <p class="text-muted message message-error"><?= htmlspecialchars($log_contents) ?></p>
        <?php else: ?>
            <p class="text-muted">No log data available or file is empty.</p>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const logForm = document.getElementById('log-form');
    const logDisplay = document.getElementById('log-display');
    
    if (logForm && logDisplay) {
        logForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(logForm);
            
            // Show loading state
            logDisplay.innerHTML = '<p class="text-muted">Loading logs...</p>';
            
            fetch('/bubblemaps/src/settingsphp/systemLogs.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // Extract the log display content from the response
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newLogDisplay = doc.getElementById('log-display');
                
                if (newLogDisplay) {
                    logDisplay.innerHTML = newLogDisplay.innerHTML;
                } else {
                    logDisplay.innerHTML = '<p class="text-muted">Error loading logs.</p>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                logDisplay.innerHTML = '<p class="text-muted message message-error">Error loading logs.</p>';
            });
        });
    }
});
</script>