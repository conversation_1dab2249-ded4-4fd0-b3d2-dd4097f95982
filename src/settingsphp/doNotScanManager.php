<?php
// Include database connection helper
require_once '../../loadenv.php';

// Function to get database connection
function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

// Function to remove DO-NOT-SCAN status from a host
function removeDoNotScanStatus($ip, $infra) {
    $conn = getDatabaseConnection();
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Remove the entry from servicesPending
        $deleteSql = "DELETE FROM servicesPending WHERE host_ip = ? AND infra = ? AND services = '[\"DO-NOT-SCAN\"]'";
        $stmt = $conn->prepare($deleteSql);
        $stmt->bind_param('ss', $ip, $infra);
        $stmt->execute();
        $stmt->close();
        
        // Unmute the host
        $unmuteSql = "UPDATE hosts SET muted = 0 WHERE ip = ? AND infra = ?";
        $stmt = $conn->prepare($unmuteSql);
        $stmt->bind_param('ss', $ip, $infra);
        $stmt->execute();
        $stmt->close();
        
        $conn->commit();
        
        $result = [
            'success' => true,
            'message' => 'Host removed from DO-NOT-SCAN status and unmuted'
        ];
    } catch (Exception $e) {
        $conn->rollback();
        $result = [
            'success' => false,
            'message' => 'Failed to remove DO-NOT-SCAN status: ' . $e->getMessage()
        ];
    }
    
    $conn->close();
    return $result;
}

// Process AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set response header
    header('Content-Type: application/json');
    
    // Check if the action is valid
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'remove_do_not_scan':
                // Check if required parameters are provided
                if (isset($_POST['ip']) && isset($_POST['infra'])) {
                    $ip = $_POST['ip'];
                    $infra = $_POST['infra'];
                    
                    // Perform the removal operation
                    $result = removeDoNotScanStatus($ip, $infra);
                    
                    // Return JSON response
                    echo json_encode($result);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Missing required parameters (ip, infra)'
                    ]);
                }
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'Invalid action specified'
                ]);
                break;
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No action specified'
        ]);
    }
} else {
    // Handle non-POST requests
    header('HTTP/1.1 405 Method Not Allowed');
    header('Allow: POST');
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST.'
    ]);
}
?> 