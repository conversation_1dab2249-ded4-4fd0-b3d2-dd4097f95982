<?php
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Include the helper functions (only if not already included)
    if (!function_exists('get_memory_details')) {
        require_once __DIR__ . '/systemHelpers.php';
    }
    
    // Check if functions are available
    if (!function_exists('get_memory_details')) {
        throw new Exception('Helper functions not loaded');
    }

    // Get current system statistics
    $mem_details = get_memory_details();
    $mem_total = $mem_details['total_kb'] * 1024;
    $mem_available = $mem_details['available_kb'] * 1024;
    $mem_free = $mem_details['free_kb'] * 1024;
    $mem_buffers = $mem_details['buffers_kb'] * 1024;
    $mem_cached = $mem_details['cached_kb'] * 1024;

    // Calculate used memory including buffers and cache
    $mem_used = $mem_total > 0 ? $mem_total - $mem_free - $mem_buffers - $mem_cached : 0;
    $mem_used_plus_cache = $mem_used + $mem_buffers + $mem_cached;
    $mem_percent_used_plus_cache = $mem_total > 0 ? round(($mem_used_plus_cache / $mem_total) * 100, 1) : 0;

    // Swap information
    $swap_total = $mem_details['swap_total_kb'] * 1024;
    $swap_free = $mem_details['swap_free_kb'] * 1024;
    $swap_used = $swap_total > 0 ? $swap_total - $swap_free : 0;
    $swap_percent_used = $swap_total > 0 ? round(($swap_used / $swap_total) * 100, 1) : 0;

    // Disk information
    $disk_details = get_disk_details('/');
    $disk_total = $disk_details['total_kb'] * 1024;
    $disk_used = $disk_details['used_kb'] * 1024;
    $disk_free = $disk_details['free_kb'] * 1024;
    $disk_percent_used = $disk_details['percent_used'];
    if ($disk_percent_used <= 0 && $disk_total > 0) {
        $disk_percent_used = round(($disk_used / $disk_total) * 100, 1);
    }

    // CPU usage - use a simpler approach that works better
    $cpu_usage_percent = 'N/A';
    $cpu_usage = 0;

    // Get CPU usage using top command for more accurate results
    $top_output = run_command("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
    if ($top_output && is_numeric($top_output)) {
        $cpu_usage = round((float)$top_output, 1);
        $cpu_usage_percent = $cpu_usage . '%';
    } else {
        // Fallback to /proc/stat method
        $stat = @file_get_contents('/proc/stat');
        if ($stat) {
            $cpu_lines = explode("\n", $stat);
            $cpu_line = null;
            foreach ($cpu_lines as $line) {
                if (strpos($line, 'cpu ') === 0) {
                    $cpu_line = $line;
                    break;
                }
            }
            
            if ($cpu_line) {
                $cpu_parts = preg_split('/\s+/', trim($cpu_line));
                if (count($cpu_parts) >= 5) {
                    $user = intval($cpu_parts[1]);
                    $nice = intval($cpu_parts[2]);
                    $system = intval($cpu_parts[3]);
                    $idle = intval($cpu_parts[4]);
                    $iowait = isset($cpu_parts[5]) ? intval($cpu_parts[5]) : 0;
                    $irq = isset($cpu_parts[6]) ? intval($cpu_parts[6]) : 0;
                    $softirq = isset($cpu_parts[7]) ? intval($cpu_parts[7]) : 0;
                    $steal = isset($cpu_parts[8]) ? intval($cpu_parts[8]) : 0;
                    
                    $total = $user + $nice + $system + $idle + $iowait + $irq + $softirq + $steal;
                    $used = $total - $idle - $iowait;
                    
                    if ($total > 0) {
                        $cpu_usage = round(($used / $total) * 100, 1);
                        $cpu_usage_percent = $cpu_usage . '%';
                    }
                }
            }
        }
    }

    $mem_threshold_class = get_threshold_class($mem_percent_used_plus_cache, 75, 90); // Normal thresholds for used memory
    $swap_threshold_class = get_threshold_class($swap_percent_used, 50, 80);
    $disk_threshold_class = get_threshold_class($disk_percent_used, 80, 95);
    $cpu_threshold_class = get_threshold_class($cpu_usage, 70, 90);

    // Return JSON response
    echo json_encode([
        'memory' => [
            'total' => format_bytes($mem_total),
            'used' => format_bytes($mem_used),
            'used_plus_cache' => format_bytes($mem_used_plus_cache),
            'free' => format_bytes($mem_free),
            'percent_used_plus_cache' => $mem_percent_used_plus_cache,
            'threshold_class' => $mem_threshold_class
        ],
        'swap' => [
            'total' => format_bytes($swap_total),
            'used' => format_bytes($swap_used),
            'free' => format_bytes($swap_free),
            'percent_used' => $swap_percent_used,
            'threshold_class' => $swap_threshold_class,
            'configured' => $swap_total > 0
        ],
        'disk' => [
            'total' => format_bytes($disk_total),
            'used' => format_bytes($disk_used),
            'free' => format_bytes($disk_free),
            'percent_used' => $disk_percent_used,
            'threshold_class' => $disk_threshold_class
        ],
        'cpu' => [
            'usage_percent' => $cpu_usage_percent,
            'usage' => $cpu_usage,
            'threshold_class' => $cpu_threshold_class
        ]
    ]);
    
} catch (Exception $e) {
    // Return error response
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
