<?php
header('Content-Type: application/json');

// Function to read bubblemap configs
function readBubblemapConfig($key) {
    $configFile = dirname(__FILE__) . '/../../conf/bubblemap_configs';
    if (!file_exists($configFile)) {
        return null;
    }
    
    $lines = file($configFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, $key . ':') === 0) {
            return trim(substr($line, strlen($key) + 1));
        }
    }
    return null;
}

// Function to write bubblemap configs
function writeBubblemapConfig($key, $value) {
    $configFile = dirname(__FILE__) . '/../../conf/bubblemap_configs';
    
    if (!file_exists($configFile)) {
        return false;
    }
    
    $lines = file($configFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $updated = false;
    
    // Update existing line or add new one
    for ($i = 0; $i < count($lines); $i++) {
        if (strpos($lines[$i], $key . ':') === 0) {
            $lines[$i] = $key . ': ' . $value;
            $updated = true;
            break;
        }
    }
    
    if (!$updated) {
        $lines[] = $key . ': ' . $value;
    }
    
    return file_put_contents($configFile, implode("\n", $lines) . "\n") !== false;
}

// Function to ensure service discovery line exists
function ensureServiceDiscoveryLine() {
    $configFile = dirname(__FILE__) . '/../../conf/bubblemap_configs';
    
    if (!file_exists($configFile)) {
        return false;
    }
    
    $lines = file($configFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $hasServiceDiscovery = false;
    
    // Check if service discovery line exists
    foreach ($lines as $line) {
        if (strpos($line, 'service discovery:') === 0) {
            $hasServiceDiscovery = true;
            break;
        }
    }
    
    // If it doesn't exist, add it
    if (!$hasServiceDiscovery) {
        $lines[] = 'service discovery: on';
        return file_put_contents($configFile, implode("\n", $lines) . "\n") !== false;
    }
    
    return true;
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Ensure the service discovery line exists
    ensureServiceDiscoveryLine();
    
    // Read the current setting
    $setting = readBubblemapConfig('service discovery');
    echo json_encode([
        'success' => true,
        'setting' => $setting ?: 'on' // Default to 'on' if not found
    ]);
} elseif ($method === 'POST') {
    // Update the setting
    $input = json_decode(file_get_contents('php://input'), true);
    $newSetting = $input['setting'] ?? null;
    
    if ($newSetting && in_array($newSetting, ['on', 'off'])) {
        if (writeBubblemapConfig('service discovery', $newSetting)) {
            echo json_encode([
                'success' => true,
                'message' => 'Service discovery setting updated successfully'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to write configuration file'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid setting value. Must be "on" or "off"'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
}
?>
