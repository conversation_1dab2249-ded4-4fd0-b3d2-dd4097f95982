<?php
/**
 * Run the update_apm_status.php script
 * This file acts as a wrapper to execute the update script via AJAX
 */

// Handle progress requests only
if (isset($_GET['action']) && $_GET['action'] === 'progress') {
    // Return progress information
    header('Content-Type: application/json');
    
    $progressFile = dirname(__FILE__) . '/../../locks/apm_progress.json';
    
    if (file_exists($progressFile)) {
        $progress = json_decode(file_get_contents($progressFile), true);
        
        // Check if JSON decoding was successful and progress is not null
        if ($progress !== null && isset($progress['timestamp'])) {
            // If progress file is older than 2 minutes, mark as idle
            if ((time() - $progress['timestamp']) > 120) {
                $progress['status'] = 'idle';
                $progress['message'] = 'Waiting for next update cycle';
            }
            
            echo json_encode($progress);
        } else {
            // Invalid JSON or missing timestamp, return default response
            echo json_encode([
                'current' => 0,
                'total' => 0,
                'percentage' => 0,
                'status' => 'idle',
                'timestamp' => time(),
                'message' => 'Invalid progress file format'
            ]);
        }
    } else {
        // No progress file exists
        echo json_encode([
            'current' => 0,
            'total' => 0,
            'percentage' => 0,
            'status' => 'idle',
            'timestamp' => time(),
            'message' => 'Waiting for next update cycle'
        ]);
    }
    exit;
}

// If no valid action is provided, return an error
header('Content-Type: application/json');
echo json_encode(['error' => 'Invalid action']);
?> 