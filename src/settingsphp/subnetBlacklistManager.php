<?php
include "../../loadenv.php";

function getDatabaseConnection() {
    $conn = new mysqli($_ENV["DB_SERVER"], $_ENV["DB_USER"], $_ENV["DB_PASSWORD"], "bubblemaps");
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    return $conn;
}

function getBlacklistFile() {
    return "../../conf/subnets_blacklist";
}

function getBlacklistedSubnets() {
    $file = getBlacklistFile();
    if (!file_exists($file)) {
        return [];
    }
    $content = file_get_contents($file);
    return array_filter(array_map('trim', explode("\n", $content)));
}

function saveBlacklistedSubnets($subnets) {
    $file = getBlacklistFile();
    $content = implode("\n", array_filter($subnets));
    return file_put_contents($file, $content) !== false;
}

function getAllSubnets() {
    $conn = getDatabaseConnection();
    $stmt = $conn->prepare("SELECT DISTINCT subnet FROM subnets WHERE subnet NOT IN ('External', 'localhost', 'Azure') ORDER BY subnet");
    $stmt->execute();
    $result = $stmt->get_result();
    
    $subnets = [];
    while ($row = $result->fetch_assoc()) {
        $subnets[] = $row['subnet'];
    }
    
    $stmt->close();
    $conn->close();
    return $subnets;
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Return available subnets for dropdown
    $subnets = getAllSubnets();
    $blacklisted = getBlacklistedSubnets();
    
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'subnets' => $subnets,
        'blacklisted' => $blacklisted
    ]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        $subnet = trim($_POST['subnet'] ?? '');
        if (empty($subnet)) {
            echo json_encode(['success' => false, 'message' => 'Subnet is required']);
            exit;
        }
        
        $blacklisted = getBlacklistedSubnets();
        if (in_array($subnet, $blacklisted)) {
            echo json_encode(['success' => false, 'message' => 'Subnet is already blacklisted']);
            exit;
        }
        
        $blacklisted[] = $subnet;
        if (saveBlacklistedSubnets($blacklisted)) {
            echo json_encode(['success' => true, 'message' => 'Subnet blacklisted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to save blacklist']);
        }
        exit;
    }
    
    if ($action === 'remove') {
        $subnet = trim($_POST['subnet'] ?? '');
        if (empty($subnet)) {
            echo json_encode(['success' => false, 'message' => 'Subnet is required']);
            exit;
        }
        
        $blacklisted = getBlacklistedSubnets();
        $key = array_search($subnet, $blacklisted);
        if ($key !== false) {
            unset($blacklisted[$key]);
            if (saveBlacklistedSubnets($blacklisted)) {
                echo json_encode(['success' => true, 'message' => 'Subnet removed from blacklist']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to save blacklist']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Subnet not found in blacklist']);
        }
        exit;
    }
    
    if ($action === 'list') {
        $blacklisted = getBlacklistedSubnets();
        echo json_encode(['success' => true, 'blacklisted' => $blacklisted]);
        exit;
    }
}

// Default response
echo json_encode(['success' => false, 'message' => 'Invalid request']);
?>
