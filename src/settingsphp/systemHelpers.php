<?php
// --- Helper Functions for System Statistics ---

/**
 * Executes a shell command and returns the trimmed output.
 * Returns null if the command fails or returns no output.
 */
function run_command($command) {
    // Basic security check (less critical for hardcoded commands)
    if (strpbrk($command, ';|&`') !== false && php_sapi_name() != 'cli') {
         // Log potentially unsafe commands if they were dynamic
         // error_log("Potentially unsafe command attempt blocked: " . $command);
         // return null;
    }
    // Redirect stderr to stdout to potentially capture error messages if needed for debugging
    $output = shell_exec($command . ' 2>&1'); // Added 2>&1
    return $output ? trim($output) : null;
}

/**
 * Formats bytes into a human-readable format (KB, MB, GB, TB).
 */
function format_bytes($bytes, $precision = 2) {
    if ($bytes <= 0) return '0 B';
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    // Ensure division by zero is not possible
    $divisor = pow(1024, $pow);
    if ($divisor == 0) return '0 B'; // Avoid division by zero
    $bytes /= $divisor;
    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * Parses /proc/meminfo to get memory details.
 * Returns an array [total_kb, free_kb, available_kb, swap_total_kb, swap_free_kb, buffers_kb, cached_kb]
 */
function get_memory_details() {
    $meminfo_raw = @file_get_contents('/proc/meminfo'); // Use @ to suppress warnings if file not readable
    $data = [];
    if ($meminfo_raw) {
        preg_match('/^MemTotal:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        $data['total_kb'] = isset($matches[1]) ? (int)$matches[1] : 0;

        preg_match('/^MemFree:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        $data['free_kb'] = isset($matches[1]) ? (int)$matches[1] : 0;

        // Extract buffers and cached memory for more detailed calculation
        $buffers_kb = 0;
        $cached_kb = 0;
        preg_match('/^Buffers:\s+(\d+)\s+kB/m', $meminfo_raw, $buf_match);
        if(isset($buf_match[1])) $buffers_kb = (int)$buf_match[1];
        preg_match('/^Cached:\s+(\d+)\s+kB/m', $meminfo_raw, $cache_match);
        if(isset($cache_match[1])) $cached_kb = (int)$cache_match[1];
        
        $data['buffers_kb'] = $buffers_kb;
        $data['cached_kb'] = $cached_kb;

        // MemAvailable is generally a better measure of usable memory than MemFree
        preg_match('/^MemAvailable:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        // Fallback to Free + Buffers + Cached calculation if MemAvailable isn't present (less accurate)
        if (isset($matches[1])) {
            $data['available_kb'] = (int)$matches[1];
        } else {
             // Simple fallback: Free + Buffers + Cached. Might overestimate available.
            $data['available_kb'] = $data['free_kb'] + $buffers_kb + $cached_kb;
        }

        preg_match('/^SwapTotal:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        $data['swap_total_kb'] = isset($matches[1]) ? (int)$matches[1] : 0;

        preg_match('/^SwapFree:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        $data['swap_free_kb'] = isset($matches[1]) ? (int)$matches[1] : 0;

    } else {
         // Return zeros if /proc/meminfo cannot be read
         $data = [
             'total_kb' => 0, 'free_kb' => 0, 'available_kb' => 0,
             'buffers_kb' => 0, 'cached_kb' => 0,
             'swap_total_kb' => 0, 'swap_free_kb' => 0
         ];
    }
    return $data;
}

/**
 * Gets disk usage details for a given mount point (default '/').
 * Returns an array [total_kb, used_kb, free_kb, percent_used]
 */
function get_disk_details($mount_point = '/') {
    $data = ['total_kb' => 0, 'used_kb' => 0, 'free_kb' => 0, 'percent_used' => 0];
    // df -Pk outputs in 1K-blocks. Use -P for POSIX standard output.
    $df_output = run_command("df -Pk " . escapeshellarg($mount_point));
    if ($df_output) {
        $lines = explode("\n", $df_output);
        if (count($lines) > 1) {
            // Find the line corresponding to the mount point (usually the second line for '/')
            $target_line = null;
             // Skip header line (index 0)
            for ($i = 1; $i < count($lines); $i++) {
                $parts = preg_split('/\s+/', trim($lines[$i]));
                // Check if the last element matches the mount point
                if (end($parts) === $mount_point) {
                    $target_line = $lines[$i];
                    break;
                }
            }
            // Fallback: If not found by exact mount point name (e.g., '/dev/sda1' shown instead of '/'),
            // assume the first data line is the one we want if $mount_point is '/'
            if ($target_line === null && $mount_point === '/' && isset($lines[1])) {
                 $target_line = $lines[1];
            }

            if ($target_line !== null) {
                $parts = preg_split('/\s+/', trim($target_line));
                // Expected format (POSIX): Filesystem 1024-blocks Used Available Capacity Mounted on
                // Indices:                    0          1           2    3         4        5
                 if (count($parts) >= 6 && is_numeric($parts[1]) && is_numeric($parts[2]) && is_numeric($parts[3])) {
                     $data['total_kb'] = (int)$parts[1];
                     $data['used_kb'] = (int)$parts[2];
                     $data['free_kb'] = (int)$parts[3];
                     $percent_str = rtrim($parts[4], '%'); // Capacity column
                     $data['percent_used'] = is_numeric($percent_str) ? (float)$percent_str : 0;
                 }
            } else {
                 // Log if the mount point wasn't found in df output
                 // error_log("Could not find mount point '$mount_point' in df output: " . $df_output);
            }
        }
    }
    return $data;
}

/**
 * Determines the CSS class ('success', 'warning', 'critical') based on percentage usage.
 */
function get_threshold_class($percent, $warn_threshold = 70, $crit_threshold = 90) {
    // Ensure thresholds are logical (warning <= critical)
    if ($warn_threshold > $crit_threshold) {
        $warn_threshold = $crit_threshold; // Adjust if accidentally reversed
    }

    // Clamp percentage between 0 and 100 for safety
    $percent = max(0, min(100, $percent));

    if ($percent >= $crit_threshold) {
        return 'critical';
    } elseif ($percent >= $warn_threshold) {
        return 'warning';
    } else {
        return 'success';
    }
}
?>
