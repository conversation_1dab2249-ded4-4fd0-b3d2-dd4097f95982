<?php
// --- Set timezone (optional, but good practice) ---
// Ensure this matches the timezone setting of your main application if necessary
// date_default_timezone_set('UTC'); // Replace 'UTC' with your server's timezone

// Include the helper functions
require_once __DIR__ . '/systemHelpers.php';

// Autoload the Blesk class (same method as in /var/www/html/options.php)
spl_autoload_register(function ($class) {
    $class_path = __DIR__ . '/../../../include/class.' . $class . '.php';
    if (file_exists($class_path)) {
        include $class_path;
    }
});

// Initialize Blesk class
$Blesk = new Blesk();


// --- Gather Information ---

$hostname = gethostname() ?: 'N/A';

// --- IP Address Retrieval (Improved) ---
$ip_output = run_command('hostname -I'); // Try 'hostname -I' first
$ip_addresses = [];
if ($ip_output) {
    $ip_addresses = preg_split('/\s+/', trim($ip_output)); // Split by space
} else {
    // Fallback 1: Use 'ip' command for global scope IPv4
    $ip_output_fallback = run_command("ip -4 -o addr show scope global | awk '{print $4}' | cut -d/ -f1");
     if ($ip_output_fallback) {
         $ip_addresses = explode("\n", trim($ip_output_fallback));
     } else {
         // Fallback 2: Original command (less reliable)
         $ip_output_original = run_command("ip -4 addr show | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | cut -d/ -f1");
         if ($ip_output_original) {
              $ip_addresses = explode("\n", trim($ip_output_original));
         }
     }
}
// Filter out potential empty elements, loopback, and ensure uniqueness
$ip_addresses = array_filter(array_unique($ip_addresses), function($ip) {
    // return !empty($ip) && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false;
    // Note: The above filter primarily keeps public IPs. Adjust if you NEED private IPs.
    // Use simpler filter to include private IPs:
    return !empty($ip) && filter_var($ip, FILTER_VALIDATE_IP) !== false; // Keep any valid IP
});
$ip_address_str = !empty($ip_addresses) ? implode(', ', $ip_addresses) : 'N/A';
// If the strict filter removed everything, try getting the first non-loopback again simpler
if ($ip_address_str === 'N/A') {
    $ip_output_simple = run_command("ip -4 -o addr show | grep -v '127.0.0.1' | awk '{print $4}' | cut -d/ -f1 | head -n 1");
    if ($ip_output_simple) {
        $ip_address_str = $ip_output_simple;
    }
}
// --- End IP Address Retrieval ---


// Try getting distribution-specific version, then fallback
$os_version = 'N/A';
if (is_readable('/etc/os-release')) {
    $os_release = parse_ini_file('/etc/os-release');
    $os_version = $os_release['PRETTY_NAME'] ?? $os_release['NAME'] ?? 'N/A';
} elseif (is_readable('/etc/redhat-release')) {
    $os_version = trim(file_get_contents('/etc/redhat-release'));
} elseif (is_readable('/etc/lsb-release')) {
     $lsb_release = parse_ini_file('/etc/lsb-release');
     $os_version = $lsb_release['DISTRIB_DESCRIPTION'] ?? 'N/A';
} elseif (($uname_s = run_command('uname -s')) && ($uname_r = run_command('uname -r'))) {
     $os_version = trim($uname_s . ' ' . $uname_r);
}
$os_version = $os_version ?: 'N/A';

// --- Operation Mode ---
// Use Blesk class method to get edition (same as in /var/www/html/options.php)
if (!$edition = $Blesk->getEdition()) {
    $edition = '';
}

$operation_mode = $edition;

// Get license information using Blesk class methods (same as in /var/www/html/options.php)
if (!$license = $Blesk->getLickey()) {
    $license = '';
}

if (!$expiry_date = $Blesk->getExpDate()) {
    $expiry_date = '';
}

// Calculate days remaining from the expiry date we got from Blesk class
$days_remaining = "N/A";
if ($expiry_date && $expiry_date !== '0000-00-00') {
    $expiry_timestamp = strtotime($expiry_date);
    $current_timestamp = time();
    if ($expiry_timestamp !== false) {
        $seconds_remaining = $expiry_timestamp - $current_timestamp;
        $days_remaining = max(0, floor($seconds_remaining / (60 * 60 * 24)) + 1); // Include today
    }
}

// Get enabled modules using Blesk class method (same as in /var/www/html/options.php)
$active_modules = '';
if (!$modules = $Blesk->getModules()) {
    $active_modules = '';
} else {
    foreach($modules as $module) {
        $active_modules .= "$module, " ;
    }
    $active_modules = trim($active_modules);
    $active_modules = rtrim($active_modules, ',');
    $active_modules = strtoupper($active_modules);
}

$enabled_modules = $active_modules;

// Get monitored nodes - avoid getMonitoredNodes() as it requires LangInit which needs settings.php
$nodes_monitored_raw = run_command('binit -apmcount');
$nodes_monitored = is_numeric($nodes_monitored_raw) ? (int)$nodes_monitored_raw : "N/A";

$system_time = date('Y-m-d H:i:s T'); // Uses server's default timezone unless overridden above
$uptime_str = run_command("uptime -p"); // Prefer pretty format
if (!$uptime_str) {
    $uptime_raw = run_command("uptime");
    if ($uptime_raw && preg_match('/up\s+(.*?),\s+\d+\s+user/', $uptime_raw, $match)) {
        $uptime_str = trim($match[1]);
    } elseif ($uptime_raw) {
        $uptime_str = $uptime_raw; // Fallback to raw string
    } else {
         $uptime_str = "N/A";
    }
} else {
    // Remove leading 'up ' if present from 'uptime -p'
    $uptime_str = preg_replace('/^up\s+/', '', $uptime_str);
}
$uptime_str = ucfirst($uptime_str); // Capitalize first letter

// --- Resource Usage ---
$mem_details = get_memory_details();
$mem_total = $mem_details['total_kb'] * 1024;
$mem_available = $mem_details['available_kb'] * 1024;

// Calculate used memory including buffers and cache (similar to 'free' command output)
$mem_free = $mem_details['free_kb'] * 1024;
$mem_buffers = $mem_details['buffers_kb'] * 1024;
$mem_cached = $mem_details['cached_kb'] * 1024;

// Used memory = Total - Free - Buffers - Cached (this matches the 'used' column in 'free' command)
$mem_used = $mem_total > 0 ? $mem_total - $mem_free - $mem_buffers - $mem_cached : 0;
$mem_percent_used = $mem_total > 0 ? round(($mem_used / $mem_total) * 100, 1) : 0;

$swap_total = $mem_details['swap_total_kb'] * 1024;
$swap_free = $mem_details['swap_free_kb'] * 1024;
$swap_used = $swap_total > 0 ? $swap_total - $swap_free : 0;
$swap_percent_used = $swap_total > 0 ? round(($swap_used / $swap_total) * 100, 1) : 0;

$disk_details = get_disk_details('/'); // Check root filesystem '/'
$disk_total = $disk_details['total_kb'] * 1024;
$disk_used = $disk_details['used_kb'] * 1024;
$disk_free = $disk_details['free_kb'] * 1024;
// Use percent directly from df command as it's often more accurate (accounts for reserved blocks)
$disk_percent_used = $disk_details['percent_used'];
// Fallback calculation if df didn't provide percentage
if ($disk_percent_used <= 0 && $disk_total > 0) {
    $disk_percent_used = round(($disk_used / $disk_total) * 100, 1);
}


// --- CPU Information ---
$cpu_model = 'N/A';
$cpu_cores = 'N/A';
$cpu_threads = 'N/A';
$cpu_speed = 'N/A';
$cpu_cache = 'N/A';

$lscpu_output = run_command("lscpu");
if ($lscpu_output) {
    // Extract model name
    if (preg_match('/^Model name:\s+(.+)/m', $lscpu_output, $matches)) {
        $cpu_model = trim($matches[1]);
    } elseif (preg_match('/^Vendor ID:\s+(.+)/m', $lscpu_output, $matches)) {
        $cpu_model = trim($matches[1]);
    }
    
    // Extract cores and threads
    if (preg_match('/^CPU\(s\):\s+(\d+)/m', $lscpu_output, $matches)) {
        $cpu_threads = $matches[1];
    }
    if (preg_match('/^Core\(s\) per socket:\s+(\d+)/m', $lscpu_output, $matches)) {
        $cores_per_socket = $matches[1];
    }
    if (preg_match('/^Socket\(s\):\s+(\d+)/m', $lscpu_output, $matches)) {
        $sockets = $matches[1];
        if (isset($cores_per_socket)) {
            $cpu_cores = $sockets * $cores_per_socket;
        }
    }
    
    // Extract CPU speed
    if (preg_match('/^CPU MHz:\s+([\d.]+)/m', $lscpu_output, $matches)) {
        $cpu_speed = round($matches[1] / 1000, 2) . ' GHz';
    } elseif (preg_match('/^CPU max MHz:\s+([\d.]+)/m', $lscpu_output, $matches)) {
        $cpu_speed = round($matches[1] / 1000, 2) . ' GHz';
    }
    
    // Extract cache info
    if (preg_match('/^L3 cache:\s+(.+)/m', $lscpu_output, $matches)) {
        $cpu_cache = trim($matches[1]);
    } elseif (preg_match('/^L2 cache:\s+(.+)/m', $lscpu_output, $matches)) {
        $cpu_cache = trim($matches[1]);
    }
}

// Fallback to /proc/cpuinfo if lscpu didn't provide all info
if ($cpu_model === 'N/A' || $cpu_cores === 'N/A') {
    $cpuinfo_raw = @file_get_contents('/proc/cpuinfo');
    if ($cpuinfo_raw) {
        if ($cpu_model === 'N/A' && preg_match('/model name\s+:\s+(.+)/', $cpuinfo_raw, $matches)) {
            $cpu_model = trim($matches[1]);
        }
        if ($cpu_cores === 'N/A') {
            $cpu_cores = substr_count($cpuinfo_raw, 'processor');
        }
        if ($cpu_speed === 'N/A' && preg_match('/cpu MHz\s+:\s+([\d.]+)/', $cpuinfo_raw, $matches)) {
            $cpu_speed = round($matches[1] / 1000, 2) . ' GHz';
        }
    }
}

// Fallback to nproc for thread count if needed
if ($cpu_threads === 'N/A') {
    $nproc_output = run_command('nproc');
    if ($nproc_output && is_numeric($nproc_output)) {
        $cpu_threads = (int)$nproc_output;
    }
}

// Format CPU string
$cpu_total_str = '';
if ($cpu_model !== 'N/A') {
    $cpu_total_str .= $cpu_model;
    if ($cpu_cores !== 'N/A' && $cpu_threads !== 'N/A') {
        $cpu_total_str .= " ($cpu_cores Cores, $cpu_threads Threads)";
    } elseif ($cpu_cores !== 'N/A') {
        $cpu_total_str .= " ($cpu_cores Cores)";
    }
    if ($cpu_speed !== 'N/A') {
        $cpu_total_str .= " @ $cpu_speed";
    }
    if ($cpu_cache !== 'N/A') {
        $cpu_total_str .= " | $cpu_cache Cache";
    }
} else {
    $cpu_total_str = $cpu_cores !== 'N/A' ? $cpu_cores . ' Core(s)' : 'N/A';
}


// --- Determine Threshold Classes for Progress Bars ---
// Adjust these thresholds as desired
$mem_warn_threshold = 75;
$mem_crit_threshold = 90;
$swap_warn_threshold = 50; // Warning if swap usage is moderate
$swap_crit_threshold = 80; // Critical if swap usage is high
$disk_warn_threshold = 80;
$disk_crit_threshold = 95; // Often higher threshold for disk
$cpu_warn_threshold = 70; // Warning if CPU usage is high
$cpu_crit_threshold = 90; // Critical if CPU usage is very high

// License threshold values (in days)
$license_warn_threshold = 90; // 3 months
$license_crit_threshold = 30; // 1 month

// Determine license status icon class based on days remaining
$license_status_class = 'success'; // Default green
$license_tooltip = 'License valid';

// Handle different formats from Blesk class
if (is_numeric($days_remaining)) {
    if ($days_remaining <= $license_crit_threshold) {
        $license_status_class = 'critical'; // Red for less than 1 month
        $license_tooltip = 'License expired or expires in less than 1 month';
    } elseif ($days_remaining <= $license_warn_threshold) {
        $license_status_class = 'warning'; // Yellow for less than 3 months
        $license_tooltip = 'License expires in less than 3 months';
    } else {
        $license_tooltip = 'License valid';
    }
} elseif ($days_remaining === 'expired' || $days_remaining === 'n/a') {
    $license_status_class = 'critical';
    $license_tooltip = 'License expired or invalid';
}

$mem_threshold_class = get_threshold_class($mem_percent_used, $mem_warn_threshold, $mem_crit_threshold);
$swap_threshold_class = get_threshold_class($swap_percent_used, $swap_warn_threshold, $swap_crit_threshold);
$disk_threshold_class = get_threshold_class($disk_percent_used, $disk_warn_threshold, $disk_crit_threshold);


// --- HTML Fragment Output ---
?>

<h2><i class="fa fa-server"></i> General Information</h2>
<p>Overview of the machine's identity and basic configuration.</p>
<dl class="info-grid">
    <dt><i class="fa fa-desktop"></i> Hostname:</dt>
    <dd><?php echo htmlspecialchars($hostname); ?></dd>

    <dt><i class="fa fa-globe"></i> IP Address:</dt>
    <dd><?php echo htmlspecialchars($ip_address_str); ?></dd>

    <dt><i class="fa fa-info-circle"></i> Software Version:</dt>
    <dd><?php echo htmlspecialchars($os_version); ?></dd>

    <dt><i class="fa fa-shield"></i> Operation Mode:</dt>
    <dd><?php echo htmlspecialchars($operation_mode); ?></dd>

    <dt><i class="fa fa-key"></i> License:</dt>
    <dd>
        <span class="license-status" title="<?php echo htmlspecialchars($license_tooltip); ?>">
            <i class="fa fa-certificate <?php echo $license_status_class; ?>"></i>
        </span>
        <button id="renew-license-btn" class="btn-restart" style="margin-left: 10px;">
            Renew
        </button>
    </dd>

    <dt><i class="fa fa-calendar-times-o"></i> Expiry Date:</dt>
    <dd><?php echo htmlspecialchars($expiry_date); ?></dd>

    <dt><i class="fa fa-hourglass-half"></i> Days Remaining:</dt>
    <dd><?php echo htmlspecialchars($days_remaining); ?></dd>

    <dt><i class="fa fa-puzzle-piece"></i> Enabled Modules:</dt>
    <dd><?php echo htmlspecialchars($enabled_modules); ?></dd>

    <dt><i class="fa fa-sitemap"></i> Nodes Monitored:</dt>
    <dd><?php echo htmlspecialchars($nodes_monitored); ?></dd>
</dl>

<style>
/* License status icon styles */
.license-status .fa-certificate {
    font-size: 18px;
}
.license-status .success {
    color: #4caf50; /* Green */
}
.license-status .warning {
    color: #ff9800; /* Yellow/Orange */
}
.license-status .critical {
    color: #f44336; /* Red */
}
</style>

<!-- Hidden License Renewal Form -->
<div id="renew-license-form" style="display: none; margin-top: 15px; padding: 20px; border: 1px solid var(--border); border-radius: var(--radius); background-color: var(--surface);">
    <h4 style="margin-bottom: 15px; font-size: 16px; color: var(--text); display: flex; align-items: center;"><i class="fa fa-key" style="margin-right: 8px;"></i> Renew License Key</h4>
    <!-- Modified form to post directly like the original lic.php -->
    <form id="license-form-actual" action="../../../proc.php" method="post" autocomplete="off">
        <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 15px;">
            <label for="license-key-input" style="margin-bottom: 5px; font-weight: 500; color: var(--text); font-size: 14px;">Enter New License Key:</label>
            <!-- Using textarea like original lic.php, though input type=text should also work -->
            <textarea id="license-key-input" name="lk" class="form-control" placeholder="XXXXXX-XXXXXX-XXXXXX-XXXXXX" required rows="3" style="background: var(--background); color: var(--text); border: 1px solid var(--border); padding: 8px 10px; border-radius: 6px; font-size: 14px; width: 95%; transition: border-color 0.2s ease;"></textarea>
            <small style="display: block; color: var(--text-secondary); font-size: 12px; margin-top: 5px;">Enter your license key in the format shown above.</small>
        </div>
        <div class="input-group" style="display: flex; align-items: center; margin-bottom: 15px;">
            <input type="checkbox" id="agree-terms" name="agree_terms" required style="margin-right: 8px;">
            <label for="agree-terms" style="color: var(--text); font-size: 14px;">
                I agree to the <a href="#" id="open-terms-modal" style="color: var(--primary); text-decoration: underline;">Terms and Conditions</a>
            </label>
        </div>
        <div class="action-buttons" style="display: flex; gap: 6px; align-items: center; justify-content: flex-start; flex-wrap: nowrap;">
            <button type="submit" class="btn-restart" style="padding: 6px 12px; height: 32px; border: 1px solid var(--border); border-radius: 4px; cursor: pointer; font-size: 13px; display: inline-flex; align-items: center; justify-content: center; background-color: var(--background); color: var(--text); transition: all 0.2s;"><i class="fa fa-check" style="margin-right: 6px;"></i>Submit Key</button>
            <button type="button" id="cancel-renew-btn" class="btn-restart" style="padding: 6px 12px; height: 32px; border: 1px solid var(--border); border-radius: 4px; cursor: pointer; font-size: 13px; display: inline-flex; align-items: center; justify-content: center; background-color: var(--background); color: var(--text); transition: all 0.2s;"><i class="fa fa-times" style="margin-right: 6px;"></i>Cancel</button>
        </div>
    </form>
    <div id="license-status-message" class="message" style="display: none; margin-top: 15px; padding: 12px 15px; border-radius: 4px; font-size: 14px; width: 100%; box-sizing: border-box;"></div>
</div>

<!-- Terms and Conditions Modal -->
<div id="terms-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
    <div style="background-color: var(--surface); border-radius: var(--radius); width: 80%; max-width: 800px; max-height: 80vh; overflow: hidden; padding: 0; position: relative; display: flex; flex-direction: column;">
        <div style="position: sticky; top: 0; z-index: 5; background-color: var(--surface); padding: 20px 20px 10px; border-bottom: 1px solid var(--border); display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 18px; color: var(--text);">Terms and Conditions</h3>
            <button id="close-terms-modal" style="background: none; border: none; font-size: 20px; cursor: pointer; color: var(--text); width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;">×</button>
        </div>
        <div id="terms-content" style="padding: 20px; overflow-y: auto; flex: 1; scrollbar-width: thin; scrollbar-color: var(--border) transparent;">
            <?php include('src/settingsphp/terms.php'); ?>
        </div>
    </div>
</div>

<style>
/* Custom scrollbar styles for the terms modal */
#terms-content::-webkit-scrollbar {
    width: 8px;
}
#terms-content::-webkit-scrollbar-track {
    background: transparent;
}
#terms-content::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 4px;
}
#terms-content::-webkit-scrollbar-thumb:hover {
    background-color: #999;
}
</style>

<hr style="margin: 25px 0; border: 0; border-top: 1px solid #eee;">

<h2><i class="fa fa-cogs"></i> System Status</h2>
<p>Current resource usage and system state.</p>
<dl class="info-grid">
    <dt><i class="fa fa-clock-o"></i> System Time:</dt>
    <dd><?php echo htmlspecialchars($system_time); ?></dd>

    <dt><i class="fa fa-arrow-circle-up"></i> Uptime:</dt>
    <dd><?php echo htmlspecialchars($uptime_str); ?></dd>

    <dt><i class="fa fa-desktop"></i> CPU information:</dt>
    <dd><?php echo htmlspecialchars($cpu_total_str); ?></dd>
</dl>

<hr style="margin: 20px 0; border: 0; border-top: 1px solid var(--border);">

<dl class="info-grid">
    <dt><i class="fa fa-tachometer"></i> CPU Usage:</dt>
    <dd>
        <span class="usage-summary">
            Current: <span id="cpu-usage-value"><i class="fa fa-spinner fa-spin"></i> Loading...</span>
        </span>
        <div class="progress-bar-container" id="cpu-usage-container" style="display:none;">
            <div id="cpu-usage-bar" class="progress-bar" style="width: 0%;" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                <span id="cpu-usage-percent">0%</span>
            </div>
        </div>
    </dd>
</dl>

<hr style="margin: 20px 0; border: 0; border-top: 1px solid var(--border);">

<dl class="info-grid">
    <dt><i class="fa fa-dashboard"></i> Memory:</dt>
    <dd>
        <span class="usage-summary">
            Total: <span id="mem-total"><i class="fa fa-spinner fa-spin"></i> Loading...</span> |
            Used: <span id="mem-used"><i class="fa fa-spinner fa-spin"></i> Loading...</span> |
            Free: <span id="mem-free"><i class="fa fa-spinner fa-spin"></i> Loading...</span>
        </span>
        <div class="progress-bar-container" id="mem-usage-container" style="display:none;">
            <div id="mem-usage-bar" class="progress-bar" style="width: 0%;" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                <span id="mem-usage-percent">0%</span>
            </div>
        </div>
    </dd>
</dl>

<hr style="margin: 20px 0; border: 0; border-top: 1px solid var(--border);">

<dl class="info-grid">
    <dt><i class="fa fa-exchange"></i> Swap:</dt>
    <dd>
        <span class="usage-summary">
            Total: <span id="swap-total"><i class="fa fa-spinner fa-spin"></i> Loading...</span> |
            Used: <span id="swap-used"><i class="fa fa-spinner fa-spin"></i> Loading...</span> |
            Free: <span id="swap-free"><i class="fa fa-spinner fa-spin"></i> Loading...</span>
        </span>
        <div class="progress-bar-container" id="swap-usage-container" style="display:none;">
            <div id="swap-usage-bar" class="progress-bar" style="width: 0%;" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                <span id="swap-usage-percent">0%</span>
            </div>
        </div>
        <span id="swap-not-configured" class="text-muted" style="font-size: 0.9em; display:none;">(Swap not configured)</span>
    </dd>
</dl>

<hr style="margin: 20px 0; border: 0; border-top: 1px solid var(--border);">

<dl class="info-grid">
    <dt><i class="fa fa-hdd-o"></i> Disk Usage (/):</dt>
    <dd>
        <span class="usage-summary">
            Size: <span id="disk-total"><i class="fa fa-spinner fa-spin"></i> Loading...</span> |
            Used: <span id="disk-used"><i class="fa fa-spinner fa-spin"></i> Loading...</span> |
            Free: <span id="disk-free"><i class="fa fa-spinner fa-spin"></i> Loading...</span>
        </span>
        <div class="progress-bar-container" id="disk-usage-container" style="display:none;">
            <div id="disk-usage-bar" class="progress-bar" style="width: 0%;" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                <span id="disk-usage-percent">0%</span>
            </div>
        </div>
    </dd>

    <script>
    let systemStatsInterval = null;
    
    function updateSystemStats() {
        // Check if modal is actually open before making the request
        if (!checkIfInformationTabActive()) {
            return; // Don't update if modal is closed or wrong tab
        }
        
        fetch('src/settingsphp/getSystemStats.php')
            .then(response => response.json())
            .then(data => {
                // Double-check modal state before updating DOM
                if (!checkIfInformationTabActive()) {
                    return; // Don't update if modal closed during request
                }
                
                // Update CPU
                const cpuValue = document.getElementById('cpu-usage-value');
                const cpuPercent = document.getElementById('cpu-usage-percent');
                const cpuBar = document.getElementById('cpu-usage-bar');
                const cpuContainer = document.getElementById('cpu-usage-container');

                if (data.cpu.usage_percent !== 'N/A') {
                    cpuValue.textContent = data.cpu.usage_percent;
                    cpuPercent.textContent = data.cpu.usage_percent;
                    cpuBar.style.width = data.cpu.usage + '%';
                    cpuBar.setAttribute('aria-valuenow', data.cpu.usage);
                    
                    cpuBar.classList.remove('success', 'warning', 'critical');
                    cpuBar.classList.add(data.cpu.threshold_class);
                    
                    cpuContainer.style.display = 'block';
                } else {
                    cpuValue.textContent = 'N/A';
                    cpuContainer.style.display = 'none';
                }

                // Update Memory
                document.getElementById('mem-total').textContent = data.memory.total;
                document.getElementById('mem-used').textContent = data.memory.used_plus_cache;
                document.getElementById('mem-free').textContent = data.memory.free;
                
                const memPercent = document.getElementById('mem-usage-percent');
                const memBar = document.getElementById('mem-usage-bar');
                const memContainer = document.getElementById('mem-usage-container');
                
                memPercent.textContent = data.memory.percent_used_plus_cache + '%';
                memBar.style.width = data.memory.percent_used_plus_cache + '%';
                memBar.setAttribute('aria-valuenow', data.memory.percent_used_plus_cache);
                
                memBar.classList.remove('success', 'warning', 'critical');
                memBar.classList.add(data.memory.threshold_class);
                
                memContainer.style.display = 'block';

                // Update Swap
                document.getElementById('swap-total').textContent = data.swap.total;
                document.getElementById('swap-used').textContent = data.swap.used;
                document.getElementById('swap-free').textContent = data.swap.free;
                
                const swapPercent = document.getElementById('swap-usage-percent');
                const swapBar = document.getElementById('swap-usage-bar');
                const swapContainer = document.getElementById('swap-usage-container');
                const swapNotConfigured = document.getElementById('swap-not-configured');
                
                if (data.swap.configured) {
                    swapPercent.textContent = data.swap.percent_used + '%';
                    swapBar.style.width = data.swap.percent_used + '%';
                    swapBar.setAttribute('aria-valuenow', data.swap.percent_used);
                    
                    swapBar.classList.remove('success', 'warning', 'critical');
                    swapBar.classList.add(data.swap.threshold_class);
                    
                    swapContainer.style.display = 'block';
                    swapNotConfigured.style.display = 'none';
                } else {
                    swapContainer.style.display = 'none';
                    swapNotConfigured.style.display = 'inline';
                }

                // Update Disk
                document.getElementById('disk-total').textContent = data.disk.total;
                document.getElementById('disk-used').textContent = data.disk.used;
                document.getElementById('disk-free').textContent = data.disk.free;
                
                const diskPercent = document.getElementById('disk-usage-percent');
                const diskBar = document.getElementById('disk-usage-bar');
                const diskContainer = document.getElementById('disk-usage-container');
                
                diskPercent.textContent = data.disk.percent_used + '%';
                diskBar.style.width = data.disk.percent_used + '%';
                diskBar.setAttribute('aria-valuenow', data.disk.percent_used);
                
                diskBar.classList.remove('success', 'warning', 'critical');
                diskBar.classList.add(data.disk.threshold_class);
                
                diskContainer.style.display = 'block';
            })
            .catch(error => console.error('Error fetching system stats:', error));
    }
    
    function startSystemStatsUpdates() {
        // Clear any existing interval
        if (systemStatsInterval) {
            clearInterval(systemStatsInterval);
        }
        // Start updates immediately and every 5 seconds
        updateSystemStats();
        systemStatsInterval = setInterval(updateSystemStats, 5000);
    }
    
    function stopSystemStatsUpdates() {
        if (systemStatsInterval) {
            clearInterval(systemStatsInterval);
            systemStatsInterval = null;
        }
    }
    
    function checkIfInformationTabActive() {
        const settingsModal = document.getElementById('settingsModal');
        const informationTab = document.querySelector('.sub-tab-link[data-target="general-system-information"]');
        const informationPane = document.getElementById('general-system-information');
        
        // Check if settings modal is open AND information tab is active
        return settingsModal && settingsModal.classList.contains('show') && 
               informationTab && informationTab.classList.contains('active') && 
               informationPane && informationPane.classList.contains('active');
    }
    
    // Start updates when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        // Only start updates if the settings modal is open AND information tab is active
        if (checkIfInformationTabActive()) {
            startSystemStatsUpdates();
        }
        
        // Listen for tab changes
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('sub-tab-link')) {
                const targetId = e.target.getAttribute('data-target');
                
                if (targetId === 'general-system-information') {
                    // Starting to view the information tab
                    startSystemStatsUpdates();
                } else {
                    // Switching away from information tab
                    stopSystemStatsUpdates();
                }
            }
        });
        
        // Listen for settings modal open/close
        const openSettingsBtn = document.getElementById('openSettingsBtn');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const settingsModal = document.getElementById('settingsModal');
        
        if (openSettingsBtn) {
            openSettingsBtn.addEventListener('click', () => {
                // Modal is opening, check if information tab is active
                setTimeout(() => {
                    if (checkIfInformationTabActive()) {
                        startSystemStatsUpdates();
                    }
                }, 100); // Small delay to ensure modal is fully opened
            });
        }
        
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', () => {
                // Modal is closing, stop updates
                stopSystemStatsUpdates();
            });
        }
        
        if (settingsModal) {
            // Also listen for clicks outside modal to close
            settingsModal.addEventListener('click', (event) => {
                if (event.target === settingsModal) {
                    stopSystemStatsUpdates();
                }
            });
        }
        
        // License renewal form functionality
        const renewBtn = document.getElementById('renew-license-btn');
        const licenseForm = document.getElementById('renew-license-form');
        const cancelBtn = document.getElementById('cancel-renew-btn');
        const licenseFormActual = document.getElementById('license-form-actual');
        const agreeCheckbox = document.getElementById('agree-terms');
        
        // Terms modal functionality
        const openTermsBtn = document.getElementById('open-terms-modal');
        const termsModal = document.getElementById('terms-modal');
        const closeTermsBtn = document.getElementById('close-terms-modal');
        
        if (renewBtn) {
            renewBtn.addEventListener('click', () => {
                licenseForm.style.display = 'block';
            });
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                licenseForm.style.display = 'none';
            });
        }
        
        if (licenseFormActual) {
            licenseFormActual.addEventListener('submit', (e) => {
                if (!agreeCheckbox.checked) {
                    e.preventDefault();
                    alert('You must agree to the terms and conditions to proceed.');
                }
            });
        }
        
        if (openTermsBtn) {
            openTermsBtn.addEventListener('click', (e) => {
                e.preventDefault();
                termsModal.style.display = 'flex';
            });
        }
        
        if (closeTermsBtn) {
            closeTermsBtn.addEventListener('click', () => {
                termsModal.style.display = 'none';
            });
        }
        
        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === termsModal) {
                termsModal.style.display = 'none';
            }
        });
    });
    </script>


</dl>

<?php
// --- End of Fragment Output ---
?>