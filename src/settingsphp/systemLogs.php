<?php
// src/settingsphp/systemLogs.php

session_start();

// Define available log files (hardcoded list)
$all_log_options = [
    '/var/log/arpwatch2.log' => 'Arpwatch',
    '/var/log/blesk.log' => 'Blesk',
    '/var/log/nagios/nagios.log' => 'APM',
    '/var/log/apm_import.log' => 'APM Import',
    '/var/log/cron' => 'Cron',
    '/var/log/httpd/ssl_error_log' => 'Httpd (Apache)',
    '/var/log/maillog' => 'Maillog',
    '/var/log/mysqld.log' => 'Mysql (Blesk5)',
    '/var/log/mysql/mysqld.log' => 'Mysql (Blesk8)',
    '/var/log/messages' => 'Messages',
    '/var/log/pnp4nagios/npcd.log' => 'Npcd',
    '/var/log/yum.log' => 'Updates (Yum)',
    '/var/log/redis/redis.log' => 'Redis',
    '/home/<USER>/logs/netdisco-backend.log' => 'SPM',
    '/var/log/netdisco_sync.log' => 'SPM Sync',
    '/var/log/netdisco_parent_child_sync.log' => 'SPM Parent-Child Sync',
    '/var/log/elasticsearch/elasticsearch.log' => 'ELM (Elastic)',
    '/var/log/logstash/logstash.log' => 'ELM (Logstash)',
    '/var/log/fusioninventory/fusioninventory.log' => 'ALCM (GLPI)',
];

// Filter to only include log files that exist and are readable
$log_options = [];
foreach ($all_log_options as $file_path => $label) {
    $current_logfile = $file_path;
    
    // Handle special case for elasticsearch dynamically
    if ($file_path == '/var/log/elasticsearch/elasticsearch.log' && !file_exists($file_path)) {
        $current_logfile = '/var/log/elasticsearch/elastic.log';
    }
    
    if (file_exists($current_logfile) && is_readable($current_logfile)) {
        $log_options[$file_path] = $label;
    }
}

// Log file selection is removed. We will fetch from all files.

// Number of lines to show
$lines_to_show = 25; // Default
if (!empty($_POST['lines_to_show'])) {
    // Add 200 to the options and update max_range
    $lines = filter_var($_POST['lines_to_show'], FILTER_VALIDATE_INT, ['options' => ['min_range' => 1, 'max_range' => 200]]);
    $lines_to_show = $lines !== false ? $lines : 25;
}

// Selected log file
$selected_log_file = $_POST['selected_log_file'] ?? '';

// Fetch log contents
$log_contents = '';

// If a specific log file is selected, only fetch from that file
if (!empty($selected_log_file) && isset($log_options[$selected_log_file])) {
    $file_path = $selected_log_file;
    $label = $log_options[$file_path];
    $current_logfile = $file_path;

    // Handle special case for elasticsearch dynamically
    if ($file_path == '/var/log/elasticsearch/elasticsearch.log' && !file_exists($file_path)) {
        $current_logfile = '/var/log/elasticsearch/elastic.log';
    }

    // Check if file exists and is readable
    if (file_exists($current_logfile) && is_readable($current_logfile)) {
        $file_lines = file($current_logfile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        // Check if reading was successful and file is not empty
        if ($file_lines !== false && !empty($file_lines)) {
            $lines = array_slice(array_reverse($file_lines), 0, $lines_to_show);

            // Check if after slicing we still have lines
            if (!empty($lines)) {
                $log_contents .= "<span class='log-header'>--- Logs for: " . htmlspecialchars($label) . " ---</span>\n";
                $log_contents .= implode("\n", array_reverse($lines)) . "\n\n";
            }
        }
    }
} else {
    // Fetch from all files (when "All Log Files" is selected)
    foreach ($log_options as $file_path => $label) {
        $current_logfile = $file_path;

        // Handle special case for elasticsearch dynamically
        if ($file_path == '/var/log/elasticsearch/elasticsearch.log' && !file_exists($file_path)) {
            $current_logfile = '/var/log/elasticsearch/elastic.log';
        } else {
            $current_logfile = $file_path;
        }

        // Check if file exists and is readable
        if (file_exists($current_logfile) && is_readable($current_logfile)) {
            $file_lines = file($current_logfile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

            // Check if reading was successful and file is not empty
            if ($file_lines !== false && !empty($file_lines)) {
                $lines = array_slice(array_reverse($file_lines), 0, $lines_to_show);

                // Check if after slicing we still have lines
                if (!empty($lines)) {
                    $log_contents .= "<span class='log-header'>--- Logs for: " . htmlspecialchars($label) . " ---</span>\n";
                    $log_contents .= implode("\n", array_reverse($lines)) . "\n\n";
                }
            }
        }
    }
}
// Trim trailing newlines that might accumulate if the last file(s) were skipped
$log_contents = rtrim($log_contents);

// Store data in session for the view
$_SESSION['lines_to_show'] = $lines_to_show;
$_SESSION['selected_log_file'] = $selected_log_file;
$_SESSION['log_contents'] = $log_contents;
$_SESSION['log_options'] = $log_options;

// Check if this is an AJAX request
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

if ($isAjax) {
    // For AJAX requests, include the content and exit
    include 'systemLogsContent.php';
    exit;
} else {
    // For initial load, include the content (this will be handled by the modal's fetch)
    include 'systemLogsContent.php';
}
?>