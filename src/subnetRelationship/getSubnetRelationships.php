<?php
// src/subnetRelationship/getSubnetRelationships.php

include "../../loadenv.php";

// Check if SPM module is available
$spm_check = shell_exec("php " . __DIR__ . "/../../checkmods.php spm 2>&1");
$spm_available = (trim($spm_check) === "true");

// Database connections
$db_host = $_ENV["DB_SERVER"];
$db_user = $_ENV["DB_USER"];
$db_pass = $_ENV["DB_PASSWORD"];
$bubblemaps_db = 'bubblemaps';
$nagiosql_db = 'db_nagiosql_v3';

// Prevent caching
header("Cache-Control: no-cache, must-revalidate");
header("Content-Type: application/json");

try {
    $relationships = [];
    $processedPairs = []; // Track processed subnet pairs to avoid duplicates
    
    // Connect to bubblemaps database to get valid subnet data
    $bubblemaps_conn = new mysqli($db_host, $db_user, $db_pass, $bubblemaps_db);
    if ($bubblemaps_conn->connect_error) {
        throw new Exception("Bubblemaps DB connection failed: " . $bubblemaps_conn->connect_error);
    }
    
    // Get all valid subnets from bubblemaps
    $validSubnets = [];
    $result = $bubblemaps_conn->query("SELECT DISTINCT subnet FROM subnets WHERE infra = (SELECT name FROM infra LIMIT 1)");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $validSubnets[] = $row['subnet'];
        }
    }
    
    if (empty($validSubnets)) {
        echo json_encode($relationships);
        exit;
    }
    
    // 1. FIRST: Query Netdisco database directly (if SPM is available)
    if ($spm_available) {
        $netdiscoRelationships = queryNetdiscoSubnetRelationships($validSubnets, $bubblemaps_conn);
        
        foreach ($netdiscoRelationships as $rel) {
            $parentSubnet = $rel['parent_subnet'];
            $childSubnet = $rel['child_subnet'];
            $connectionType = $rel['connection_type'];
            $hostCount = $rel['host_count'];
            
            $pairKey = $parentSubnet . '|' . $childSubnet;
            if (!isset($processedPairs[$pairKey])) {
                $relationships[] = [
                    'parent_subnet' => $parentSubnet,
                    'child_subnet' => $childSubnet,
                    'parent_subnet_nickname' => $rel['parent_subnet_nickname'],
                    'child_subnet_nickname' => $rel['child_subnet_nickname'],
                    'source' => 'netdisco',
                    'connection_type' => $connectionType,
                    'host_count' => $hostCount
                ];
                $processedPairs[$pairKey] = true;
            }
        }
    }
    
    // 2. SECOND: Query NagiosQL database and complement/merge
    $nagiosql_conn = new mysqli($db_host, $db_user, $db_pass, $nagiosql_db);
    if ($nagiosql_conn->connect_error) {
        throw new Exception("NagiosQL DB connection failed: " . $nagiosql_conn->connect_error);
    }
    
    // Get subnet relationships based on host relationships
    $sql = "SELECT
                h1.subnet AS parent_subnet,
                h2.subnet AS child_subnet,
                COUNT(*) as host_count
            FROM
                tbl_lnkHostToHost l
            JOIN
                tbl_host p ON l.idSlave = p.id
            JOIN
                tbl_host c ON l.idMaster = c.id
            JOIN
                bubblemaps.hosts h1 ON p.address = h1.ip
            JOIN
                bubblemaps.hosts h2 ON c.address = h2.ip
            WHERE
                h1.subnet != h2.subnet
                AND h1.blacklist = 0
                AND h2.blacklist = 0
                AND h1.apmStatus != 'not-added'
                AND h1.apmStatus != 'ask'
                AND h2.apmStatus != 'not-added'
                AND h2.apmStatus != 'ask'
            GROUP BY
                h1.subnet, h2.subnet
            HAVING
                COUNT(*) > 0";
    
    $result = $nagiosql_conn->query($sql);
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $parentSubnet = $row['parent_subnet'];
            $childSubnet = $row['child_subnet'];
            $hostCount = intval($row['host_count']);
            
            $pairKey = $parentSubnet . '|' . $childSubnet;
            if (!isset($processedPairs[$pairKey])) {
                // Get subnet nicknames
                $parentNickname = getSubnetNickname($parentSubnet, $bubblemaps_conn);
                $childNickname = getSubnetNickname($childSubnet, $bubblemaps_conn);
                
                $relationships[] = [
                    'parent_subnet' => $parentSubnet,
                    'child_subnet' => $childSubnet,
                    'parent_subnet_nickname' => $parentNickname,
                    'child_subnet_nickname' => $childNickname,
                    'source' => 'nagiosql',
                    'connection_type' => 'Manual',
                    'host_count' => $hostCount
                ];
                $processedPairs[$pairKey] = true;
            } else {
                // Update existing relationship to mark it as "both"
                foreach ($relationships as &$rel) {
                    if ($rel['parent_subnet'] === $parentSubnet && $rel['child_subnet'] === $childSubnet) {
                        $rel['source'] = 'both';
                        break;
                    }
                }
            }
        }
    }
    
    $nagiosql_conn->close();
    $bubblemaps_conn->close();
    
    echo json_encode($relationships);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * Query Netdisco for subnet relationships based on device connections
 */
function queryNetdiscoSubnetRelationships($validSubnets, $bubblemaps_conn) {
    $relationships = [];
    
    // Create a mapping of IP to subnet for quick lookup
    $ipToSubnet = [];
    $result = $bubblemaps_conn->query("SELECT ip, subnet FROM hosts WHERE blacklist = 0 AND apmStatus != 'not-added' AND apmStatus != 'ask'");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $ipToSubnet[$row['ip']] = $row['subnet'];
        }
    }
    
    // Filter valid IPs for Netdisco query
    $validIps = array_keys($ipToSubnet);
    $validIps = array_filter($validIps, function($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP);
    });
    
    if (empty($validIps)) {
        return $relationships;
    }
    
    // Create IN clause for all valid IPs
    $ipList = "'" . implode("','", $validIps) . "'";
    
    // Get device connections from Netdisco for valid IPs only
    $sql = <<<SQL
SELECT
    dp.ip AS parent_ip,
    dp.port AS port,
    COALESCE(
        (SELECT string_agg(ip::text, ', ') 
         FROM node_ip 
         WHERE mac = n.mac AND active = true
         GROUP BY mac),
        dp.remote_ip::text
    ) AS child_ip,
    CASE
        WHEN dp.remote_ip IS NOT NULL THEN 'Uplink'
        WHEN n.mac IS NOT NULL THEN 'Device'
        ELSE 'Unknown'
    END AS connection_type
FROM 
    device_port dp
LEFT JOIN 
    node n ON dp.ip = n.switch AND dp.port = n.port AND n.time_last > NOW() - INTERVAL '2 hours'
WHERE
    dp.up = 'up'
    AND dp.ip IN ({$ipList})
    AND (
        -- Include recent device connections (nodes with MAC addresses)
        (n.mac IS NOT NULL AND EXISTS (
            SELECT 1 FROM node_ip 
            WHERE mac = n.mac AND active = true
        ))
        -- Include uplinks (remote_ip connections, no time restriction needed)
        OR dp.remote_ip IS NOT NULL
    )
ORDER BY 
    dp.ip, dp.port;
SQL;

    // Execute the query via psql
    $command = "sudo -u netdisco /usr/bin/psql -U netdisco -d netdisco -t -A -F'|' -c " . escapeshellarg($sql);
    $output = shell_exec($command . " 2>&1");
    
    if ($output === null) {
        return $relationships;
    }
    
    $lines = explode("\n", trim($output));
    $subnetConnections = [];
    
    foreach ($lines as $line) {
        if (empty(trim($line))) continue;
        
        $parts = explode('|', $line);
        if (count($parts) < 4) continue;
        
        $parentIp = trim($parts[0]);
        $childIpRaw = trim($parts[2]);
        $connectionType = trim($parts[3]);
        
        // Handle multiple IPs separated by commas (from string_agg)
        $childIps = array_map('trim', explode(',', $childIpRaw));
        
        foreach ($childIps as $childIp) {
            // Remove CIDR notation (e.g., /32, /24) from IP addresses
            $childIp = preg_replace('/\/\d+$/', '', $childIp);
            
            // Validate IP addresses and check if both IPs exist in our subnet mapping
            if (filter_var($parentIp, FILTER_VALIDATE_IP) && 
                filter_var($childIp, FILTER_VALIDATE_IP) &&
                $parentIp !== $childIp &&
                isset($ipToSubnet[$parentIp]) && isset($ipToSubnet[$childIp])) {
                
                $parentSubnet = $ipToSubnet[$parentIp];
                $childSubnet = $ipToSubnet[$childIp];
                
                // Only process if subnets are different
                if ($parentSubnet !== $childSubnet) {
                    $key = $parentSubnet . '|' . $childSubnet;
                    if (!isset($subnetConnections[$key])) {
                        $subnetConnections[$key] = [
                            'parent_subnet' => $parentSubnet,
                            'child_subnet' => $childSubnet,
                            'connection_type' => $connectionType,
                            'host_count' => 0
                        ];
                    }
                    $subnetConnections[$key]['host_count']++;
                }
            }
        }
    }
    
    // Convert to final format and get nicknames
    foreach ($subnetConnections as $connection) {
        $parentNickname = getSubnetNickname($connection['parent_subnet'], $bubblemaps_conn);
        $childNickname = getSubnetNickname($connection['child_subnet'], $bubblemaps_conn);
        
        $relationships[] = [
            'parent_subnet' => $connection['parent_subnet'],
            'child_subnet' => $connection['child_subnet'],
            'parent_subnet_nickname' => $parentNickname,
            'child_subnet_nickname' => $childNickname,
            'source' => 'netdisco',
            'connection_type' => $connection['connection_type'],
            'host_count' => $connection['host_count']
        ];
    }
    
    return $relationships;
}

/**
 * Get subnet nickname from database
 */
function getSubnetNickname($subnet, $conn) {
    $stmt = $conn->prepare("SELECT subnetNickname FROM subnets WHERE subnet = ? LIMIT 1");
    $stmt->bind_param("s", $subnet);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        return $row['subnetNickname'];
    }
    
    return $subnet; // Fallback to subnet name if nickname not found
}
?>
