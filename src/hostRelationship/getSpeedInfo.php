<?php
// src/hostRelationship/getSpeedInfo.php

include "../../loadenv.php";

/**
 * Get user credentials for Nagios API
 */
function getUserCredentials() {
    try {
        $db_host = $_ENV["DB_SERVER"];
        $db_user = $_ENV["DB_USER"];
        $db_pass = $_ENV["DB_PASSWORD"];
        $bubblemaps_db = 'blesk';
        
        $conn = new mysqli($db_host, $db_user, $db_pass, $bubblemaps_db);
        if ($conn->connect_error) {
            return null;
        }
        
        $result = $conn->query("SELECT username, password FROM users WHERE user_id = 1 LIMIT 1");
        if (!$result) {
            $conn->close();
            return null;
        }
        
        if ($result->num_rows === 0) {
            $conn->close();
            return null;
        }
        
        $row = $result->fetch_assoc();
        $conn->close();
        
        if (!$row || empty($row['username']) || empty($row['password'])) {
            return null;
        }
        return [
            'tfUsername' => $row['username'],
            'tfPassword' => $row['password']
        ];
    } catch (Exception $e) {
        error_log("Exception in getUserCredentials: " . $e->getMessage());
        return null;
    }
}

/**
 * Get hostname by IP from Nagios
 */
function getHostnameByIP($ip) {
    try {
        $credentials = getUserCredentials();
        if (!$credentials) {
            error_log("No credentials found for getHostnameByIP");
            return null;
        }
        
        $nagiosUrl = "https://" . $_SERVER['HTTP_HOST'] . "/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $nagiosUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        if ($curlError) {
            return null;
        }
        
        if ($httpCode !== 200 || !$response) {
            return null;
        }
        
        $data = json_decode($response, true);
        if (!$data || !isset($data['data']['hostlist'])) {
            return null;
        }
        
        foreach ($data['data']['hostlist'] as $hostname => $hostData) {
            if (isset($hostData['address']) && $hostData['address'] === $ip) {
                return $hostname;
            }
        }
        return null;
    } catch (Exception $e) {
        error_log("Exception in getHostnameByIP: " . $e->getMessage());
        return null;
    }
}

// Check if SPM module is available (like in get_spm_data.php)
$spm_check = shell_exec("php " . __DIR__ . "/../../checkmods.php spm 2>&1");
if (trim($spm_check) !== "true") {
    http_response_code(500);
    echo json_encode(['error' => 'SPM module is not available']);
    exit;
}

// Prevent caching
header("Cache-Control: no-cache, must-revalidate");
header("Content-Type: application/json");

try {
    // Get parent and child IPs from request
    $parentIp = $_GET['parent_ip'] ?? '';
    $childIp = $_GET['child_ip'] ?? '';
    
    if (empty($parentIp) || empty($childIp)) {
        http_response_code(400);
        echo json_encode(['error' => 'Parent IP and Child IP are required']);
        exit;
    }
    
    // Build the Netdisco query to get speed information for all matching ports
    // Use a simpler approach that matches the logic in get_spm_data.php
    $sql = "SELECT dp.ip AS parent_ip, dp.port AS port_number, dp.speed AS port_speed FROM device_port dp LEFT JOIN node n ON dp.ip = n.switch AND dp.port = n.port AND n.time_last > NOW() - INTERVAL '2 hours' WHERE dp.ip = '$parentIp' AND (dp.remote_ip = '$childIp' OR EXISTS (SELECT 1 FROM node_ip WHERE mac = n.mac AND active = true AND ip = '$childIp')) ORDER BY dp.port";
    
    // Execute the query via psql with proper formatting options
    // Use the sudoers configuration: apache ALL=(netdisco) NOPASSWD: /usr/bin/psql -U netdisco -d netdisco *
    $command = "sudo -u netdisco /usr/bin/psql -U netdisco -d netdisco -t -A -F'|' -c " . escapeshellarg($sql);
    
    $output = shell_exec($command . ' 2>&1');
    
    if ($output === null) {
        throw new Exception('Failed to execute database query');
    }
    
    // Parse the output
    $lines = array_filter(explode("\n", trim($output)));
    
    $ports = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '') continue;
        
        // Parse the pipe-separated line
        $parts = explode('|', $line);
        
        if (count($parts) >= 3) {
            $ports[] = [
                'port_number' => trim($parts[1]),
                'port_speed' => trim($parts[2])
            ];
        }
    }
    
    if (!empty($ports)) {
        // Get traffic information from Nagios for each port
        $portsWithTraffic = [];
        foreach ($ports as $port) {
            $portWithTraffic = $port;
            try {
                $portWithTraffic['traffic'] = getPortTrafficFromNagios($parentIp, $port['port_number']);
            } catch (Exception $e) {
                error_log("Error getting traffic for port {$port['port_number']}: " . $e->getMessage());
                $portWithTraffic['traffic'] = null;
            }
            $portsWithTraffic[] = $portWithTraffic;
        }
        
        $speedInfo = [
            'parent_ip' => $parentIp,
            'child_ip' => $childIp,
            'ports' => $portsWithTraffic
        ];
        echo json_encode($speedInfo);
    } else {
        // Add debugging information
        echo json_encode([
            'error' => 'No speed information found', 
            'parent_ip' => $parentIp, 
            'child_ip' => $childIp,
            'query' => $sql,
            'output_lines' => count($lines)
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error in getSpeedInfo.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Database query failed: ' . $e->getMessage()]);
}

/**
 * Get port traffic information from Nagios API
 */
function getPortTrafficFromNagios($parentIp, $portNumber) {
    try {
        // Get the real hostname from Nagios
        $hostname = getHostnameByIP($parentIp);
        if (!$hostname) {
        return null;
        }
        
        // Get credentials for Nagios API
        $credentials = getUserCredentials();
        if (!$credentials) {
            return null;
        }
        
        // Build Nagios API URL
        $nagiosUrl = "https://" . $_SERVER['HTTP_HOST'] . "/nagios/cgi-bin/statusjson.cgi?query=servicelist&details=true&hostname=" . urlencode($hostname);
        
        // Initialize cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $nagiosUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        if ($curlError) {
            return null;
        }
        
        if ($httpCode !== 200 || !$response) {
            return null;
        }
        
        $data = json_decode($response, true);
        if (!$data || !isset($data['data']['servicelist'][$hostname])) {
            return null;
        }
        
        $services = $data['data']['servicelist'][$hostname];
        
        // Find matching service by fuzzy matching port number
        $matchingService = findMatchingPortService($services, $portNumber);
        if (!$matchingService) {
            return null;
        }
        
        // Parse traffic data from plugin output
        return parseTrafficData($matchingService['plugin_output'], $matchingService['perf_data']);
        
    } catch (Exception $e) {
        error_log("Error getting port traffic from Nagios: " . $e->getMessage());
        return null;
    }
}

/**
 * Find service that matches the port number using fuzzy matching
 */
function findMatchingPortService($services, $portNumber) {
    // Convert port number to various formats for matching
    $portFormats = [
        $portNumber,
        str_replace('/', '-', $portNumber), // 1/0/1 -> 1-0-1
        str_replace('/', '_', $portNumber), // 1/0/1 -> 1_0_1
        'port' . $portNumber,               // port1/0/1
        'port' . str_replace('/', '', $portNumber), // port101
    ];
    
    foreach ($services as $serviceName => $serviceData) {
        $description = strtolower($serviceData['description']);
        
        // Check if any port format matches the service description
        foreach ($portFormats as $format) {
            if (strpos($description, strtolower($format)) !== false) {
                return $serviceData;
            }
        }
        
        // Also check for partial matches (e.g., "1/0/1" matches "port1-Alfonso_Test")
        $portParts = explode('/', $portNumber);
        if (count($portParts) >= 2) {
            $lastPart = end($portParts);
            if (strpos($description, $lastPart) !== false) {
                return $serviceData;
            }
        }
        
        // Check if service name contains port number
        if (strpos(strtolower($serviceName), strtolower($portNumber)) !== false) {
            return $serviceData;
        }
    }
    
    return null;
}

/**
 * Parse traffic data from plugin output and performance data
 */
function parseTrafficData($pluginOutput, $perfData) {
    $traffic = [
        'current_in' => null,
        'current_out' => null
    ];
    
    // Parse ONLY plugin output for current traffic
    // Example: "OK - Average IN: 466.48KB (0.37%), Average OUT: 374.18KB (0.30%)Total RX: 1535.79GBytes, Total TX: 1412.59GBytes"
    if (preg_match('/Average IN:\s*([0-9.]+[KMGT]?[Bb])/', $pluginOutput, $matches)) {
        $traffic['current_in'] = $matches[1];
    }
    
    if (preg_match('/Average OUT:\s*([0-9.]+[KMGT]?[Bb])/', $pluginOutput, $matches)) {
        $traffic['current_out'] = $matches[1];
    }
    
    return $traffic;
}

?>
