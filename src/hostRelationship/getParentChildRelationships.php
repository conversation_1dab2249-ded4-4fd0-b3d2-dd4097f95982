<?php
// src/hostRelationship/getParentChildRelationships.php

include "../../loadenv.php";

// Check if SPM module is available
$spm_check = shell_exec("php " . __DIR__ . "/../../checkmods.php spm 2>&1");
$spm_available = (trim($spm_check) === "true");

// Database connections
$db_host = $_ENV["DB_SERVER"];
$db_user = $_ENV["DB_USER"];
$db_pass = $_ENV["DB_PASSWORD"];
$bubblemaps_db = 'bubblemaps';
$nagiosql_db = 'db_nagiosql_v3';

// Prevent caching
header("Cache-Control: no-cache, must-revalidate");
header("Content-Type: application/json");

try {
    $relationships = [];
    $processedPairs = []; // Track processed parent-child pairs to avoid duplicates
    
    // Connect to bubblemaps database to get valid host IPs
    $bubblemaps_conn = new mysqli($db_host, $db_user, $db_pass, $bubblemaps_db);
    if ($bubblemaps_conn->connect_error) {
        throw new Exception("Bubblemaps DB connection failed: " . $bubblemaps_conn->connect_error);
    }
    
    // Get all valid host IPs from bubblemaps
    $validIps = [];
    $result = $bubblemaps_conn->query("SELECT DISTINCT ip FROM hosts WHERE blacklist = 0 AND apmStatus != 'not-added' AND apmStatus != 'ask'");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $validIps[] = $row['ip'];
        }
    }
    $bubblemaps_conn->close();
    
    if (empty($validIps)) {
        echo json_encode($relationships);
        exit;
    }
    
    // 1. FIRST: Query Netdisco database directly (if SPM is available)
    if ($spm_available) {
        $netdiscoRelationships = queryNetdiscoRelationships($validIps);
        
        foreach ($netdiscoRelationships as $rel) {
            $parentIp = $rel['parent_ip'];
            $childIp = $rel['child_ip'];
            $connectionType = $rel['connection_type'];
            
            $pairKey = $parentIp . '|' . $childIp;
            if (!isset($processedPairs[$pairKey])) {
                $relationships[] = [
                    'parent_ip' => $parentIp,
                    'child_ip' => $childIp,
                    'parent_hostname' => null, // Will be filled from NagiosQL
                    'child_hostname' => null,  // Will be filled from NagiosQL
                    'source' => 'netdisco',
                    'connection_type' => $connectionType
                ];
                $processedPairs[$pairKey] = true;
            }
        }
    }
    
    // 2. SECOND: Query NagiosQL database and complement/merge
    $nagiosql_conn = new mysqli($db_host, $db_user, $db_pass, $nagiosql_db);
    if ($nagiosql_conn->connect_error) {
        throw new Exception("NagiosQL DB connection failed: " . $nagiosql_conn->connect_error);
    }
    
    $sql = "SELECT
                p.address AS parent_ip,
                p.host_name AS parent_hostname,
                c.address AS child_ip,
                c.host_name AS child_hostname
            FROM
                tbl_lnkHostToHost l
            JOIN
                tbl_host p ON l.idSlave = p.id
            JOIN
                tbl_host c ON l.idMaster = c.id";
    
    $result = $nagiosql_conn->query($sql);
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $parentIp = $row['parent_ip'];
            $childIp = $row['child_ip'];
            $pairKey = $parentIp . '|' . $childIp;
            
            if (!isset($processedPairs[$pairKey])) {
                // New relationship from NagiosQL
                $relationships[] = [
                    'parent_ip' => $parentIp,
                    'child_ip' => $childIp,
                    'parent_hostname' => $row['parent_hostname'],
                    'child_hostname' => $row['child_hostname'],
                    'source' => 'nagiosql',
                    'connection_type' => 'Manual'
                ];
                $processedPairs[$pairKey] = true;
            } else {
                // Update existing relationship with hostname info
                foreach ($relationships as &$rel) {
                    if ($rel['parent_ip'] === $parentIp && $rel['child_ip'] === $childIp) {
                        $rel['parent_hostname'] = $row['parent_hostname'];
                        $rel['child_hostname'] = $row['child_hostname'];
                        $rel['source'] = 'both'; // Present in both databases
                        break;
                    }
                }
            }
        }
    }
    
    $nagiosql_conn->close();
    
    echo json_encode($relationships);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * Query Netdisco database for parent-child relationships
 */
function queryNetdiscoRelationships($validIps) {
    $relationships = [];
    
    // Filter valid IPs
    $validIps = array_filter($validIps, function($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP);
    });
    
    if (empty($validIps)) {
        return $relationships;
    }
    
    try {
        $relationships = queryNetdiscoForAllSwitches($validIps);
    } catch (Exception $e) {
        error_log("Error querying Netdisco: " . $e->getMessage());
    }
    
    return $relationships;
}

/**
 * Query Netdisco for all switches at once
 */
function queryNetdiscoForAllSwitches($validIps) {
    // Create IN clause for all valid IPs
    $ipList = "'" . implode("','", $validIps) . "'";
    
    $sql = <<<SQL
SELECT
    dp.ip AS parent_ip,
    dp.port AS port,
    COALESCE(
        (SELECT string_agg(ip::text, ', ') 
         FROM node_ip 
         WHERE mac = n.mac AND active = true
         GROUP BY mac),
        dp.remote_ip::text
    ) AS child_ip,
    CASE
        WHEN dp.remote_ip IS NOT NULL THEN 'Uplink'
        WHEN n.mac IS NOT NULL THEN 'Device'
        ELSE 'Unknown'
    END AS connection_type
FROM 
    device_port dp
LEFT JOIN 
    node n ON dp.ip = n.switch AND dp.port = n.port AND n.time_last > NOW() - INTERVAL '2 hours'
WHERE
    dp.up = 'up'
    AND dp.ip IN ({$ipList})
    AND (
        -- Include recent device connections (nodes with MAC addresses)
        (n.mac IS NOT NULL AND EXISTS (
            SELECT 1 FROM node_ip 
            WHERE mac = n.mac AND active = true
        ))
        -- Include uplinks (remote_ip connections, no time restriction needed)
        OR dp.remote_ip IS NOT NULL
    )
ORDER BY 
    dp.ip, dp.port;
SQL;

    // Execute the query via psql
    $command = "sudo -u netdisco /usr/bin/psql -U netdisco -d netdisco -t -A -F'|' -c " . escapeshellarg($sql);
    $output = shell_exec($command . ' 2>&1');
    
    if ($output === null) {
        throw new Exception("Failed to execute netdisco query");
    }
    
    // Parse the output
    $lines = array_filter(explode("\n", trim($output)));
    $results = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '') continue;
        
        // Expected order: parent_ip|port|child_ip|connection_type
        $parts = explode('|', $line);
        
        if (count($parts) >= 4) {
            $parentIp = trim($parts[0]);
            $port = trim($parts[1]);
            $childIpRaw = trim($parts[2]);
            $connectionType = trim($parts[3]);
            
            // Handle multiple IPs separated by commas (from string_agg)
            $childIps = array_map('trim', explode(',', $childIpRaw));
            
            foreach ($childIps as $childIp) {
                // Remove CIDR notation (e.g., /32, /24) from IP addresses
                $childIp = preg_replace('/\/\d+$/', '', $childIp);
                
                // Validate IP addresses
                if (filter_var($parentIp, FILTER_VALIDATE_IP) && 
                    filter_var($childIp, FILTER_VALIDATE_IP) &&
                    $parentIp !== $childIp) {
                    
                    $results[] = [
                        'parent_ip' => $parentIp,
                        'child_ip' => $childIp,
                        'connection_type' => $connectionType
                    ];
                }
            }
        }
    }
    
    return $results;
}

/**
 * Query Netdisco for a specific switch (same as working sync script)
 */
function queryNetdiscoForSwitch($switchIp) {
    $sql = <<<SQL
SELECT
    dp.ip AS parent_ip,
    dp.port AS port,
    COALESCE(
        (SELECT string_agg(ip::text, ', ') 
         FROM node_ip 
         WHERE mac = n.mac AND active = true
         GROUP BY mac),
        dp.remote_ip::text
    ) AS child_ip,
    CASE
        WHEN dp.remote_ip IS NOT NULL THEN 'Uplink'
        WHEN n.mac IS NOT NULL THEN 'Device'
        ELSE 'Unknown'
    END AS connection_type
FROM 
    device_port dp
LEFT JOIN 
    node n ON dp.ip = n.switch AND dp.port = n.port AND n.time_last > NOW() - INTERVAL '2 hours'
WHERE
    dp.up = 'up'
    AND dp.ip = '{$switchIp}'
    AND (
        -- Include recent device connections (nodes with MAC addresses)
        (n.mac IS NOT NULL AND EXISTS (
            SELECT 1 FROM node_ip 
            WHERE mac = n.mac AND active = true
        ))
        -- Include uplinks (remote_ip connections, no time restriction needed)
        OR dp.remote_ip IS NOT NULL
    )
ORDER BY 
    dp.ip, dp.port;
SQL;

    // Execute the query via psql
    $command = "sudo -u netdisco /usr/bin/psql -U netdisco -d netdisco -t -A -F'|' -c " . escapeshellarg($sql);
    $output = shell_exec($command . ' 2>&1');
    
    if ($output === null) {
        throw new Exception("Failed to execute netdisco query for switch: $switchIp");
    }
    
    // Parse the output
    $lines = array_filter(explode("\n", trim($output)));
    $results = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '') continue;
        
        // Expected order: parent_ip|port|child_ip|connection_type
        $parts = explode('|', $line);
        
        if (count($parts) >= 4) {
            $parentIp = trim($parts[0]);
            $port = trim($parts[1]);
            $childIpRaw = trim($parts[2]);
            $connectionType = trim($parts[3]);
            
            // Handle multiple IPs separated by commas (from string_agg)
            $childIps = array_map('trim', explode(',', $childIpRaw));
            
            foreach ($childIps as $childIp) {
                // Remove CIDR notation (e.g., /32, /24) from IP addresses
                $childIp = preg_replace('/\/\d+$/', '', $childIp);
                
                // Validate IP addresses
                if (filter_var($parentIp, FILTER_VALIDATE_IP) && 
                    filter_var($childIp, FILTER_VALIDATE_IP) &&
                    $parentIp !== $childIp) {
                    
                    $results[] = [
                        'parent_ip' => $parentIp,
                        'child_ip' => $childIp,
                        'connection_type' => $connectionType
                    ];
                }
            }
        }
    }
    
    return $results;
}
?> 