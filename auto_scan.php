<?php
include "loadenv.php";

/**
 * Auto-scanning script to detect new hosts on all known subnets across all infrastructures
 * This script is called by background_scan.php to automatically detect new hosts
 */

// Database configuration
define('DB_HOST', $_ENV["DB_SERVER"]);
define('DB_USER', $_ENV["DB_USER"]);
define('DB_PASS', $_ENV["DB_PASSWORD"]);
define('DB_NAME', 'bubblemaps');

// Function to read bubblemap configs
function readBubblemapConfig($key) {
    $configFile = dirname(__FILE__) . '/conf/bubblemap_configs';
    if (!file_exists($configFile)) {
        return null;
    }
    
    $lines = file($configFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, $key . ':') === 0) {
            return trim(substr($line, strlen($key) + 1));
        }
    }
    return null;
}

// Function to check if host discovery is enabled
function isHostDiscoveryEnabled() {
    $setting = readBubblemapConfig('host discovery');
    return $setting === 'on';
}

/**
 * Get a database connection
 * @return mysqli Database connection
 */
function getDatabaseConnection() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    return $conn;
}

/**
 * Get admin user credentials from the blesk database
 * @return array|null Array with username and password or null if not found
 */
function getAdminCredentials() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, 'blesk');
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $credentials = [
            "username" => $row["username"],
            "password" => $row["password"]
        ];
    } else {
        $credentials = null;
    }

    $conn->close();
    return $credentials;
}

/**
 * Get the server's IP address
 * @return string The server's IP address
 */
function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

/**
 * Get all infrastructures from the database
 * @return array List of infrastructure names
 */
function getAllInfrastructures() {
    $conn = getDatabaseConnection();
    $sql = "SELECT name FROM infra";
    $result = $conn->query($sql);
    
    $infrastructures = [];
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $infrastructures[] = $row['name'];
        }
    }
    
    $conn->close();
    return $infrastructures;
}

/**
 * Get blacklisted subnets from file
 * @return array List of blacklisted subnets
 */
function getBlacklistedSubnets() {
    $file = __DIR__ . '/conf/subnets_blacklist';
    if (!file_exists($file)) {
        return [];
    }
    $content = file_get_contents($file);
    return array_filter(array_map('trim', explode("\n", $content)));
}

/**
 * Get all subnets for a specific infrastructure
 * @param string $infra Infrastructure name
 * @return array List of subnets
 */
function getSubnetsForInfra($infra) {
    $conn = getDatabaseConnection();
    
    $stmt = $conn->prepare("SELECT subnet FROM subnets WHERE infra = ? AND subnet NOT IN ('External', 'localhost', 'Azure')");
    $stmt->bind_param("s", $infra);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $subnets = [];
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $subnets[] = $row['subnet'];
        }
    }
    
    $stmt->close();
    $conn->close();
    
    // Filter out blacklisted subnets
    $blacklisted = getBlacklistedSubnets();
    return array_diff($subnets, $blacklisted);
}

/**
 * Scan a specific subnet in an infrastructure
 * @param string $subnet Subnet to scan
 * @param string $infra Infrastructure name
 * @return bool True if scan was successful, false otherwise
 */
function scanSubnet($subnet, $infra) {
    $credentials = getAdminCredentials();
    $serverIp = getSelfIp();
    
    if ($credentials === null) {
        error_log("Failed to retrieve admin credentials");
        return false;
    }
    
    // Ensure the subnet has the correct format for scanning (append 1-254 if needed)
    if (substr($subnet, -1) === '.') {
        $subnetToScan = $subnet . "1-254";
    } else {
        $subnetToScan = $subnet;
    }
    
    // Create the URL for the scan - use the absolute path to scan.php
    // Add approval=ask parameter to ensure hosts get the 'ask' status
    $scanUrl = "https://$serverIp/bubblemaps/scan.php?infra=" . urlencode($infra) . 
               "&ip=" . urlencode($subnetToScan) . 
               "&forceScan=yes&approval=ask";
    
    // Set up cURL to call the scan URL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $scanUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERPWD => $credentials['username'] . ":" . $credentials['password'],
        CURLOPT_HTTPAUTH => CURLAUTH_BASIC
    ]);
    
    // Execute the cURL request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    // Log the response for debugging
    error_log("Scan URL: $scanUrl");
    error_log("HTTP Code: $httpCode");
    error_log("Response: " . substr($response, 0, 200) . "...");
    
    // Check for errors
    if (curl_errno($ch)) {
        error_log("cURL error when scanning subnet $subnet in infra $infra: " . curl_error($ch));
        curl_close($ch);
        return false;
    }
    
    curl_close($ch);
    
    // Check if the HTTP status code indicates success
    if ($httpCode != 200) {
        error_log("HTTP error when scanning subnet $subnet in infra $infra: $httpCode");
        return false;
    }
    
    return true;
}

/**
 * Main function to scan all subnets in all infrastructures
 */
function scanAllSubnets() {
    echo "Starting automatic subnet scanning...\n";
    
    // Get all infrastructures
    $infrastructures = getAllInfrastructures();
    if (empty($infrastructures)) {
        echo "No infrastructures found in the database.\n";
        return;
    }
    
    echo "Found " . count($infrastructures) . " infrastructure(s).\n";
    
    // For each infrastructure, get and scan all subnets
    foreach ($infrastructures as $infra) {
        echo "Processing infrastructure: $infra\n";
        
        // Get all subnets for this infrastructure
        $subnets = getSubnetsForInfra($infra);
        if (empty($subnets)) {
            echo "No subnets found for infrastructure $infra.\n";
            continue;
        }
        
        echo "Found " . count($subnets) . " subnet(s) in $infra.\n";
        
        // Scan each subnet
        foreach ($subnets as $subnet) {
            echo "Scanning subnet: $subnet in $infra...\n";
            
            $result = scanSubnet($subnet, $infra);
            if ($result) {
                echo "Successfully scanned subnet $subnet in $infra.\n";
            } else {
                echo "Failed to scan subnet $subnet in $infra.\n";
            }
        }
    }
    
    echo "Automatic subnet scanning completed.\n";
}

// Function to read the lock status
function isLocked($lockFile) {
    if (!file_exists($lockFile)) {
        return false;
    }
    
    $fp = @fopen($lockFile, 'r');
    if (!$fp) {
        return false;
    }
    
    // Try to get a shared lock (non-blocking)
    $locked = !flock($fp, LOCK_SH | LOCK_NB);
    fclose($fp);
    
    return $locked;
}

// Function to set the lock status
function setLock($lockFile, $status) {
    global $lockHandle;
    
    if ($status) {
        // Create or open the lock file
        $lockHandle = fopen($lockFile, 'w');
        if (!$lockHandle) {
            throw new Exception("Unable to create lock file: $lockFile");
        }
        
        // Try to acquire an exclusive lock (non-blocking)
        if (!flock($lockHandle, LOCK_EX | LOCK_NB)) {
            fclose($lockHandle);
            throw new Exception("Unable to acquire lock: $lockFile");
        }
        
        // Write PID to the lock file
        fwrite($lockHandle, getmypid());
        fflush($lockHandle);
    } else if (isset($lockHandle) && is_resource($lockHandle)) {
        // Release the lock
        flock($lockHandle, LOCK_UN);
        fclose($lockHandle);
        
        // Remove the lock file
        if (file_exists($lockFile)) {
            @unlink($lockFile);
        }
    }
}

// Script start - Check if host discovery is enabled
if (!isHostDiscoveryEnabled()) {
    echo "Host discovery is disabled. Exiting.\n";
    exit;
}

// Disallow the script to run multiple instances if already running
$lockFile = dirname(__FILE__) . '/locks/auto_scan.lock';
$lockHandle = null;

// Check if the script is already running
if (isLocked($lockFile)) {
    echo "Auto scan script is already running. Exiting.\n";
    exit;
}

// Set the lock
try {
    setLock($lockFile, true);
    
    // Run the main function
    scanAllSubnets();

    echo "Auto scan script completed successfully.\n";
} catch (Exception $e) {
    // Handle any errors that occur during execution
    echo "Error: " . $e->getMessage() . "\n";
} finally {
    // Always release the lock when the script finishes
    setLock($lockFile, false);
} 