<?php
require_once 'redirect_to_view.php';
include 'language_selector.php';
include 'theme_loader.php'; // Include the theme loader
include 'components/statusIndicators.php';
require_once 'loadenv.php';

// Get default infra name for All Hosts view
$defaultInfra = 'Infrastructure';
try {
    $db = new PDO('mysql:host=' . $_ENV["DB_SERVER"] . ';dbname=bubblemaps', $_ENV["DB_USER"], $_ENV["DB_PASSWORD"]);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $query = "SELECT name FROM infra LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $defaultInfra = $row['name'];
    }
} catch (PDOException $e) {
    // Fallback to default if DB error
    $defaultInfra = 'Infrastructure';
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blesk</title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/hostlist.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <script src="functions/d3.v7.min.js"></script>
    <script src="functions/fetchInfraBubbles.js"></script>
    <script src="functions/helperFunctions.js"></script>
    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <script src="functions/background_beacon.js"></script>
    <script src="functions/host_approval.js"></script>
    <script src="functions/hostlistPhpFunctions/hostListView.js"></script>
    <script src="functions/scanModal.js"></script>
    <script src="functions/shared/featureStatusIndicators.js"></script>
    <script src="functions/infraPhpFunctions/infraBubbleStatus.js"></script>
    <script src="functions/setMainView.js"></script>
    <script src="functions/globalAiAssistant.js"></script>
</head>

<body>
    <header>
        <div style="display: flex; align-items: center; gap: 15px;">
            <a href="../../index.php">
                <img style="width: 25px;" src="../../images/blesk-favicon.svg" alt="Home">
            </a>
            <div class="breadcrumbs">
                <span class="current"><i class="fa fa-home"></i> Home</span>
            </div>
            
            <!-- Simple APM Progress Indicator -->
            <div id="apm-progress-simple" class="apm-progress-simple" title="Updating ALL hosts status..." style="display: none;">
                <i class="fa fa-refresh fa-spin"></i>
                <span id="apm-progress-count">0/0</span>
            </div>
            
            <!-- Feature Status Indicators -->
            <?php renderStatusIndicators(); ?>
            
            <!-- Status Filters in Header -->
            <div class="hostlist-status-filters-header">
                <div class="hostlist-filter-section-header">
                    <span class="filter-label">Hosts:</span>
                    <button class="hostlist-status-filter ok" data-type="host" data-status="ok" title="Up"></button>
                    <button class="hostlist-status-filter down" data-type="host" data-status="down" title="Down"></button>
                    <button class="hostlist-status-filter unknown" data-type="host" data-status="unknown" title="Unreachable"></button>
                    <button class="hostlist-status-filter pending" data-type="host" data-status="pending" title="Pending"></button>
                </div>
                <div class="hostlist-filter-section-header">
                    <span class="filter-label">Services:</span>
                    <button class="hostlist-status-filter ok" data-type="service" data-status="ok" title="OK"></button>
                    <button class="hostlist-status-filter warning" data-type="service" data-status="warning" title="Warning"></button>
                    <button class="hostlist-status-filter critical" data-type="service" data-status="critical" title="Critical"></button>
                    <button class="hostlist-status-filter unknown" data-type="service" data-status="unknown" title="Unknown"></button>
                    <button class="hostlist-status-filter pending" data-type="service" data-status="pending" title="Pending"></button>
                </div>
            </div>
        </div>
        
        <div class="header-content">
            <div class="hamburger">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </div>
            <div class="header-buttons">
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="viewDropdownBtn"><i class="fa fa-eye"></i> View <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="viewDropdownContent">
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'list', 'hostlist.php')"><i class="fa fa-list"></i> Asset Overview</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'table', 'tableview.php')"><i class="fa fa-table"></i> Data Grid</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'dashboards', 'networkmap.php')"><i class="fa fa-map"></i> Dashboards and Visualization</a>
                    </div>
                </div>
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="menuDropdownBtn"><i class="fa fa-bars"></i> Control Panel <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="menuDropdownContent">
                        <a class="header-button" id="scanModal-btn"><i class="fa fa-search"></i> Scan</a>
                        <a class="header-button" onclick='showModal("https://<?php echo $_SERVER["HTTP_HOST"]; ?>/bubblemaps/credentials.php")'><i class="fa fa-key"></i> Credentials</a>
                        <a class="header-button" href="notifications.php"><i class="fa fa-bell"></i> Notifications</a>
                        <a class="header-button" href="reports.php"><i class="fa fa-file-text"></i> Reports</a>
                    </div>
                </div>
                <a class="header-button" id="openSettingsBtn"><i class="fa fa-cog"></i> Advanced Settings</a>
            </div>
        </div>
    </header>
    
    <!-- Mobile Status Container (hidden by default, shown via JS on mobile) -->
    <div class="mobile-status-container" id="mobile-status-container">
        <!-- APM Progress Indicator for mobile -->
        <div id="apm-progress-mobile" class="apm-progress-mobile" title="Updating ALL hosts status..." style="display: none;">
            <i class="fa fa-refresh fa-spin"></i>
            <span id="apm-progress-count-mobile">0/0</span>
        </div>
        
        <!-- Feature Status Indicators for mobile -->
        <?php renderStatusIndicators(true); ?>
        
        <!-- Status Filters for mobile -->
        <div class="hostlist-status-filters-header">
            <div class="hostlist-filter-section-header">
                <span class="filter-label">Hosts:</span>
                <button class="hostlist-status-filter ok" data-type="host" data-status="ok" title="Up"></button>
                <button class="hostlist-status-filter down" data-type="host" data-status="down" title="Down"></button>
                <button class="hostlist-status-filter unknown" data-type="host" data-status="unknown" title="Unreachable"></button>
                <button class="hostlist-status-filter pending" data-type="host" data-status="pending" title="Pending"></button>
            </div>
            <div class="hostlist-filter-section-header">
                <span class="filter-label">Services:</span>
                <button class="hostlist-status-filter ok" data-type="service" data-status="ok" title="OK"></button>
                <button class="hostlist-status-filter warning" data-type="service" data-status="warning" title="Warning"></button>
                <button class="hostlist-status-filter critical" data-type="service" data-status="critical" title="Critical"></button>
                <button class="hostlist-status-filter unknown" data-type="service" data-status="unknown" title="Unknown"></button>
                <button class="hostlist-status-filter pending" data-type="service" data-status="pending" title="Pending"></button>
            </div>
        </div>
    </div>
    
    <div id="canvas-container">
        <svg id="map"></svg>
    </div>
    <div id="context-menu" class="context-menu">
        <ul>
            <li id="rename">Rename</li>
            <li id="view">View</li>
            <li id="delete">Delete Infrastructure</li>
        </ul>
    </div>
    <?php include "settingsModal.php"; ?>

    <!-- Scan Modal will be created by scanModal.js -->
    <!-- iframe Modal -->
    <div id="infoModal" class="iframeModal">
        <div class="iframeModal-content" id="iframeModal-content">
            <span class="iframeMclose" title="Back" aria-label="Back">←</span>
            <div class="iframe-loader"></div>
            <iframe id="iframeModal-frame"></iframe>
        </div>
    </div>
    
    <!-- Feature Issue Popover -->
    <div id="feature-issue-popover" class="status-hosts-popover" style="display: none;">
        <h4 id="popover-title">Feature Issues</h4>
        <ul id="popover-list">
            <!-- Populated by JavaScript -->
        </ul>
    </div>
    <script>
        const modalBody = document.getElementById('iframeModal-content');
        // Store the original inline styles of modalBody
        const originalStyles = modalBody.style.cssText;
        // Global URL parameters
        const urlParams = new URLSearchParams(window.location.search);

        async function updateBubbleName(id, newName) {
            try {
                const response = await fetch('update_infra_name.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `id=${id}&newName=${newName}`
                });
                const result = await response.text();
                if (result !== 'Success') {
                    console.error('Error updating bubble name:', result);
                }
            } catch (error) {
                console.error('Error updating bubble name:', error);
            }
        }

        async function deleteInfrastructure(infraName) {
            // Show confirmation dialog
            if (!confirm(`Are you sure you want to delete the infrastructure "${infraName}"?\n\nThis will delete the infrastructure and all its subnets, but only if no hosts are assigned to it.`)) {
                return;
            }

            try {
                // Send delete request
                const formData = new FormData();
                formData.append('infra', infraName);

                const response = await fetch('delete_infra.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    // Reload the page to refresh the infrastructure list
                    window.location.reload();
                } else {
                    alert('Error: ' + result.message);
                }

            } catch (error) {
                console.error('Error deleting infrastructure:', error);
                alert('An error occurred while deleting the infrastructure. Please try again.');
            }
        }

        window.addEventListener('resize', () => {
            window.location.reload();
        });
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                window.location.reload();
            }
        });
        document.addEventListener('DOMContentLoaded', function() {
            const contextMenu = document.getElementById('context-menu');
            let currentBubble = null;

            document.addEventListener('contextmenu', function(event) {
                // Only trigger the menu if the right-click target is a host bubble
                if (event.target.classList.contains('host-bubble')) {
                    event.preventDefault();
                    currentBubble = event.target; // Store the DOM element
                    const bubbleData = d3.select(currentBubble).datum(); // Get the bound data

                    // Position the context menu at the mouse coordinates
                    contextMenu.style.top = event.clientY + 'px';
                    contextMenu.style.left = event.clientX + 'px';
                    contextMenu.style.display = 'block';
                } else {
                    contextMenu.style.display = 'none';
                    currentBubble = null;
                }
            });

            document.addEventListener('click', function(event) {
                // If no bubble is currently selected, hide the menu and exit early.
                if (!currentBubble) {
                    contextMenu.style.display = 'none';
                    return;
                }

                const bubbleData = d3.select(currentBubble).datum(); // Safely retrieve the data

                if (event.target.id === 'rename') {
                    // Prompt for a new name using the bubble's current hostname
                    const newName = prompt('Enter new name for the bubble:', bubbleData.hostname);
                    if (newName) {
                        // Update the data
                        bubbleData.hostname = newName;
                        d3.select(currentBubble).datum(bubbleData);
                        // Update the corresponding label.
                        // It is assumed that labels have the class 'bubble-text' and share the same id.
                        d3.selectAll('#map g .bubble-text')
                            .filter(d => d.id === bubbleData.id)
                            .text(newName);
                        // Update on the server or internal state.
                        updateBubbleName(bubbleData.id, newName);
                    }
                    contextMenu.style.display = 'none';
                    currentBubble = null;
                } else if (event.target.id === 'view') {
                    event.preventDefault();
                    const targetUrl = urlParams.has('subnet')
                        ? `subnets.php?infra=${encodeURIComponent(bubbleData.hostname)}`
                        : `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${encodeURIComponent(bubbleData.hostname)}`;
                    window.location.href = targetUrl;
                } else if (event.target.id === 'delete') {
                    event.preventDefault();
                    deleteInfrastructure(bubbleData.hostname);
                    contextMenu.style.display = 'none';
                    currentBubble = null;
                } else {
                    contextMenu.style.display = 'none';
                    currentBubble = null;
                }
            });
        });

        // Scan modal - Now handled by modular scanModal.js
        var btnS = document.getElementById("scanModal-btn");
        btnS.onclick = function() {
            if (window.scanModal) {
                window.scanModal.show();
            }
        }

        function showModal(url, modalType = null, serviceName = null, tab = null) {
            const modal = document.getElementById('infoModal');
            const iframe = document.getElementById('iframeModal-frame');
            const modalBody = document.getElementById('iframeModal-content');
            
            if (!modal || !iframe || !modalBody) {
                console.error('Modal elements not found:', { modal, iframe, modalBody });
                return;
            }
            
            const originalStyles = modalBody.style.cssText;

            if (url.includes('credentials.php')){
                modalBody.style.maxHeight = '800px';
                modalBody.style.maxWidth = '1000px';
                // Remove 'active' class from header-buttons when opening credentials modal
                ['.header-buttons', '.hamburger'].forEach(sel => {
                    const el = document.querySelector(sel);
                    if (el) el.classList.remove('active');
                });
            } else {
                // Explicitly reset all styles for non-credentials modals
                modalBody.style.maxHeight = '';
                modalBody.style.maxWidth = '';
                modalBody.style.top = '';
                modalBody.style.width = '';
                modalBody.style.height = '';
            }

            modal.classList.remove('loaded');
            iframe.style.display = 'none';
            iframe.src = url;
            modal.style.display = 'block';
            modal.classList.add('show');
            modal.classList.remove('small');

            // Set data attributes for opening specific modals within the iframe
            if (modalType === 'host') {
                iframe.setAttribute('data-open-host-modal', 'true');
                if (tab) {
                    iframe.setAttribute('data-open-tab', tab);
                }
            } else if (modalType === 'service' && serviceName) {
                iframe.setAttribute('data-open-service-modal', 'true');
                iframe.setAttribute('data-service-name', serviceName);
                if (tab) {
                    iframe.setAttribute('data-open-tab', tab);
                }
            }

            iframe.onload = function() {
                const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                const navElement = iframeDocument.querySelector("nav");
                if (navElement) {
                    navElement.style.display = "none";
                }
                
                // Check if we need to open a host or service modal
                if (iframe.hasAttribute('data-open-host-modal')) {
                    // Try to trigger a click on the host card to open host modal
                    setTimeout(() => {
                        try {
                            const hostCard = iframeDocument.getElementById('host-card');
                            if (hostCard) {
                                hostCard.click();
                                
                                // If we need to open a specific tab
                                const tab = iframe.getAttribute('data-open-tab');
                                if (tab === 'performance') {
                                    // Wait for the modal to open and then click on the performance tab
                                    setTimeout(() => {
                                        try {
                                            const performanceTab = iframeDocument.querySelector('button.tab-button[data-tab="performance"]');
                                            if (performanceTab) {
                                                performanceTab.click();
                                            }
                                        } catch (e) {
                                            console.error('Failed to open performance tab:', e);
                                        }
                                    }, 700);
                                }
                            }
                        } catch (e) {
                            console.error('Failed to auto-open host modal:', e);
                        }
                        iframe.removeAttribute('data-open-host-modal');
                        iframe.removeAttribute('data-open-tab');
                    }, 500);
                } else if (iframe.hasAttribute('data-open-service-modal')) {
                    // Try to trigger a click on the specific service card
                    setTimeout(() => {
                        try {
                            const serviceName = iframe.getAttribute('data-service-name');
                            const encodedServiceName = encodeURIComponent(serviceName);
                            const serviceCard = iframeDocument.querySelector(`.service-card[data-service="${encodedServiceName}"]`);
                            if (serviceCard) {
                                serviceCard.click();
                                
                                // If we need to open a specific tab
                                const tab = iframe.getAttribute('data-open-tab');
                                if (tab === 'performance') {
                                    // Wait for the modal to open and then click on the performance tab
                                    setTimeout(() => {
                                        try {
                                            const performanceTab = iframeDocument.querySelector('button.tab-button[data-tab="performance"]');
                                            if (performanceTab) {
                                                performanceTab.click();
                                            }
                                        } catch (e) {
                                            console.error('Failed to open performance tab:', e);
                                        }
                                    }, 700);
                                }
                            }
                        } catch (e) {
                            console.error('Failed to auto-open service modal:', e);
                        }
                        iframe.removeAttribute('data-open-service-modal');
                        iframe.removeAttribute('data-service-name');
                        iframe.removeAttribute('data-open-tab');
                    }, 500);
                }
                
                // Apply translations if translator is available
                if (typeof translator !== 'undefined') {
                    translator.translateIframe(iframe);
                }
                modal.classList.add('loaded');
                iframe.style.display = 'block';
            };
        }
        document.querySelector('.iframeMclose').addEventListener('click', function() {
            const modal = document.getElementById('infoModal');
            if (!modal) return;
            modal.classList.remove('show');
            modal.classList.add('closing');
            setTimeout(() => {
                modal.classList.remove('closing');
                modal.style.display = 'none';
            }, 320);
        });

        window.addEventListener('click', function(event) {
            const modal = document.getElementById('infoModal');
            if (event.target == modal) {
                modal.classList.remove('show');
                modal.classList.add('closing');
                setTimeout(() => {
                    modal.classList.remove('closing');
                    modal.style.display = 'none';
                }, 300);
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Azure Scan functionality
            const scanForm = document.getElementById('scanForm');
            const azureWrapper = document.querySelector('.azure-scan-wrapper');
            const ipGroup = document.getElementById('ip')?.closest('.form-group');
            const internalGroup = document.getElementById('internal')?.closest('.form-group');

            // Toggle visibility based on scanType change
            scanForm.addEventListener('change', (e) => {
                if (e.target.name === 'scanType') {
                    const isAzure = e.target.value === 'azure';
                    // Toggle groups
                    if (ipGroup) ipGroup.style.display = isAzure ? 'none' : '';
                    if (internalGroup) internalGroup.style.display = isAzure ? 'none' : '';
                    azureWrapper.style.display = isAzure ? '' : 'none';
                    // Adjust required attribute for IP field
                    const ipInput = document.getElementById('ip');
                    if (ipInput) ipInput.required = !isAzure;
                    // Hide/show submit button based on scan type
                    const submitButton = scanForm.querySelector('button[type="submit"]');
                    if (submitButton) submitButton.style.display = isAzure ? 'none' : '';
                }
            });

            // Azure scan functionality is now handled by modular scanModal.js

            // Original scan form submission
            scanForm.addEventListener('submit', function(event) {
                const scanType = document.querySelector('input[name="scanType"]:checked')?.value;
                if (scanType === 'azure') {
                    // Prevent submitting to scan.php; the Azure flow is handled entirely
                    // via JS + dedicated backend above.
                    event.preventDefault();
                    return;
                }

                event.preventDefault();
                const submitButton = this.querySelector('.btn-submit');
                submitButton.disabled = true;
                submitButton.textContent = 'Scanning...';
                const formData = new FormData(this);
                const params = new URLSearchParams(formData).toString();
                const infraValue = formData.get('infra');
                const input = formData.get('ip');

                if(!isValidNetworkString(input)){
                    alert('Invalid network string');
                    submitButton.disabled = false;
                    submitButton.textContent = 'Submit';
                    return; // Stop further execution
                }

                fetch('scan.php?' + params)
                    .then(response => response.text())
                    .then(data => {
                        return `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${infraValue}`;
                    })
                    .then(url => {
                        window.location.href = url;
                    })
                    .catch(error => {
                        alert(error);
                    })
                    .finally(() => {
                        submitButton.disabled = false;
                        submitButton.textContent = 'Submit';
                    });
            });
            const hamburger = document.querySelector('.hamburger');
            const headerButtons = document.querySelector('.header-buttons');

            hamburger.addEventListener('click', function(e) {
                e.stopPropagation();
                this.classList.toggle('active');
                headerButtons.classList.toggle('active');
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.header-content')) {
                    hamburger.classList.remove('active');
                    headerButtons.classList.remove('active');
                }
            });

            const dropdownBtn = document.getElementById('menuDropdownBtn');
            const dropdownContent = document.getElementById('menuDropdownContent');
            if(dropdownBtn && dropdownContent){
                dropdownBtn.addEventListener('click', function(e){
                    e.preventDefault();
                    e.stopPropagation();
                    // Close other dropdowns first
                    const viewDropdownContent = document.getElementById('viewDropdownContent');
                    if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                    dropdownContent.classList.toggle('show');
                });
            }

            const viewDropdownBtn = document.getElementById('viewDropdownBtn');
            const viewDropdownContent = document.getElementById('viewDropdownContent');
            if(viewDropdownBtn && viewDropdownContent){
                viewDropdownBtn.addEventListener('click', function(e){
                    e.preventDefault();
                    e.stopPropagation();
                    // Close other dropdowns first
                    if(dropdownContent) dropdownContent.classList.remove('show');
                    viewDropdownContent.classList.toggle('show');
                });
            }
            
            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e){
                if(!e.target.closest('.header-dropdown')){
                    if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                    if(dropdownContent) dropdownContent.classList.remove('show');
                }
            });
        });

        // Helper function to check if we're on mobile
        function isMobile() {
            return window.innerWidth <= 768;
        }
        
        // Initialize status filters functionality
        function initStatusFilters() {
            // Fetch initial counts
            updateStatusCounts();
            
            // Update counts every 30 seconds
            setInterval(updateStatusCounts, 30000);
        }
        
        // Fetch and update status counts from Nagios API
        async function updateStatusCounts() {
            try {
                const [hostResponse, serviceResponse] = await Promise.all([
                    fetch(`https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=hostcount`),
                    fetch(`https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=servicecount`)
                ]);
                
                if (!hostResponse.ok || !serviceResponse.ok) {
                    throw new Error('Failed to fetch status counts');
                }
                
                const hostData = await hostResponse.json();
                const serviceData = await serviceResponse.json();
                
                if (hostData.result.type_code !== 0 || serviceData.result.type_code !== 0) {
                    throw new Error('API returned error');
                }
                
                // Update host filter buttons
                const hostCounts = hostData.data.count;
                updateFilterButton('host', 'ok', hostCounts.up || 0, 'Up');
                updateFilterButton('host', 'down', hostCounts.down || 0, 'Down');
                updateFilterButton('host', 'unknown', hostCounts.unreachable || 0, 'Unreachable');
                updateFilterButton('host', 'pending', hostCounts.pending || 0, 'Pending');
                
                // Update service filter buttons
                const serviceCounts = serviceData.data.count;
                updateFilterButton('service', 'ok', serviceCounts.ok || 0, 'OK');
                updateFilterButton('service', 'warning', serviceCounts.warning || 0, 'Warning');
                updateFilterButton('service', 'critical', serviceCounts.critical || 0, 'Critical');
                updateFilterButton('service', 'unknown', serviceCounts.unknown || 0, 'Unknown');
                updateFilterButton('service', 'pending', serviceCounts.pending || 0, 'Pending');
                
            } catch (error) {
                console.error('Error updating status counts:', error);
            }
        }
        
        // Update a specific filter button with count
        function updateFilterButton(type, status, count, label) {
            // Update both desktop and mobile versions
            const selectors = [
                `.hostlist-status-filter[data-type="${type}"][data-status="${status}"]`,
                `.mobile-status-container .hostlist-status-filter[data-type="${type}"][data-status="${status}"]`
            ];
            
            selectors.forEach(selector => {
                const buttons = document.querySelectorAll(selector);
                buttons.forEach(btn => {
                    btn.textContent = count;
                    btn.setAttribute('title', `${label} (${count})`);
                });
            });
        }
        
        // APM Progress monitoring
        function startApmProgressMonitoring() {
            checkApmProgress();
            setInterval(checkApmProgress, 500);
        }
        
        function checkApmProgress() {
            fetch('src/settingsphp/runUpdateApmStatus.php?action=progress')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    try {
                        const data = JSON.parse(text);
                        
                        const showOnMobile = isMobile();
                        const simpleIndicator = document.getElementById('apm-progress-simple');
                        const mobileIndicator = document.getElementById('apm-progress-mobile');
                        const countText = document.getElementById('apm-progress-count');
                        const countTextMobile = document.getElementById('apm-progress-count-mobile');
                        
                        const activeIndicator = showOnMobile ? mobileIndicator : simpleIndicator;
                        const inactiveIndicator = showOnMobile ? simpleIndicator : mobileIndicator;
                        const activeCountText = showOnMobile ? countTextMobile : countText;
                        
                        if (data.total > 0 || data.status === 'running' || data.status === 'completed') {
                            if (activeIndicator) {
                                activeIndicator.style.display = 'flex';
                            }
                            if (inactiveIndicator) {
                                inactiveIndicator.style.display = 'none';
                            }
                            
                            if (activeCountText) {
                                if (data.status === 'running') {
                                    activeCountText.textContent = data.current + '/' + data.total;
                                } else if (data.status === 'completed') {
                                    activeCountText.textContent = data.total + '/' + data.total + ' ✓';
                                } else {
                                    activeCountText.textContent = data.current + '/' + data.total;
                                }
                            }
                        } else if (data.status && data.status.startsWith('error')) {
                            if (activeIndicator) {
                                activeIndicator.style.display = 'flex';
                            }
                            if (inactiveIndicator) {
                                inactiveIndicator.style.display = 'none';
                            }
                            if (activeCountText) {
                                activeCountText.textContent = 'Error';
                            }
                        } else {
                            if (data.status === 'idle' && data.total === 0) {
                                if (activeIndicator) {
                                    activeIndicator.style.display = 'none';
                                }
                                if (inactiveIndicator) {
                                    inactiveIndicator.style.display = 'none';
                                }
                            }
                        }
                    } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                    }
                })
                .catch(error => {
                    console.error('Error checking APM progress:', error);
                });
        }
        
        // Add mobile styles (same as subnets.php)
        function addMobileStyles() {
            const mobileStyles = document.createElement('style');
            mobileStyles.innerHTML = `
                /* Mobile Status Container Styles */
                .mobile-status-container {
                    display: none;
                    flex-direction: column;
                    padding: 8px 15px;
                    border-bottom: 1px solid #555;
                    position: relative;
                    z-index: 8;
                }
                
                /* Only show the mobile status container in mobile view */
                @media (max-width: 768px) {
                    .mobile-status-container {
                        display: flex;
                        gap: 10px;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                    }
                    
                    /* Hide header indicators on mobile - show mobile ones instead */
                    header .hostlist-status-indicators {
                        display: none !important;
                    }
                    
                    header .hostlist-status-filters-header {
                        display: none !important;
                    }
                    
                    header #apm-progress-simple {
                        display: none !important;
                    }
                    
                    /* APM Progress for mobile */
                    .apm-progress-mobile {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        font-size: 14px;
                        color: rgba(255, 255, 255, 0.8);
                        margin-bottom: 5px;
                    }
                    
                    .apm-progress-mobile i {
                        color: #ccc;
                        font-size: 13px;
                        margin-right: 2px;
                    }
                }
            `;
            document.head.appendChild(mobileStyles);
        }

        // Initialize feature status indicators
        if (typeof initFeatureStatusIndicators === 'function') {
            initFeatureStatusIndicators();
        }
        
        // Initialize status filters
        initStatusFilters();
        
        // Start APM progress monitoring
        startApmProgressMonitoring();
        
        // Add mobile styles
        addMobileStyles();

        
        fetchInfraBubbles();
        getInfrasNames();
        const dictionaries = <?php echo json_encode($dictionaries); ?>;
        const selectedLang = '<?php echo $selectedLang; ?>';
        const translator = new Translator(dictionaries, selectedLang);
        translator.init();
        initTranslationObserver(translator); // Initialize the observer directly
        
        // Fallback in case the host_approval.js script fails to load
        if (typeof initHostApproval !== 'function') {
            function initHostApproval() {
                console.log('Host approval module not loaded. Using fallback function.');
            }
        }
        
        // Initialize host approval notifications
        initHostApproval(urlParams.get('infra'));

        d3.selectAll('#map g .host-bubble').on("click", function(event, d) {
            const urlParams = new URLSearchParams(window.location.search);
            const targetUrl = urlParams.has('subnet')
                ? `subnets.php?infra=${encodeURIComponent(d.hostname)}&subnet=true`
                : `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${encodeURIComponent(d.hostname)}`;
            window.location.href = targetUrl;
        });
    </script>
</body>

</html>