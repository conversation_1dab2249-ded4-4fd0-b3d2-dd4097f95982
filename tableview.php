<?php
include 'language_selector.php';
include 'theme_loader.php';
include 'components/statusIndicators.php';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Blesk - Table View</title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/hostlist.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/tableview.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <script src="functions/d3.v7.min.js"></script>
    <script src="functions/fetchHostGroups.js"></script>
    <script src="functions/helperFunctions.js"></script>
    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <script src="functions/background_beacon.js"></script>
    <!-- Reuse data/API helpers from host list view -->
    <script src="functions/hostlistPhpFunctions/hostListView.js"></script>
    <script src="functions/hostlistPhpFunctions/searchFilters.js"></script>
    <script src="functions/hostlistPhpFunctions/exportCSV.js"></script>
    <script src="functions/shared/featureStatusIndicators.js"></script>
    <script src="functions/hostlistPhpFunctions/modalHandlers.js"></script>
    <script src="functions/hostlistPhpFunctions/contextMenu.js"></script>
    <script src="functions/hostlistPhpFunctions/autoRefresh.js"></script>
    <script src="functions/hostlistPhpFunctions/mobileLayout.js"></script>
    <script src="functions/tableviewPhpFunctions/init.js"></script>
    <script src="functions/scanModal.js"></script>
    <script src="functions/setMainView.js"></script>
    <script src="functions/globalAiAssistant.js"></script>
</head>

<body>
    <header>
        <div style="display: flex; align-items: center; gap: 15px;">
            <a href="../../index.php">
                <img style="width: 25px;" src="../../images/blesk-favicon.svg" alt="Home">
            </a>
            <div class="breadcrumbs">
                <a href="infra.php<?php echo isset($_GET['subnet']) ? '?subnet=true' : ''; ?>">
                    <i class="fa fa-home"></i> Home
                </a>
                <span class="separator">/</span>
                <span class="current">
                    <i class="fa fa-table"></i> Data Grid
                </span>
            </div>

            <!-- Auto-refresh countdown -->
            <div id="refresh-countdown" class="refresh-countdown" title="Auto-refreshing in...">
                <i class="fa fa-refresh"></i>
                <span id="countdown-timer">60s</span>
            </div>

            <!-- Combined Host and Service Status Indicators -->
            <?php renderStatusIndicators(); ?>

            <!-- Status Filters in Header -->
            <div class="hostlist-status-filters-header">
                <div class="hostlist-filter-section-header">
                    <span class="filter-label">Hosts:</span>
                    <button class="hostlist-status-filter ok" data-type="host" data-status="ok" title="Up"></button>
                    <button class="hostlist-status-filter down" data-type="host" data-status="down" title="Down"></button>
                    <button class="hostlist-status-filter unknown" data-type="host" data-status="unknown" title="Unreachable"></button>
                    <button class="hostlist-status-filter pending" data-type="host" data-status="pending" title="Pending"></button>
                </div>
                <div class="hostlist-filter-section-header">
                    <span class="filter-label">Services:</span>
                    <button class="hostlist-status-filter ok" data-type="service" data-status="ok" title="OK"></button>
                    <button class="hostlist-status-filter warning" data-type="service" data-status="warning" title="Warning"></button>
                    <button class="hostlist-status-filter critical" data-type="service" data-status="critical" title="Critical"></button>
                    <button class="hostlist-status-filter unknown" data-type="service" data-status="unknown" title="Unknown"></button>
                    <button class="hostlist-status-filter pending" data-type="service" data-status="pending" title="Pending"></button>
                    <span class="status-filter-reset" id="resetAllFilters" title="Reset all filters"><i class="fa fa-refresh" aria-hidden="true"></i></span>
                </div>
            </div>
        </div>

        <div class="header-content">
            <div class="hamburger">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </div>
            <div class="header-buttons">
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="viewDropdownBtn"><i class="fa fa-eye"></i> View <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="viewDropdownContent">
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'bubble', 'https://<?php echo $_SERVER['HTTP_HOST']; ?>/bubblemaps/infra.php')"><i class="fa fa-th"></i> Infrastructure Mapping</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'list', 'hostlist.php')"><i class="fa fa-list"></i> Asset Overview</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'dashboards', 'networkmap.php')"><i class="fa fa-map"></i> Dashboards and Visualization</a>
                    </div>
                </div>
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="menuDropdownBtn"><i class="fa fa-bars"></i> Control Panel <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="menuDropdownContent">
                        <a class="header-button" id="formModal-button"><i class="fa fa-search"></i> Scan</a>
                        <a class="header-button" onclick='showModal("https://<?php echo $_SERVER["HTTP_HOST"]; ?>/bubblemaps/credentials.php")'><i class="fa fa-key"></i> Credentials</a>
                        <a class="header-button" href="notifications.php"><i class="fa fa-bell"></i> Notifications</a>
                        <a class="header-button" href="reports.php"><i class="fa fa-file-text"></i> Reports</a>
                    </div>
                </div>
                <a class="header-button" id="openSettingsBtn"><i class="fa fa-cog"></i> Advanced Settings</a>
            </div>
        </div>
    </header>

    <?php include "settingsModal.php"; ?>

            <div class="hostlist-search-container">
            <div class="search-input-wrapper">
                <label for="searchBar-tableview">Search:</label>
                <input type="text" id="searchBar-tableview" placeholder="Search hosts...">
                <span class="search-actions">
                    <button id="tableview-clear-search" class="hostlist-clear-search">&times;</button>
                </span>
            </div>
            <div class="search-mode-selector">
                <select id="search-mode-tableview">
                    <option value="host" selected>Search Hosts</option>
                    <option value="service">Search Services</option>
                </select>
            </div>
            <div class="hostgroup-selector">
                <label for="hostgroup-filter">Hostgroup:</label>
                <select id="hostgroup-filter">
                    <option value="all" selected>All Hostgroups</option>
                </select>
            </div>
            <div class="servicegroup-selector">
                <label for="servicegroup-filter">Service Group:</label>
                <select id="servicegroup-filter">
                    <option value="all" selected>All Service Groups</option>
                </select>
            </div>
            <div class="table-order-controls">
                <button id="adjustOrderBtn" class="generate-btn" title="Adjust hostgroup order">
                    <i class="fa fa-sort"></i> Adjust Hostgroup Order
                </button>
            </div>
        </div>

    <div class="hostlist-container">
        <div id="tableview-content" class="tableview-content">
            <div class="hostlist-loading">
                <div class="spinner"></div>
                <div>Loading grouped hosts and services...</div>
            </div>
        </div>
    </div>

    <!-- iframe Modal -->
    <div id="infoModal" class="iframeModal">
        <div class="iframeModal-content" id="iframeModal-content">
            <span class="iframeMclose" title="Back" aria-label="Back">←</span>
            <div class="iframe-loader"></div>
            <iframe id="modal-frame"></iframe>
        </div>
    </div>

    <!-- Context Menu -->
    <div id="custom-context-menu" style="display:none; position:absolute; z-index:1001;"></div>

    <!-- Service Modal for context menu commands -->
    <div id="service-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modal-title"></h2>
                <span id="modal-delete" class="modal-delete" style="display: none;"><i class="fa fa-trash" aria-hidden="true"></i></span>
                <span id="modal-options" class="modal-options"><i class="fa fa-ellipsis-v" aria-hidden="true"></i></span>
                <span id="modal-close" class="modal-close">×</span>
            </div>
            <div id="modal-body" class="modal-body"></div>
        </div>
    </div>

    <script>
        const urlParams = new URLSearchParams(window.location.search);
        const dictionaries = <?php echo json_encode($dictionaries); ?>;
        const selectedLang = '<?php echo $selectedLang; ?>';


        
        // Initialize the translator
        const translator = new Translator(dictionaries, selectedLang);
        translator.init();
        initTranslationObserver(translator);

        // Initialize scan modal
        window.scanModal = new ScanModal({
            modalId: 'formModal',
            hasAzureSupport: true,
            hasScanTypeSelector: true,
            hasInternalCheckbox: true,
            ipLabel: 'Enter a single ip, ip range or url.',
            forceScan: true
        });

        // Handle hamburger menu
        const hamburger = document.querySelector('.hamburger');
        const headerButtons = document.querySelector('.header-buttons');
        hamburger.addEventListener('click', function(e) {
            e.stopPropagation();
            this.classList.toggle('active');
            headerButtons.classList.toggle('active');
        });
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.header-content')) {
                hamburger.classList.remove('active');
                headerButtons.classList.remove('active');
            }
        });

        // View dropdown
        const viewDropdownBtn = document.getElementById('viewDropdownBtn');
        const viewDropdownContent = document.getElementById('viewDropdownContent');
        if(viewDropdownBtn && viewDropdownContent){
            viewDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                const menuDropdownContent = document.getElementById('menuDropdownContent');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
                viewDropdownContent.classList.toggle('show');
            });
        }

        // Control Panel dropdown
        const menuDropdownBtn = document.getElementById('menuDropdownBtn');
        const menuDropdownContent = document.getElementById('menuDropdownContent');
        if(menuDropdownBtn && menuDropdownContent){
            menuDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                menuDropdownContent.classList.toggle('show');
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e){
            if(!e.target.closest('.header-dropdown')){
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
            }
        });
    </script>

    <!-- Host Status Popover -->
    <div class="status-hosts-popover" id="feature-issue-popover">
        <h4 id="popover-title">Details</h4>
        <ul id="popover-list"></ul>
    </div>

</body>
</html>


