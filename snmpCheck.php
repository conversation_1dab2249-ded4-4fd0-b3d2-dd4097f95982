<?php
include "loadenv.php";

class NetworkScanner {
    private $host;
    private $outputMessages = [];

    public function __construct($host) {
        $this->host = $host;
    }

    // Check if IP is valid
    public function isValidIp() {
        return filter_var($this->host, FILTER_VALIDATE_IP) !== false;
    }

    // Check if network firewall allows pings using fping
    public function isNetworkPingAllowed() {
        // First, try pinging the specific host
        $command = "/usr/sbin/fping " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output);
        // Check if the output contains "is alive"
        foreach ($output as $line) {
            if (strpos($line, "is alive") !== false) {
                return true; // Host responded
            }
        }

        // Extract the subnet and scan the range if the host didn't respond
        $ipParts = explode('.', $this->host);
        if (count($ipParts) !== 4) {
            return false; // Invalid IP format
        }
        
        $subnetPrefix = "{$ipParts[0]}.{$ipParts[1]}.{$ipParts[2]}";
        $scanRange = "{$subnetPrefix}.2 {$subnetPrefix}.253";

        // Scan the subnet range
        $scanCommand = "/usr/sbin/fping -r 0 -g $scanRange 2>&1";
        $scanOutput = [];
        exec($scanCommand, $scanOutput);

        // Check if any host in the range responded
        foreach ($scanOutput as $line) {
            if (strpos($line, "is alive") !== false) {
                return true;
            }
        }

        return false; // No hosts responded, likely blocked
    }

    // Check if device firewall responds to pings
    public function isDevicePingResponsive() {
        $command = "/usr/sbin/fping " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output);
        // Check if the output contains "is alive"
        foreach ($output as $line) {
            if (strpos($line, "is alive") !== false) {
                return true; // Host responded
            }
        }
        return false;
    }

    // Check if UDP port 161 is open
    public function isSnmpPortOpen() {
        $command = "sudo nmap -sU -p 161 -Pn " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output, $exitCode);
        $result = implode("\n", $output);

        return strpos($result, '161/udp open') !== false;
    }

    // Validate SNMP v1/v2c community string
    public function isCommunityStringValid($community, $version = 'v1') {
        if (!$this->isSnmpPortOpen()) {
            return false;
        }

        $oid = "*******.*******.0"; // sysDescr
        $command = "snmpget -$version -c " . escapeshellarg($community) . " " . 
                  escapeshellarg($this->host) . " $oid 2>&1";
        $output = [];
        exec($command, $output, $exitCode);
        $result = implode("\n", $output);

        return $exitCode === 0 && !empty($result) && 
               strpos($result, 'Timeout') === false && 
               strpos($result, 'No Such') === false;
    }

    // Check SNMP v1/v2 functionality
    public function checkSnmpV1V2() {
        $communityStrings = $this->getSnmpCommunities();
        if (empty($communityStrings)) {
            return false;
        }

        $versions = ['v1', 'v2c'];
        foreach ($communityStrings as $community) {
            foreach ($versions as $version) {
                if ($this->isCommunityStringValid($community, $version)) {
                    return true;
                }
            }
        }
        return false;
    }

    // Validate SNMPv3 credentials
    public function isSnmpV3CredentialsValid($cred) {
        if (!$this->isSnmpPortOpen()) {
            return false;
        }

        $oid = "*******.*******.0"; // sysDescr
        $username       = escapeshellarg($cred['snmp_username']);
        $authPassword   = escapeshellarg($cred['snmp_password']);
        $authProtocol   = escapeshellarg($cred['snmp_auth_protocol']);
        $privPassphrase = escapeshellarg($cred['snmp_priv_passphrase']);
        $privProtocol   = escapeshellarg($cred['snmp_priv_protocol']);

        $command = "snmpget -v3 -l authPriv -u $username -a $authProtocol -A $authPassword -x $privProtocol -X $privPassphrase " . 
                  escapeshellarg($this->host) . " $oid 2>&1";
        $output = [];
        exec($command, $output, $exitCode);
        $result = implode("\n", $output);

        return $exitCode === 0 && !empty($result) && 
               strpos($result, 'Timeout') === false && 
               strpos($result, 'No Such') === false;
    }

    // Check SNMPv3 functionality
    public function checkSnmpV3() {
        $credentials = $this->getSnmpv3Credentials();
        if (!$credentials || empty($credentials)) {
            return false;
        }

        foreach ($credentials as $cred) {
            if ($this->isSnmpV3CredentialsValid($cred)) {
                return true;
            }
        }
        return false;
    }

    function getSnmpCommunities() {
        $host = $_ENV["DB_SERVER"];
        $dbname = "ndd";
        $username = $_ENV["DB_USER"];
        $password = $_ENV["DB_PASSWORD"];
    
        try {
            // Create a new PDO connection
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
            // Query to select all communityro values
            $stmt = $pdo->query("SELECT communityro FROM csnmp");
    
            // Fetch results as an array
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            // Handle database connection errors
            return ["Error: " . $e->getMessage()];
        }
    }    

    function getSnmpv3Credentials() {
        // Database connection parameters
        $host   = $_ENV["DB_SERVER"];
        $dbname = 'ndd';
        $user   = $_ENV["DB_USER"];
        $pass   = $_ENV["DB_PASSWORD"];
        
        try {
            // Establish a new PDO connection
            $pdo = new PDO("mysql:host={$host};dbname={$dbname};charset=utf8mb4", $user, $pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
            // Prepare and execute the query
            $sql  = "SELECT snmp_username, snmp_password, snmp_auth_protocol, snmp_priv_passphrase, snmp_priv_protocol FROM csnmpv3";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
    
            // Fetch and return all rows as an associative array
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // Log the error and return false on failure
            error_log("Database error: " . $e->getMessage());
            return false;
        }
    }

    // Generate output messages based on test results with check/cross marks
    public function generateOutput() {
        // Ping checks
        if ($this->isNetworkPingAllowed()) {
            $this->outputMessages[] = "✓ Ping traffic to {$this->host} is allowed through the firewall.";
        } else {
            $this->outputMessages[] = "✗ The firewall on the network does not let pings pass to IP {$this->host}";
            return implode("\n", $this->outputMessages);
        }

        if ($this->isDevicePingResponsive()) {
            $this->outputMessages[] = "✓ The device is reachable via ping.";
        } else {
            $this->outputMessages[] = "✗ The device does not respond to pings.";
            return implode("\n", $this->outputMessages);
        }

        // SNMP checks
        if ($this->isSnmpPortOpen()) {
            $this->outputMessages[] = "✓ The SNMP port (161) is open on {$this->host}";
        
            // SNMP v1/v2 check first
            if ($this->checkSnmpV1V2()) {
                $this->outputMessages[] = "✓ The community string is configured and correct on the equipment.";
                // SNMPv1/v2 works, so we don't need to check or show SNMPv3 results
            } else {
                // SNMPv1/v2 failed, try SNMPv3
                if ($this->checkSnmpV3()) {
                    $this->outputMessages[] = "✓ The SNMPv3 credentials are configured and correct on the equipment.";
                    // SNMPv3 works, so we don't need to show SNMPv1/v2 failure
                } else {
                    // Both SNMPv1/v2 and SNMPv3 failed, show both error messages
                    $this->outputMessages[] = "✗ The community string is not configured or is not the correct one on the equipment.";
                    $this->outputMessages[] = "✗ The SNMPv3 credentials are not configured or are not the correct ones on the equipment.";
                }
            }
        } else {
            $this->outputMessages[] = "✗ The SNMP port (161) is closed or blocked by the firewall on {$this->host}";
        }        

        return implode("\n", $this->outputMessages);
    }

    // Main scanning function
    public function scan() {
        if (!$this->isValidIp()) {
            return "✗ Invalid IP address provided\n• Use a valid IP like ***********";
        }

        return $this->generateOutput();
    }
}

// Usage
if (isset($_GET['host'])) {
    $scanner = new NetworkScanner($_GET['host']);
    echo $scanner->scan();
} else {
    echo "✗ No host provided\n• Add a host parameter, e.g., ?host=*******";
}

?>