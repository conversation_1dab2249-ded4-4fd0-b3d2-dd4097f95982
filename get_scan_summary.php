<?php
session_start();

header('Content-Type: application/json');

// Function to get scan summary from session
function getScanSummary() {
    if (isset($_SESSION['scanSummary'])) {
        $summary = $_SESSION['scanSummary'];
        unset($_SESSION['scanSummary']); // Clear after retrieval
        return $summary;
    }
    return null;
}

$scanSummary = getScanSummary();

if ($scanSummary) {
    echo json_encode([
        'success' => true,
        'data' => $scanSummary
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'No scan summary available'
    ]);
}
?>
