<?php
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $viewType = $_POST['view'] ?? '';
    
    if (empty($viewType)) {
        echo json_encode(['success' => false, 'error' => 'No view type provided']);
        exit;
    }
    
    $config_file = 'conf/bubblemap_configs';
    
    // Read the current configuration
    $config_content = '';
    if (file_exists($config_file)) {
        $config_content = file_get_contents($config_file);
    }
    
    // Split into lines and preserve all existing content
    $lines = explode("\n", $config_content);
    
    // Remove empty lines at the end
    while (!empty($lines) && trim(end($lines)) === '') {
        array_pop($lines);
    }
    
    // Check if "main view" line exists and update it
    $main_view_exists = false;
    foreach ($lines as $index => $line) {
        $trimmed_line = trim($line);
        if (strpos($trimmed_line, 'main view:') === 0) {
            $main_view_exists = true;
            // Update the existing line
            $lines[$index] = "main view: $viewType";
            break;
        }
    }
    
    // If "main view" doesn't exist, add it
    if (!$main_view_exists) {
        $lines[] = "main view: $viewType";
    }
    
    // Write back to the configuration file with proper newline at the end
    $new_config_content = implode("\n", $lines) . "\n";
    $result = file_put_contents($config_file, $new_config_content);
    
    if ($result !== false) {
        echo json_encode(['success' => true, 'view' => $viewType]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to write to config file']);
    }
} else {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
}
?>
