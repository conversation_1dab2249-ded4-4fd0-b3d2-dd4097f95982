<?php
include "loadenv.php";

class VMwareScanner {
    private $host;
    private $outputMessages = [];

    public function __construct($host) {
        $this->host = $host;
    }

    // Check if IP is valid
    public function isValidIp() {
        return filter_var($this->host, FILTER_VALIDATE_IP) !== false;
    }

    // Check if network firewall allows pings using fping
    public function isNetworkPingAllowed() {
        // First, try pinging the specific host
        $command = "/usr/sbin/fping " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output);
        // Check if the output contains "is alive"
        foreach ($output as $line) {
            if (strpos($line, "is alive") !== false) {
                return true; // Host responded
            }
        }

        // Extract the subnet and scan the range if the host didn't respond
        $ipParts = explode('.', $this->host);
        if (count($ipParts) !== 4) {
            return false; // Invalid IP format
        }
        
        $subnetPrefix = "{$ipParts[0]}.{$ipParts[1]}.{$ipParts[2]}";
        $scanRange = "{$subnetPrefix}.2 {$subnetPrefix}.253";

        // Scan the subnet range
        $scanCommand = "/usr/sbin/fping -r 0 -g $scanRange 2>&1";
        $scanOutput = [];
        exec($scanCommand, $scanOutput);

        // Check if any host in the range responded
        foreach ($scanOutput as $line) {
            if (strpos($line, "is alive") !== false) {
                return true;
            }
        }

        return false; // No hosts responded, likely blocked
    }

    // Check if device firewall responds to pings
    public function isDevicePingResponsive() {
        $command = "/usr/sbin/fping " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output);
        // Check if the output contains "is alive"
        foreach ($output as $line) {
            if (strpos($line, "is alive") !== false) {
                return true; // Host responded
            }
        }
        return false;
    }

    // Check if HTTPS port 443 is open
    public function isHttpsPortOpen() {
        $command = "sudo nmap -p 443 -Pn " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output, $exitCode);
        $result = implode("\n", $output);

        return strpos($result, '443/tcp open') !== false;
    }

    // Get VMware credentials from database
    private function getVmwareCredentials() {
        $host = $_ENV["DB_SERVER"];
        $dbname = "ndd";
        $username = $_ENV["DB_USER"];
        $password = $_ENV["DB_PASSWORD"];
    
        try {
            // Create a new PDO connection
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
            // Query to select all VMware credentials
            $stmt = $pdo->query("SELECT username, password FROM cvmware ORDER BY position ASC");
    
            // Fetch results as an associative array
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // Handle database connection errors
            return [];
        }
    }

    // Test HTTPS connection with VMware credentials using curl
    public function testVmwareConnection($username, $password) {
        if (!$this->isHttpsPortOpen()) {
            return false;
        }

        // Test vSphere API connectivity using the vimService.wsdl endpoint
        $testUrl = "https://{$this->host}/sdk/vimService.wsdl";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $testUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Check if we got a response (even if it's an error, it means the service is there)
        if ($error) {
            return false;
        }

        // If we get any response from the VMware vSphere API endpoint, consider it a success
        // VMware typically returns 401 for invalid credentials or 200 for valid ones
        return $httpCode == 200 || $httpCode == 401;
    }

    // Check VMware functionality
    public function checkVmwareConnection() {
        $credentials = $this->getVmwareCredentials();
        if (empty($credentials)) {
            return false;
        }

        foreach ($credentials as $cred) {
            if (!empty($cred['username']) && !empty($cred['password'])) {
                if ($this->testVmwareConnection($cred['username'], $cred['password'])) {
                    return true;
                }
            }
        }
        return false;
    }

    // Generate output messages
    public function generateOutput() {
        // Ping checks
        if ($this->isNetworkPingAllowed()) {
            $this->outputMessages[] = "✓ Ping traffic to {$this->host} is allowed through the firewall.";
        } else {
            $this->outputMessages[] = "✗ The firewall on the network does not let pings pass to IP {$this->host}";
            return implode("\n", $this->outputMessages);
        }

        if ($this->isDevicePingResponsive()) {
            $this->outputMessages[] = "✓ The device is reachable via ping.";
        } else {
            $this->outputMessages[] = "✗ The device does not respond to pings.";
            return implode("\n", $this->outputMessages);
        }

        // HTTPS checks
        if ($this->isHttpsPortOpen()) {
            $this->outputMessages[] = "✓ TCP port 443 (HTTPS) is reachable and open on {$this->host}";
        
            // VMware connection check
            if ($this->checkVmwareConnection()) {
                $this->outputMessages[] = "✓ The VMware HTTPS credentials are properly configured and validated on the device.";
            } else {
                $this->outputMessages[] = "✗ VMware HTTPS credentials are not configured or are not the correct ones on the equipment.";
            }
        } else {
            $this->outputMessages[] = "✗ The HTTPS port (443) is closed or blocked by the firewall on {$this->host}";
        }        

        return implode("\n", $this->outputMessages);
    }

    // Main scanning function
    public function scan() {
        if (!$this->isValidIp()) {
            return "✗ Invalid IP address provided\n• Use a valid IP like ***********";
        }

        return $this->generateOutput();
    }
}

// Usage
if (isset($_GET['host'])) {
    $scanner = new VMwareScanner($_GET['host']);
    echo $scanner->scan();
} else {
    echo "✗ No host provided\n• Add a host parameter, e.g., ?host=*******";
}
?> 