<?php
// Shared status indicators component
// Usage:
//   include 'components/statusIndicators.php';
//   renderStatusIndicators(); // desktop
//   renderStatusIndicators(true); // mobile variant (IDs suffixed with -mobile)

if (!function_exists('renderStatusIndicators')) {
    function renderStatusIndicators($isMobile = false) {
        $suffix = $isMobile ? '-mobile' : '';
        ?>
        <div class="hostlist-status-indicators">
            <div class="status-indicator" id="feature-flap-detection-status<?php echo $suffix; ?>" title="Flap Detection (Hosts & Services)">
                <i class="fa fa-flag"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-notifications-status<?php echo $suffix; ?>" title="Notifications (Hosts & Services)">
                <i class="fa fa-bell"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-event-handlers-status<?php echo $suffix; ?>" title="Event Handlers (Hosts & Services)">
                <i class="fa fa-cogs"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-active-checks-status<?php echo $suffix; ?>" title="Active Checks (Hosts & Services)">
                <i class="fa fa-check-circle"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-passive-checks-status<?php echo $suffix; ?>" title="Passive Checks (Hosts & Services)">
                <i class="fa fa-eye"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-acknowledged-problems-status<?php echo $suffix; ?>" title="Acknowledged Problems (Hosts & Services)">
                <i class="fa fa-gavel"></i>
                <span class="indicator-badge"></span>
            </div>
        </div>
        <?php
    }
}
?>


