<?php
include "loadenv.php";

// Database configuration
define('DB_HOST', $_ENV["DB_SERVER"]);
define('DB_USER', $_ENV["DB_USER"]);
define('DB_PASS', $_ENV["DB_PASSWORD"]);
define('DB_NAME', 'bubblemaps');

// Get database connection
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get request parameters
$hostgroup = isset($_GET['hostgroup']) ? $_GET['hostgroup'] : null;
$hostIds = isset($_GET['ids']) ? $_GET['ids'] : null;
$infra = isset($_GET['infra']) ? $_GET['infra'] : null;
$mode = isset($_GET['mode']) ? $_GET['mode'] : null;



// Handle subnet_counts mode for infrastructure-wide counts
if ($mode === 'subnet_counts' && $infra) {
    // Get counts for entire infrastructure
    // Count hosts by status - treat anything not down/unreachable/pending as "up"
    $hostQuery = "
        SELECT 
            SUM(CASE WHEN apmStatus = 'down' THEN 1 ELSE 0 END) AS down,
            SUM(CASE WHEN apmStatus = 'unreachable' THEN 1 ELSE 0 END) AS unreachable,
            SUM(CASE WHEN apmStatus = 'pending' THEN 1 ELSE 0 END) AS pending,
            SUM(CASE WHEN apmStatus NOT IN ('down', 'unreachable', 'pending') THEN 1 ELSE 0 END) AS up
        FROM hosts 
        WHERE infra = ? AND blacklist = 0 AND apmStatus != 'ask'
    ";
    
    $hostStmt = $conn->prepare($hostQuery);
    $hostStmt->bind_param("s", $infra);
    $hostStmt->execute();
    $hostResult = $hostStmt->get_result();
    $hostCounts = $hostResult->fetch_assoc();
    
    // Get service counts for the entire infrastructure
    $serviceQuery = "
        SELECT 
            SUM(pending_count) AS pending,
            SUM(ok_count) AS ok,
            SUM(warning_count) AS warning,
            SUM(unknown_count) AS unknown,
            SUM(critical_count) AS critical
        FROM hosts 
        WHERE infra = ? AND blacklist = 0 AND apmStatus != 'ask'
    ";
    
    $serviceStmt = $conn->prepare($serviceQuery);
    $serviceStmt->bind_param("s", $infra);
    $serviceStmt->execute();
    $serviceResult = $serviceStmt->get_result();
    $serviceCounts = $serviceResult->fetch_assoc();
    
    // Format response in the expected format for updateHostAndServiceCounts
    $response = [
        'hostCounts' => $hostCounts,
        'serviceCounts' => $serviceCounts
    ];
    
    header('Content-Type: application/json');
    echo json_encode($response);
    
    $hostStmt->close();
    $serviceStmt->close();
    $conn->close();
    exit;
}

// Standard host status query processing
// Base query - includes status count fields used for the badge count (sum of non-OK statuses)
$query = "SELECT id, ip, apmStatus, pending_count, ok_count, warning_count, unknown_count, critical_count, hostgroup 
          FROM hosts 
          WHERE blacklist = 0 AND apmStatus != 'ask'";

// Add infra filtering if provided
if ($infra) {
    $infraEscaped = $conn->real_escape_string($infra);
    $query .= " AND infra = '$infraEscaped'";
}

// If both hostgroup and host IDs are provided, prioritize host IDs for precise filtering
if ($hostIds) {
    // Clean and validate the host IDs (expected format: comma-separated list of integers)
    $idArray = array_filter(array_map('intval', explode(',', $hostIds)));
    
    if (!empty($idArray)) {
        $safeIds = implode(',', $idArray);
        $query .= " AND id IN ($safeIds)";
    }
} elseif ($hostgroup) {
    // If no specific host IDs, but hostgroup is provided, filter by hostgroup
    $hostgroupEscaped = $conn->real_escape_string($hostgroup);
    
    // First try with exact matching using JSON_CONTAINS with quoted string
    $query .= " AND (JSON_CONTAINS(hostgroup, '\"$hostgroupEscaped\"') 
                OR hostgroup LIKE '%$hostgroupEscaped%')";
}

// For large results, consider adding a LIMIT clause
// $query .= " LIMIT 1000";

$result = $conn->query($query);

if (!$result) {
    error_log("Query failed: " . $conn->error);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Database query failed: ' . $conn->error]);
    $conn->close();
    exit;
}

$hosts = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $hosts[] = $row;
    }
}



$conn->close();

// Return JSON response
header('Content-Type: application/json');
echo json_encode($hosts);
?> 