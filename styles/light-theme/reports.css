/* Reports Page - Light Theme */

.reports-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #ffffff;
    overflow: visible;
}

/* Controls bar */
.reports-controls {
    padding: 12px 18px;
    background-color: #403c3c;
    border-bottom: 1px solid #555;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.controls-row {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-group label {
    display: inline-block;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-right: 8px;
    font-weight: 500;
    white-space: nowrap;
}

.control-group input[type="datetime-local"],
.control-group input[type="text"],
.control-group select {
    background-color: #333;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    padding: 6px 10px;
    font-size: 14px;
    height: 32px;
}

/* Calendar picker icon styling for light theme */
.control-group input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    filter: invert(1) brightness(1.5);
    opacity: 0.9;
    cursor: pointer;
}

.control-group input[type="datetime-local"]::-moz-calendar-picker-indicator {
    filter: invert(1) brightness(1.8);
    opacity: 0.9;
    cursor: pointer;
}

/* Search input specific styling */
.search-input-wrapper {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
}

#report-search {
    min-width: 250px;
    background-color: #333;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    padding: 8px 40px 8px 15px;
    font-size: 14px;
    height: 22px;
    flex: 1;
}

#report-search:focus {
    outline: none;
    background-color: #444;
}

#report-search::placeholder {
    color: #ccc;
}

.search-actions {
    display: flex;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    align-items: center;
}

.report-clear-search {
    background: transparent;
    border: none;
    color: #ccc;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.report-clear-search:hover {
    color: #fff;
}

.report-clear-search.visible {
    opacity: 1;
    visibility: visible;
}

.generate-btn {
    background: transparent;
    border: 1px solid #777;
    color: #ccc;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.generate-btn:hover {
    background-color: #555;
    color: #fff;
}

/* Vertical separator */
.vertical-separator {
    width: 1px;
    height: 32px;
    background-color: #555;
    margin: 0 8px;
    flex-shrink: 0;
}

/* Content */
.reports-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 18px 18px;
    background-color: #ffffff;
    margin-bottom: 20px;
    max-height: calc(100vh - 200px);
}

.reports-loading,
.reports-empty,
.reports-error,
.reports-placeholder {
    text-align: center;
    color: #777;
    font-size: 16px;
    padding: 40px 20px;
}

.reports-error {
    color: #d41d28;
}

/* Table */
.report-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 14px;
}

.report-table thead {
    position: sticky;
    top: -1px;
    background-color: #f5f5f5;
    z-index: 5;
    padding-top: 1px;
}

.report-table th,
.report-table td {
    padding: 10px 14px;
    text-align: left;
    vertical-align: middle;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #333;
}

.report-table tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Status colours */
.ok { color: #4CAF50; }
.warning { color: #FFC107; }
.critical { color: #F44336; }
.unknown { color: #64748b; }

/* Report Summary */
.report-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.summary-title {
    margin: 0 0 15px 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

.summary-time-range {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    text-align: center;
    font-style: italic;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
}

.summary-stat:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
}

.summary-stat.warning {
    border-left: 4px solid #FFC107;
    background: linear-gradient(135deg, #fff8e1 0%, #fff3cd 100%);
}

.summary-stat.critical {
    border-left: 4px solid #F44336;
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
}

.summary-stat.unknown {
    border-left: 4px solid #64748b;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.stat-label {
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.summary-stat.warning .stat-value {
    color: #e65100;
}

.summary-stat.critical .stat-value {
    color: #c62828;
}

.summary-stat.unknown .stat-value {
    color: #37474f;
}

/* Pie chart styles */
.summary-pie-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin-top: 15px;
}

.pie-chart-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.pie-chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 10px;
}

.pie-legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.pie-legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    border: 1px solid #ddd;
}

.pie-legend-label {
    color: #333;
    font-weight: 500;
}

/* Charts container */
.charts-container {
    display: flex;
    gap: 30px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.chart-section {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.chart-title {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    text-align: center;
}

/* Marimekko chart styles */
.summary-marimekko-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;
}

.marimekko-chart-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.marimekko-empty {
    color: #777;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Marimekko chart click interactions */
.marimekko-chart-wrapper rect {
    transition: opacity 0.2s ease;
}

.marimekko-chart-wrapper rect:hover {
    opacity: 0.8;
}

/* Table row highlight effect */
.report-table tr.highlighted {
    background-color: rgba(255, 255, 0, 0.3) !important;
    transition: background-color 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .charts-container {
        flex-direction: column;
        gap: 20px;
    }

    .chart-section {
        min-width: unset;
    }
    
    .search-input-wrapper {
        width: 100%;
    }
    
    #report-search {
        min-width: auto;
        width: 100%;
    }
}

/* Hostgroup title */
.report-title {
    margin: 25px 0 10px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* Host link styling */
.host-link {
    color: #1976d2;
    text-decoration: none;
    transition: color 0.2s ease;
    cursor: pointer;
}

.host-link:hover {
    color: #1565c0;
    text-decoration: underline;
}

.host-link:active {
    color: #0d47a1;
}

/* Service link styling */
.service-link {
    color: #1976d2;
    text-decoration: none;
    transition: color 0.2s ease;
    cursor: pointer;
}

.service-link:hover {
    color: #1565c0;
    text-decoration: underline;
}

.service-link:active {
    color: #0d47a1;
}

/* Service description field styling */
#service-description-wrapper {
    transition: opacity 0.3s ease;
}

#service-description-wrapper.hidden {
    opacity: 0.5;
    pointer-events: none;
}

.report-title:first-of-type {
    margin-top: 0;
}

.reports-content > .report-title:first-child {
    margin-top: 0;
}

.report-table td.ok { background-color: rgba(76,175,80,0.12); }
.report-table td.critical { background-color: rgba(244,67,54,0.12); }
.report-table td.warning { background-color: rgba(255,193,7,0.12); }
.report-table td.unknown { background-color: rgba(100,116,139,0.12); }

.hostgroup-table { margin-bottom: 35px; }

/* Status filter buttons */
.status-row { justify-content: flex-start; }

.reports-status-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 6px 10px;
    align-items: center;
}

.reports-status-filter {
    cursor: pointer;
    border: none;
    border-radius: 4px;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    opacity: 0.8;
}

.reports-status-filter.ok { background:#4CAF50; color:#fff; }
.reports-status-filter.warning { background:#FFC107; color:#333; }
.reports-status-filter.critical { background:#F44336; color:#fff; }
.reports-status-filter.unknown { background:#64748b; color:#fff; }
.reports-status-filter.active { box-shadow: 0 0 0 2px #fff; opacity:1; }
.reports-status-filter:not(.active) { filter: grayscale(0.5) opacity(0.5); }

/* Scrollbar styles */
.reports-content {
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: #bbb #ffffff; /* thumb track */
}

.reports-content::-webkit-scrollbar {
    width: 8px;
}

.reports-content::-webkit-scrollbar-track {
    background: #ffffff;
}

.reports-content::-webkit-scrollbar-thumb {
    background-color: #bbb;
    border-radius: 4px;
}

.reports-content::-webkit-scrollbar-thumb:hover {
    background-color: #999;
}

/* Schedule Report Modal */
.sr-modal{display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);z-index:1000;align-items:center;justify-content:center;}
.sr-modal-content{background:#fdfdfd;padding:25px 30px;border-radius:8px;min-width:320px;max-width:1200px;max-height:90vh;color:#333;overflow-y:auto;}
.sr-modal-content h2{margin-top:0;margin-bottom:15px;font-size:20px;}
.sr-close{float:right;font-size:28px;cursor:pointer;color:#777;}
.sr-close:hover{color:#000;}
.sr-field{display:flex;flex-direction:column;gap:6px;margin-bottom:15px;}
.sr-field label{font-size:14px;color:#333;font-weight:500;}
.sr-field input, .sr-field select{background:#fff;border:1px solid #ddd;color:#333;padding:8px 12px;border-radius:4px;font-size:14px;transition:border-color 0.2s ease;}
.sr-field input:focus, .sr-field select:focus{outline:none;border-color:#007bff;box-shadow:0 0 0 2px rgba(0,123,255,0.1);}
.sr-actions{display:flex;justify-content:flex-end;gap:10px;margin-top:20px;padding-top:15px;border-top:1px solid #eee;}

/* Schedule modal enhancements */
.sr-status{font-size:14px;margin-bottom:6px;}
.sr-status.scheduled{color:#4CAF50;font-weight:600;}
.sr-status.not-scheduled{color:#d41d28;font-weight:600;}

/* Buttons inside modal - matching settings modal styles */
.sr-modal .generate-btn {
    background: #f1f5f9;
    background-color: #f1f5f9;
    color: #111827;
    padding: 6px 10px;
    min-width: 32px;
    height: 32px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.sr-modal .generate-btn:hover {
    background-color: #e2e8f0;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
}

/* Saved Reports Container (in modal) */
.saved-reports-container {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    min-height: 300px;
    max-height: 500px;
    overflow-y: auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Multi-select controls */
.saved-reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.saved-reports-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.saved-reports-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.select-all-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #777;
}

.select-all-checkbox input[type="checkbox"] {
    margin: 0;
}

.bulk-delete-btn {
    background: #dc3545;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: none;
}

.bulk-delete-btn:hover {
    background: #c82333;
}

.bulk-delete-btn.visible {
    display: inline-block;
}

/* Report item with checkbox */
.saved-report-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

.saved-report-item.selected {
    border-color: #007bff;
    background: #f8f9fa;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.saved-report-item:hover {
    background: #f8f9fa;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.report-checkbox {
    margin-right: 12px;
}

.report-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.report-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.report-date {
    font-size: 12px;
    color: #777;
    margin-bottom: 2px;
}

.report-details {
    font-size: 11px;
    color: #888;
}

.report-actions {
    display: flex;
    gap: 8px;
}

.report-action-btn {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #777;
}

.report-action-btn:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.view-btn:hover {
    color: #2196F3;
    border-color: #2196F3;
}

.download-btn:hover {
    color: #4CAF50;
    border-color: #4CAF50;
}

.delete-btn:hover {
    color: #f44336;
    border-color: #f44336;
}

.saved-reports-loading,
.saved-reports-empty {
    text-align: center;
    color: #777;
    font-size: 14px;
    padding: 20px;
}

.saved-reports-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Saved reports list scrollbar styling for light theme */
.saved-reports-container::-webkit-scrollbar {
    width: 8px;
}

.saved-reports-container::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
}

.saved-reports-container::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 4px;
    border: 2px solid #f8fafc;
}

.saved-reports-container::-webkit-scrollbar-thumb:hover {
    background-color: #555555;
}

.saved-report-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

.saved-report-item:hover {
    background: #f8f9fa;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.report-info {
    flex: 1;
}

.report-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.report-date {
    font-size: 12px;
    color: #777;
    margin-bottom: 2px;
}

.report-details {
    font-size: 11px;
    color: #888;
}

.report-actions {
    display: flex;
    gap: 8px;
}

.report-action-btn {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #777;
}

.report-action-btn:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.view-btn:hover {
    color: #2196F3;
    border-color: #2196F3;
}

.download-btn:hover {
    color: #4CAF50;
    border-color: #4CAF50;
}

.delete-btn:hover {
    color: #f44336;
    border-color: #f44336;
}

/* Saved reports badge */
.saved-reports-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: #fff;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    min-width: 18px;
}

/* Scheduled reports badge */
.scheduled-reports-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #007bff;
    color: #fff;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    min-width: 18px;
}

.generate-btn {
    position: relative;
}

/* Schedule modal enhancements */
.schedule-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    padding-right: 30px; /* Add space for the close button */
}

.schedule-modal-header h2 {
    margin: 0;
}

.scheduled-reports-search {
    margin: 20px 0;
    padding: 0 5px;
}

.scheduled-reports-search .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.scheduled-reports-search #scheduled-reports-search-input {
    width: 100%;
    padding: 10px 40px 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #fff;
    color: #333;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.scheduled-reports-search #scheduled-reports-search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.1);
}

.scheduled-reports-search #scheduled-reports-search-input::placeholder {
    color: #999;
}

.scheduled-reports-search .search-actions {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
}

.scheduled-reports-search .report-clear-search {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    font-size: 12px;
    transition: all 0.2s ease;
    display: none;
}

.scheduled-reports-search .report-clear-search:hover {
    background: #f0f0f0;
    color: #333;
}

.scheduled-reports-search .report-clear-search.visible {
    display: block;
}

.scheduled-reports-list {
    max-height: 500px;
    overflow-y: auto;
}

/* Scheduled reports list scrollbar styling for light theme */
.scheduled-reports-list::-webkit-scrollbar {
    width: 8px;
}

.scheduled-reports-list::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
}

.scheduled-reports-list::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 4px;
    border: 2px solid #f8fafc;
}

.scheduled-reports-list::-webkit-scrollbar-thumb:hover {
    background-color: #555555;
}

.scheduled-report-item {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.scheduled-report-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.scheduled-report-item.disabled {
    opacity: 0.6;
    background: #f8f9fa;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.report-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.report-status {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
}

.report-status.enabled {
    background: #d4edda;
    color: #155724;
}

.report-status.disabled {
    background: #f8d7da;
    color: #721c24;
}

.report-details {
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 8px;
    font-size: 14px;
    border-bottom: 1px solid #ddd;
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.detail-label {
    font-weight: 500;
    color: #666;
    min-width: 100px;
}

.detail-dots {
    display: none;
}

.detail-value {
    color: #333;
    text-align: right;
    min-width: 120px;
}

.report-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.report-action-btn {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #777;
}

.report-action-btn:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.toggle-btn:hover {
    color: #007bff;
    border-color: #007bff;
}

.edit-btn:hover {
    color: #007bff;
    border-color: #007bff;
}

.send-btn:hover {
    color: #28a745;
    border-color: #28a745;
}

.delete-btn:hover {
    color: #f44336;
    border-color: #f44336;
}

.scheduled-reports-loading,
.scheduled-reports-empty {
    text-align: center;
    color: #777;
    font-size: 14px;
    padding: 20px;
}

/* Add report form */
.add-report-form {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid #e9ecef;
}

.add-report-form h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .sr-field {
    flex: 1;
}

/* Checkbox group styling */
.sr-checkbox-group {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.sr-checkbox-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    cursor: pointer;
    color: #333;
}

.sr-checkbox-group input[type="checkbox"] {
    margin: 0;
}

/* Filters Modal Styles */
#filtersModal .sr-modal-content {
    background: #fdfdfd;
    color: #333;
}

#filtersModal .sr-field label {
    color: #333;
    font-weight: 500;
}

#filtersModal .sr-field input,
#filtersModal .sr-field select {
    background: #fff;
    border: 1px solid #ddd;
    color: #333;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

#filtersModal .sr-field input:focus,
#filtersModal .sr-field select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.1);
}

/* Calendar picker icon styling for filters modal */
#filtersModal input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    filter: none;
    opacity: 0.7;
    cursor: pointer;
}

#filtersModal input[type="datetime-local"]::-moz-calendar-picker-indicator {
    filter: none;
    opacity: 0.7;
    cursor: pointer;
}

/* Day selection field styling */
#sr-day-selection-row,
#edit-sr-day-selection-row {
    transition: all 0.3s ease;
}

#sr-weekday-field,
#sr-monthday-field,
#edit-sr-weekday-field,
#edit-sr-monthday-field {
    transition: opacity 0.3s ease;
}

#sr-weekday-field.hidden,
#sr-monthday-field.hidden,
#edit-sr-weekday-field.hidden,
#edit-sr-monthday-field.hidden {
    opacity: 0.5;
    pointer-events: none;
}

/* Time shortcuts styling */
.time-shortcuts {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 5px;
}

.time-shortcut-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.time-shortcut-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    color: #212529;
}

.time-shortcut-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}
