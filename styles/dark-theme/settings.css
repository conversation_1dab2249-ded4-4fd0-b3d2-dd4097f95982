:root {
    --primary: #888;
    --pending: #808080;
    --success: #a0a0a0;
    --warning: #808080;
    --critical: #555;
    --unknown: #707070;
    --surface: #222;
    --background: #2f2f2f;
    --text: #f8f9fa;
    --text-secondary: #ccc;
    --border: #717171;
    --radius: 16px;
    --shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.2);
    --pending-bg: #373737;
    --success-bg: #3a3a3a;
    --warning-bg: #404040;
    --critical-bg: #333333;
    --unknown-bg: #353535;
    --brand-color: #cde06b;
}

/* Modal Styles (incorporating provided styles) */
.settings-modal{
    display: none;
    /* Hidden by default */
    font-family: 'Calibri', sans-serif;
    line-height: 1.6;
    font-weight: 400;
    color: var(--text);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: 2000;
    align-items: center;
    justify-content: center;
    opacity: 0;
    /* Start hidden for transition */
    transition: opacity 0.3s ease;
    padding: 20px;
    /* Padding around the modal content */
}

.settings-modal.show {
    display: flex;
    /* Use flex for centering */
    opacity: 1;
}

.settings-modal-content {
    background: var(--surface);
    /* Changed from --background for contrast */
    border-radius: var(--radius);
    width: 90%;
    max-width: 1024px;
    /* Increased max-width for better layout */
    padding: 0;
    /* Remove padding, handle inside */
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
    border: 1px solid var(--border);
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    /* Prevent content overflow */
    height: 85vh;
    /* Fixed height */
    max-height: 700px;
    /* Optional max height */
}

.settings-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 24px;
    border-bottom: 1px solid var(--border);
    flex-shrink: 0;
    /* Prevent header from shrinking */
}

.settings-modal-title {
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    color: var(--text);
    letter-spacing: -0.025em;
}

.settings-modal-close {
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 24px;
    /* Slightly larger for easier clicking */
    line-height: 1;
    transition: color 0.2s ease;
    padding: 5px;
}

.settings-modal-close:hover {
    color: var(--text);
}

.modal-body-container {
    display: flex;
    flex-grow: 1;
    /* Allow body to fill remaining space */
    overflow: hidden;
    /* Prevent internal overflow issues */
}

/* Top Tabs */
.modal-top-tabs {
    display: flex;
    padding: 0 24px;
    border-bottom: 1px solid var(--border);
    background-color: var(--surface);
    /* Match modal content bg */
    flex-shrink: 0;
    /* Prevent shrinking */
}

.top-tab-link {
    padding: 12px 20px;
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 16px;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: color 0.2s ease, border-color 0.2s ease;
    margin-bottom: -1px;
    /* Overlap border */
    text-decoration: none;
    white-space: nowrap;
}

.top-tab-link:hover {
    color: var(--text);
}

.top-tab-link.active {
    color: #cccccc;
    border-bottom-color: #888888;
}

/* Left Sub-Tabs Navigation */
.modal-sub-tabs-nav {
    width: 200px;
    flex-shrink: 0;
    background: var(--background);
    /* Darker background for contrast */
    padding: 15px 0;
    border-right: 1px solid var(--border);
    overflow-y: auto;
    /* Scroll if too many sub-tabs */
}

.sub-tabs-container {
    display: none;
    /* Hide sub-tab groups by default */
    list-style: none;
    padding: 0;
    margin: 0;
}

/* Log message highlighting */
.log-warning {
    color: orange;
}

.log-error,
.log-critical {
    color: red;
}

.log-line-count {
    display: block;
    text-align: right;
    color: var(--text-secondary);
    font-size: 0.9em;
    margin-top: 5px;
    margin-bottom: 10px;
    border-bottom: 1px solid var(--border);
    padding-bottom: 5px;
}

/* Style for log file headers in System Logs */
.log-header {
    display: block; /* Ensures it takes its own line */
    font-weight: bold;
    color: var(--primary); /* Use primary theme color */
    background-color: var(--background-alt); /* Subtle background highlight */
    padding: 2px 5px;
    margin-top: 10px; /* Add some space above headers */
    border-radius: 4px;
}

/* Log Display Styles */
.log-display-container {
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 15px;
    max-height: 600px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 13px;
    color: var(--text-secondary);
}

/* Log Display Scrollbar Styling */
.log-display-container::-webkit-scrollbar {
    width: 8px;
}

.log-display-container::-webkit-scrollbar-track {
    background: var(--background);
    border-radius: 4px;
}

.log-display-container::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 4px;
    border: 2px solid var(--background);
}

.log-display-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--primary);
}

/* Log Section Styles */
.log-section {
    margin-bottom: 20px;
}

.log-section:last-child {
    margin-bottom: 0;
}

.log-section-content {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
    font-family: monospace;
    font-size: 13px;
    color: var(--text-secondary);
    white-space: pre-wrap;
    word-break: break-word;
    overflow: visible;
}

.sub-tabs-container.active {
    display: block;
    /* Show active sub-tab group */
}

/* Style for sub-tab section headings */
.sub-tab-heading {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 600;
    padding: 15px 20px 5px 20px; /* Adjusted for icon */
    text-transform: uppercase;
    letter-spacing: 0.5px;
    /* Modifications for Accordion */
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    transition: background-color 0.2s ease;
}

.sub-tab-heading:hover {
     background-color: rgba(255, 255, 255, 0.05); /* Subtle hover */
}

/* Style the accordion icon */
.sub-tab-heading .accordion-icon {
    margin-right: 8px; /* Space between icon and text */
    font-size: 0.8em; /* Slightly smaller icon */
    width: 16px; /* Consistent width */
    text-align: center;
    transition: transform 0.3s ease; /* Animate rotation */
    color: #999999; /* Match other icons */
}

/* Rotate icon when heading's section is collapsed */
.sub-tab-heading.collapsed .accordion-icon {
    transform: rotate(-90deg);
}


/* List items containing actual links */
.sub-tab-item {
    /* No additional style needed by default, but provides a hook */
    transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
    overflow: hidden; /* Needed for smooth collapse animation if using max-height */
    max-height: 50px; /* Adjust if items are taller */
    opacity: 1;
}

/* Style for collapsed items */
.sub-tab-item.collapsed {
    /* Use max-height and opacity for smoother animation (optional) */
     max-height: 0;
     opacity: 0;
     /* If you prefer instant hide/show, use display: none; */
     /* display: none; */
     /* Reset padding/margin if necessary when collapsed */
     /* padding-top: 0; */
     /* padding-bottom: 0; */
     /* margin-top: 0; */
     /* margin-bottom: 0; */
}

.sub-tab-link {
    display: block;
    padding: 10px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    cursor: pointer;
    border-left: 3px solid transparent;
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
    white-space: nowrap;
}

.sub-tab-link i {
    margin-right: 8px;
    width: 16px;
    /* Align icons */
    text-align: center;
    color: var(--primary);
    /* Icon color */
}

.sub-tab-link.active i {
    color: #aaaaaa;
    /* Active icon color */
}

.sub-tab-link:hover {
    background-color: var(--surface);
    color: var(--text);
}

.sub-tab-link:hover i {
    color: var(--text);
}


.sub-tab-link.active {
    background-color: var(--surface);
    color: #cccccc;
    border-left-color: #888888;
    font-weight: 500;
}

/* Content Area */
.modal-content-area {
    flex-grow: 1;
    padding: 24px;
    overflow-y: auto;
    /* Primary scrollbar for content */
    background: var(--surface);
    /* Match modal content bg */
}

/* Styling the scrollbar for webkit browsers */
.modal-content-area::-webkit-scrollbar,
.modal-sub-tabs-nav::-webkit-scrollbar {
    width: 8px;
}

.modal-content-area::-webkit-scrollbar-track,
.modal-sub-tabs-nav::-webkit-scrollbar-track {
    background: var(--background);
    border-radius: 4px;
}

.modal-content-area::-webkit-scrollbar-thumb,
.modal-sub-tabs-nav::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 4px;
    border: 2px solid var(--background);
    /* Creates padding around thumb */
}

.modal-content-area::-webkit-scrollbar-thumb:hover,
.modal-sub-tabs-nav::-webkit-scrollbar-thumb:hover {
    background-color: var(--primary);
}

.tab-content-pane {
    display: none;
    /* Hide content panes by default */
}

.tab-content-pane.active {
    display: block;
    /* Show active content pane */
}

/* Content Styles */
.tab-content-pane h2 {
    color: var(--text);
    margin-top: 0;
    border-bottom: 1px solid var(--border);
    padding-bottom: 10px;
    margin-bottom: 20px;
    /* Increased margin */
    font-size: 20px;
    font-weight: 600;
}

.tab-content-pane h2 i {
    margin-right: 8px;
    font-size: 18px;
    /* Slightly smaller icon in heading */
}

.tab-content-pane h3 {
    color: var(--text);
    font-size: 16px;
    margin-top: 25px;
    margin-bottom: 10px;
    font-weight: 600;
    border-bottom: 1px dashed var(--border);
    padding-bottom: 5px;
}

.tab-content-pane p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 15px;
    margin-top: 0px;
}

.tab-content-pane code {
    background-color: var(--background);
    padding: 3px 6px;
    border-radius: 4px;
    font-family: monospace;
    color: #aaaaaa;
    border: 1px solid var(--border);
    font-size: 0.9em;
}

.tab-content-pane strong {
    color: var(--text);
    font-weight: 600;
}

/* Form Element Styles */
.tab-content-pane label {
    display: block;
    margin-bottom: 12px;
    color: var(--text-secondary);
    cursor: pointer;
}

.tab-content-pane input[type="radio"],
.tab-content-pane input[type="checkbox"] {
    margin-right: 8px;
    vertical-align: middle;
    accent-color: var(--brand-color);
    /* Style radio/checkbox color */
}

/* Custom styling for unchecked radio buttons and checkboxes */
.tab-content-pane input[type="radio"]:not(:checked),
.tab-content-pane input[type="checkbox"]:not(:checked) {
    background-color: var(--background);
    border: 1px solid var(--border);
    filter: brightness(0.7);
}

.tab-content-pane input[type="radio"]:not(:checked) {
    border-radius: 50%;
}

.tab-content-pane input[type="checkbox"]:not(:checked) {
    border-radius: 3px;
}

.tab-content-pane input[type="text"],
.tab-content-pane input[type="email"],
.tab-content-pane input[type="password"] { /* Added password type */
    background: var(--background);
    color: var(--text);
    border: 1px solid var(--border);
    padding: 8px 10px;
    border-radius: 6px;
    font-size: 14px;
    width: 100%;
    /* Default to full width */
    max-width: 350px;
    /* Limit max width */
    transition: border-color 0.2s ease;
}

.tab-content-pane input[type="text"]:focus,
.tab-content-pane input[type="email"]:focus,
.tab-content-pane input[type="password"]:focus { /* Added password type */
    outline: none;
    border-color: #888888;
}

.tab-content-pane select {
    background: var(--background);
    color: var(--text);
    border: 1px solid var(--border);
    padding: 8px 10px;
    border-radius: 6px;
    font-size: 14px;
    width: 100%;
    max-width: 100px;
    transition: border-color 0.2s ease;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ccc'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 12px;
}

.tab-content-pane select:focus {
    outline: none;
    border-color: #888888;
}

.tab-content-pane .input-group {
    margin-bottom: 15px;
}

.tab-content-pane .input-group label {
    display: inline-block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text);
    /* Make labels slightly more prominent */
    font-size: 14px;
}

.tab-content-pane .input-group small {
    display: block;
    color: var(--text-secondary);
    font-size: 12px;
    margin-top: 5px;
}

.tab-content-pane fieldset {
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    margin-bottom: 20px;
}

.tab-content-pane legend {
    padding: 0 10px;
    color: var(--text);
    font-weight: 600;
    font-size: 15px;
}

/* Style radio/checkbox labels slightly differently */
.tab-content-pane fieldset label,
.tab-content-pane label.checkbox-label,
.tab-content-pane label.radio-label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
    /* Normal weight for options */
    color: var(--text-secondary);
}

.tab-content-pane fieldset label:hover,
.tab-content-pane label.checkbox-label:hover,
.tab-content-pane label.radio-label:hover {
    color: var(--text);
}


/* Responsive Adjustments */
@media (max-width: 768px) {
    .settings-modal-content {
        width: 95%;
        height: 90vh;
        /* Adjust height for smaller screens */
    }

    .modal-body-container {
        flex-direction: column;
        /* Stack layout on smaller screens */
    }

    .modal-top-tabs {
        padding: 0 15px;
        overflow-x: auto;
        /* Allow scrolling top tabs if needed */
        white-space: nowrap;
        /* Simple scrollbar for horizontal */
        scrollbar-width: thin;
        scrollbar-color: var(--border) var(--surface);
    }

    .modal-top-tabs::-webkit-scrollbar {
        height: 5px;
    }

    .modal-top-tabs::-webkit-scrollbar-thumb {
        background-color: var(--border);
        border-radius: 3px;
    }


    .modal-sub-tabs-nav {
        width: 100%;
        /* Take full width */
        border-right: none;
        border-bottom: 1px solid var(--border);
        max-height: none;
        /* Allow full height in column layout */
        overflow-y: visible;
        /* Disable vertical scroll */
        padding: 0;
        /* Reset padding */
    }

    /* Make sub-tab groups scroll horizontally */
    .modal-sub-tabs-nav .sub-tabs-container {
        white-space: nowrap;
        overflow-x: auto;
        padding: 10px 15px;
        /* Add padding for scroll */
        scrollbar-width: thin;
        scrollbar-color: var(--border) var(--background);
        border-bottom: 1px solid var(--border);
        /* Separator line */
    }

     .modal-sub-tabs-nav .sub-tabs-container:last-child {
         border-bottom: none; /* Remove border from last container */
     }

    .modal-sub-tabs-nav .sub-tabs-container::-webkit-scrollbar {
        height: 5px;
    }

    .modal-sub-tabs-nav .sub-tabs-container::-webkit-scrollbar-thumb {
        background-color: var(--border);
        border-radius: 3px;
    }

    /* Style sub-tab items for horizontal layout */
    .sub-tab-heading {
        display: none; /* Hide headings on small screens for simplicity */
    }

    .modal-sub-tabs-nav li { /* Includes both heading and item LIs */
        display: inline-block;
        /* Make list items horizontal */
        margin-right: 5px;
         /* Reset accordion collapse styles for horizontal layout */
        max-height: initial !important;
        opacity: 1 !important;
        overflow: visible !important;
    }

     /* Ensure items are not collapsed on small screens */
    .modal-sub-tabs-nav li.sub-tab-item,
    .modal-sub-tabs-nav li.sub-tab-item.collapsed {
        display: inline-block !important; /* Force display */
    }

    .sub-tab-link {
        padding: 8px 12px;
        /* Adjust padding */
        border-left: none;
        /* Remove left border */
        border-bottom: 3px solid transparent;
        /* Use bottom border */
        display: inline-block;
        /* Make them flow horizontally */
        margin-bottom: 0;
        border-radius: 0;
    }

    .sub-tab-link.active {
        border-left-color: transparent;
        border-bottom-color: var(--brand-color);
        background-color: transparent;
        /* No background highlight */
    }

    .sub-tab-link i {
        margin-right: 5px;
        /* Reduce icon margin */
    }


    .modal-content-area {
        padding: 15px;
        /* Reduce padding */
    }

    .tab-content-pane input[type="text"],
    .tab-content-pane input[type="email"],
    .tab-content-pane input[type="password"] { /* Added password type */
        max-width: 100%;
        /* Allow text inputs to take full width */
    }
}

@media (max-width: 480px) {
    .settings-modal-header {
        padding: 10px 15px;
    }

    .settings-modal-title {
        font-size: 18px;
    }

    .top-tab-link {
        padding: 10px 12px;
        font-size: 14px;
    }

    .sub-tab-link {
        font-size: 13px;
        padding: 6px 10px;
    }

    .tab-content-pane h2 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .tab-content-pane h3 {
        font-size: 15px;
    }

    .tab-content-pane {
        font-size: 14px;
        /* Adjust base font size */
    }

    .tab-content-pane input[type="text"] {
        padding: 6px 8px;
        font-size: 13px;
    }
}

/* ================================================================ */
/* Styles for System Services - Simplified Alignment                */
/* ================================================================ */

/* Reset table styling */
.service-status {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    table-layout: fixed;
}

.service-status th,
.service-status td {
    padding: 12px 15px;
    text-align: left;
    vertical-align: middle;
    border-bottom: 1px solid var(--border);
    height: 44px;
    box-sizing: border-box;
}

/* Status indicators */
.status-running,
.status-stopped,
.status-failed,
.status-other,
.status-unknown {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    height: 28px;
}

.status-running { background-color: rgba(46, 204, 113, 0.2); color: #2ecc71; }
.status-stopped { background-color: rgba(220, 53, 69, 0.2); color: #dc3545; }
.status-failed { background-color: rgba(255, 193, 7, 0.2); color: #ffc107; }
.status-other,
.status-unknown { background-color: var(--unknown-bg); color: var(--unknown); }

/* Action buttons container */
.action-buttons {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
}

/* Button styles */
.btn-stop,
.btn-restart,
.btn-critical,
.btn-warning,
.btn-secondary
 {
    padding: 6px;
    height: 32px;
    border: 1px solid #888; 
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-stop { background-color: var(--surface); color: var(--text); }
.btn-restart { background-color: var(--surface); color: var(--text); }
.btn-critical { background-color: var(--critical-bg); color: var(--critical); }
.btn-warning { background-color: var(--warning-bg); color: var(--warning); }
.btn-secondary { background-color: var(--surface); color: var(--text); }

/* Disabled buttons */
.action-buttons button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: var(--background);
    color: var(--text-secondary);
}

/* State messages */
.message {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

.message-success {
    background-color: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
    border-left: 4px solid #2ecc71;
}

.message-error {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border-left: 4px solid #dc3545;
}
.service-status {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    overflow: hidden;
}

.service-status th {
    background-color: var(--surface);
    color: var(--text);
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

.service-status tr {
    line-height: 32px;
}

.service-status td {
    padding: 0 15px;
    border-bottom: 1px solid var(--border);
    vertical-align: middle;
}

.status-running,
.status-stopped,
.status-failed,
.status-other,
.status-unknown {
    line-height: 24px;
    display: inline-block;
    vertical-align: middle;
}

.action-buttons button {
    line-height: 24px;
    height: 24px;
    vertical-align: middle;
}

.service-status tr:last-child td {
    border-bottom: none;
}

/* Status Indicators */
.status-running {
    color: #2ecc71;
    background-color: rgba(46, 204, 113, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.status-stopped {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.status-failed {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.status-other, .status-unknown {
    color: var(--unknown);
    background-color: var(--unknown-bg);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
}

.action-buttons button {
    padding: 6px;
    margin: 0 2px;
    min-width: 32px;
    height: 32px;
    border: 1px solid #888;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s;
    background-color: #444;
    color: #fff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Reset start button styles and keep only stop and restart */
.btn-stop {
    background-color: #444;
    color: #fff;
    transition: all 0.2s;
}

.btn-stop:hover:not(:disabled) {
    background-color: #555;
    transform: translateY(-1px);
}

.btn-restart {
    background-color: #444;
    color: #fff;
    transition: all 0.2s;
}

.btn-restart:hover:not(:disabled) {
    background-color: #555;
    transform: translateY(-1px);
}
.btn-critical:hover:not(:disabled) {
    background-color: rgba(220, 53, 69, 0.3);
    transform: translateY(-1px);
}
.btn-warning:hover:not(:disabled) {
    background-color: rgba(255, 193, 7, 0.3);
    transform: translateY(-1px);
}
.btn-secondary:hover:not(:disabled) {
    background-color: #555;
    transform: translateY(-1px);
}

.action-buttons button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.message-info {
    background-color: var(--unknown-bg);
    color: var(--text-secondary);
    border-left: 4px solid var(--primary);
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

.message-info i {
    margin-right: 8px;
    color: var(--primary);
}

/* ================================================================ */
/* Styles for System Info Fragment (Example PHP output)             */
/* ================================================================ */

/* Grid Layout for Info */
.tab-content-pane .info-grid {
    display: grid;
    grid-template-columns: max-content 1fr;
    /* Label | Value */
    gap: 8px 18px;
    /* Row gap | Column gap */
    align-items: center;
    margin-top: 15px;
    margin-bottom: 25px;
    color: var(--text);
    /* Default text color */
}

/* Labels (dt) in the grid */
.tab-content-pane .info-grid dt {
    font-weight: 600;
    color: var(--text-secondary);
    white-space: nowrap;
    display: flex;
    align-items: center;
}

/* Icons within labels */
.tab-content-pane .info-grid dt i.fa {
    margin-right: 8px;
    color: #999999;
    width: 1.2em;
    text-align: center;
    font-size: 1.1em;
}

/* Values (dd) in the grid */
.tab-content-pane .info-grid dd {
    margin: 0;
    word-break: break-word;
    color: var(--text);
    line-height: 1.5;
}

/* Text summary before progress bar */
.tab-content-pane .info-grid dd .usage-summary {
    display: block;
    font-size: 0.9em;
    color: var(--text-secondary);
    margin-bottom: 6px;
}

/* Progress Bar Container */
.tab-content-pane .progress-bar-container {
    background-color: var(--background);
    border-radius: 6px;
    height: 18px;
    margin-top: 8px;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border);
}

/* Progress Bar Base Style */
.tab-content-pane .progress-bar {
    height: 100%;
    line-height: 18px;
    text-align: center;
    white-space: nowrap;
    border-radius: 5px;
    transition: width 0.6s ease, background-color 0.3s ease;
    font-size: 11px;
    font-weight: 600;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #111;
}

/* === PROGRESS BAR DYNAMIC COLOR STATES === */
.tab-content-pane .progress-bar.success {
    background-color: #2ecc71;
    color: #222;
}

.tab-content-pane .progress-bar.warning {
    background-color: #888888;
    color: #111;
}

.tab-content-pane .progress-bar.critical {
    background-color: #777;
    color: #f8f9fa;
}

/* === END PROGRESS BAR DYNAMIC COLOR STATES === */


/* Optional: Style for the 'Swap not configured' text */
.tab-content-pane .text-muted {
    font-size: 0.9em;
    color: var(--text-secondary);
    font-style: italic;
}


/* Specific styles for System Updates pane elements */
#general-system-updates .btn-restart {
    margin-top: 15px;
}

#general-system-updates #update-status {
    margin-top: 15px;
    overflow: hidden;
    overflow-wrap: break-word;
    display: none; /* Keep hidden initially */
    word-break: break-word; /* Add this for extra safety */
    white-space: normal; /* Ensure text wraps naturally */
}

#general-system-updates #update-status.visible {
    display: block; /* Add a class to show it via JS if needed, or JS can set display directly */
}

/* Style for the <pre> tag holding command output */
.update-output {
    background-color: var(--background); /* Darker background */
    border: 1px solid var(--border);
    padding: 10px 15px;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap; /* Wrap long lines */
    word-wrap: break-word; /* Break words if necessary */
    color: var(--text-secondary); /* Lighter text for output */
    font-family: monospace;
    font-size: 0.9em;
    border-radius: 4px;
    margin-top: 10px; /* Space above the pre block */
}

/* Style scrollbar within the pre block */
.update-output::-webkit-scrollbar {
    width: 6px;
}
.update-output::-webkit-scrollbar-track {
    background: var(--surface);
    border-radius: 3px;
}
.update-output::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 3px;
}
.update-output::-webkit-scrollbar-thumb:hover {
    background-color: var(--primary);
}
 /* ================================================================ */
 /* Styles for System Services Modules (using <details>/<summary>) */
 /* ================================================================ */
 
 .service-modules-container {
     /* Container for all module sections */
     margin-top: 20px;
 }
 
 .module-section {
     border: 1px solid var(--border);
     border-radius: var(--radius);
     margin-bottom: 15px;
     overflow: hidden; /* Contain borders and background */
     background-color: var(--surface); /* Match table header bg */
     transition: background-color 0.2s ease;
 }
 
 /* Removed [open] style change for consistency */
 /* .module-section[open] {
     background-color: var(--background);
 } */
  
 /* Module Header */
 .module-header {
     display: flex;
     justify-content: space-between;
     align-items: center;
     padding: 10px 15px; /* Slightly reduced padding */
     cursor: pointer;
     background-color: var(--surface);
     border-bottom: 1px solid var(--border);
     list-style: none; /* Remove default marker */ 
     transition: background-color 0.2s ease;
     font-weight: 600;
     color: var(--text);
 }
 
 .module-header::-webkit-details-marker {
     display: none; /* Hide default arrow in Webkit */
 }
 
 .module-header::before { /* Custom arrow using Font Awesome 4.7 */
     /* content: '\f0da'; */ /* FA 5 Right */
     content: '\f0da'; /* FA 4.7 Caret Right */
     font-family: 'FontAwesome'; /* Font Awesome 4.7 family name */
     font-weight: normal; /* FA 4.7 uses normal weight for icons */
     font-style: normal;
     display: inline-block;
     margin-right: 10px;
     color: #999999;
     transition: transform 0.3s ease; /* Keep transition for potential future use */
     width: 1em; /* Ensure space is allocated */
     text-align: center;
 }
 
 .module-section[open] > .module-header::before {
     /* transform: rotate(90deg); */ /* FA 5 */
      content: '\f0d7'; /* FA 4.7 Caret Down */
 }
 
 .module-header:hover {
     background-color: var(--background); /* Hover effect */
 }
 
 .module-name {
     flex-grow: 1;
     display: flex;
     align-items: center;
     gap: 10px;
 }
 
 /* Module status indicator styles */
 .module-status-indicator {
     font-size: 0.85em;
     font-weight: normal;
     padding: 2px 8px;
     border-radius: 4px;
     margin-left: 10px;
 }
 
 /* Match the existing status colors */
 .module-status-indicator.status-running {
     background-color: rgba(46, 204, 113, 0.2);
     color: #2ecc71;
 }
 
 .module-status-indicator.status-stopped {
     background-color: rgba(220, 53, 69, 0.2);
     color: #dc3545;
 }
 
 .module-status-indicator.status-failed {
     background-color: rgba(255, 193, 7, 0.2);
     color: #ffc107;
 }
 
 .module-status-indicator.status-other,
 .module-status-indicator.status-unknown {
     background-color: var(--unknown-bg);
     color: var(--unknown);
 }
 
 .module-actions {
     display: flex;
     gap: 8px;
     align-items: center; /* Align buttons vertically */
 }
 
 /* Style for module action buttons */
 .btn-module-action {
     padding: 6px;
     margin: 0 2px;
     min-width: 32px;
     height: 32px;
     border: 1px solid #888;
     border-radius: 4px;
     cursor: pointer;
     font-size: 13px;
     transition: all 0.2s;
     background-color: #444;
     color: #fff;
     display: inline-flex;
     align-items: center;
     justify-content: center;
 }
 
 .btn-module-action.btn-stop { 
     background-color: #444;
     color: #fff;
     transition: all 0.2s;
 }
 
 .btn-module-action.btn-restart { 
     background-color: #444;
     color: #fff;
     transition: all 0.2s;
 }
 
 .btn-module-action.btn-stop:hover:not(:disabled) { 
     background-color: #555;
     transform: translateY(-1px);
 }
 
 .btn-module-action.btn-restart:hover:not(:disabled) { 
     background-color: #555;
     transform: translateY(-1px);
 }
 
 .btn-module-action:disabled {
     opacity: 0.5;
     cursor: not-allowed;
 }
 
 /* Table within the module */
 .module-table {
     width: 100%;
     border-collapse: collapse;
     border: none;
     margin: 0;
     border-radius: 0;
     overflow: visible;
     background-color: var(--background);
     table-layout: fixed; /* Added for consistent column widths */
 }
 
 .module-table thead {
     display: none; /* Hide table headers */
 }
 
 .module-table tbody tr:first-child td {
     padding-top: 10px; /* Add padding to first row */
 }
 
 .module-table tbody tr:last-child td {
     padding-bottom: 10px; /* Add padding to last row */
 }
 
 .module-table tr {
     border-bottom: 1px solid var(--border);
 }
 
 .module-table tr:last-child {
     border-bottom: none;
 }
 
 .module-table td {
     background-color: var(--background);
     padding: 10px 15px; /* More vertical padding */
     vertical-align: middle;
     box-sizing: border-box;
     border: none; /* Remove individual cell borders */
     height: 50px; /* Consistent height */
 }
 
 /* Fixed alignment for the table cells */
 .module-table td:first-child {
     padding-left: 20px; /* More left padding for first column */
     width: 30%;
 }
 
 .module-table td:nth-child(2) {
     width: 30%;
 }
 
 .module-table td:last-child {
     width: 40%;
     text-align: right;
     padding-right: 20px; /* More right padding */
 }
 
 /* Status indicators */
 .status-running,
 .status-stopped,
 .status-failed,
 .status-other,
 .status-unknown {
     display: inline-flex;
     align-items: center;
     padding: 4px 10px; /* Adjusted padding */
     border-radius: 4px;
     min-width: 80px; /* Give a minimum width for consistency */
     justify-content: center; /* Center the text */
     height: 28px;
 }
 
 /* Module status indicator styles */
 .module-status-indicator {
     font-size: 0.85em;
     font-weight: normal;
     padding: 2px 8px;
     border-radius: 4px;
     margin-left: 10px;
     display: inline-flex; /* Better vertical alignment */
     align-items: center;
     justify-content: center;
     height: 22px; /* Slightly smaller than regular status indicators */
 }
 
 /* Ensure forms inside don't cause issues */
 .service-action-form, .module-action-form {
     margin: 0;
     display: inline-block; /* Ensure forms stay inline */
     vertical-align: middle; /* Align forms */
 }
 
 /* Ensure alignment within the module table cells */
 .module-table .action-buttons {
     justify-content: flex-end; /* Right-align the buttons */
     height: 100%; /* Ensure buttons container fills cell height */
 }
 
 .module-table .status-indicator { 
      /* Existing status styles should work */
      height: 28px; /* Match button height */
      line-height: initial; /* Reset line-height if needed */
      vertical-align: middle;
      display: inline-flex; /* Better alignment */
      align-items: center;
      justify-content: center; /* Center the text */
      min-width: 80px; /* Give a minimum width for consistency */
 }

.save-button, .reset-btn {
    background-color: #444;
    color: #fff;
    padding: 8px;
    min-width: 32px;
    height: 32px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 0;
    margin-right: 4px;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.save-button:hover, .reset-btn:hover {
    background-color: #555;
    transform: translateY(-1px);
}

/* User Modules Table Styles */
.user-modules-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    font-size: 14px;
}

.user-modules-table th {
    text-align: center;
    font-weight: 600;
    padding: 10px;
    background-color: var(--surface);
    border-bottom: 1px solid var(--border);
}

.user-modules-table th:first-child {
    text-align: left;
}

.user-modules-table td {
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid var(--border);
}

.user-modules-table td:first-child {
    text-align: left;
}

.user-modules-table tr:hover {
    background-color: var(--background);
}

.user-modules-table .module-status-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    font-size: 1em;
}

.user-modules-table .assigned {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
}

.user-modules-table .not-assigned {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.user-modules-table-wrapper {
    max-height: 500px;
    overflow: auto;
    margin-bottom: 15px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
}

/* Auth Tabs Navigation */
.auth-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border);
    overflow-x: auto;
}

.auth-tab-link {
    padding: 10px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    margin-right: 4px;
    position: relative;
    border-bottom: 3px solid transparent;
    font-size: 14px;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.auth-tab-link:hover {
    color: var(--text);
}

.auth-tab-link.active {
    color: #cccccc;
    border-bottom-color: #888888;
}

.auth-tab-link i {
    margin-right: 6px;
}

.auth-tab-content {
    padding-top: 15px;
}

/* Backup Tabs Navigation - Same styling as Auth Tabs */
.backup-tab-link {
    padding: 10px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    margin-right: 4px;
    position: relative;
    border-bottom: 3px solid transparent;
    font-size: 14px;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.backup-tab-link:hover {
    color: var(--text);
}

.backup-tab-link.active {
    color: #cccccc;
    border-bottom-color: #888888;
}

.backup-tab-link i {
    margin-right: 6px;
}

.backup-tab-content {
    padding-top: 15px;
}

/* Blacklist Tabs Navigation - Same styling as Auth Tabs */
.blacklist-tab-link {
    padding: 10px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    margin-right: 4px;
    position: relative;
    border-bottom: 3px solid transparent;
    font-size: 14px;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.blacklist-tab-link:hover {
    color: var(--text);
}

.blacklist-tab-link.active {
    color: #cccccc;
    border-bottom-color: #888888;
}

.blacklist-tab-link i {
    margin-right: 6px;
}

/* Service Discovery Tabs Navigation - Same styling as Blacklist Tabs */
.service-discovery-tab-link {
    padding: 10px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    margin-right: 4px;
    position: relative;
    border-bottom: 3px solid transparent;
    font-size: 14px;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.service-discovery-tab-link:hover {
    color: var(--text);
}

.service-discovery-tab-link.active {
    color: var(--text);
    border-bottom-color: var(--primary);
}

.service-discovery-tab-link i {
    margin-right: 6px;
}

/* Host Discovery Tabs Navigation - Same styling as Service Discovery Tabs */
.host-discovery-tab-link {
    padding: 10px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    margin-right: 4px;
    position: relative;
    border-bottom: 3px solid transparent;
    font-size: 14px;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.host-discovery-tab-link:hover {
    color: var(--text);
}

.host-discovery-tab-link.active {
    color: var(--text);
    border-bottom-color: var(--primary);
}

.host-discovery-tab-link i {
    margin-right: 6px;
}

/* User Edit Form Styles */
#user-edit-form-container,
#user-add-form-container {
    background-color: var(--surface);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

#user-edit-form-container h3,
#user-add-form-container h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--text);
    font-size: 18px;
    font-weight: 600;
}

#user-edit-form .input-group,
#user-add-form .input-group {
    margin-bottom: 15px;
}

#module-checkboxes-container {
    margin-top: 10px;
}

#module-checkboxes-container .checkbox-label {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

#module-checkboxes-container .checkbox-label:hover {
    background-color: var(--hover);
}

#module-checkboxes-container input[type="checkbox"] {
    margin-right: 8px;
}

/* Custom styling for unchecked checkboxes in module container */
#module-checkboxes-container input[type="checkbox"]:not(:checked) {
    background-color: var(--background);
    border: 1px solid var(--border);
    filter: brightness(0.7);
    border-radius: 3px;
}

@media (max-width: 768px) {
    #module-checkboxes-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 480px) {
    #module-checkboxes-container {
        grid-template-columns: 1fr;
    }
}

/* ================================ */
/* LDAP Browse Panel Styles         */
/* ================================ */
.ldap-browse-panel {
    background: var(--surface);
    overflow: hidden; /* clip inner scrollbars to rounded corners */
    color: var(--text);
    position: fixed;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 700px;
    height: 75%;
    padding: 20px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    z-index: 1050;
}

.ldap-browse-panel h3 {
    color: var(--text);
    margin-bottom: 10px;
}

.ldap-browse-panel details {
    margin-left: 10px;
}

.ldap-browse-panel summary {
    cursor: pointer;
    list-style: none;
    user-select: none;
}

.ldap-browse-panel input[type="checkbox"] {
    margin-right: 6px;
}

/* Custom styling for unchecked checkboxes in LDAP browse panel */
.ldap-browse-panel input[type="checkbox"]:not(:checked) {
    background-color: var(--background);
    border: 1px solid var(--border);
    filter: brightness(0.7);
    border-radius: 3px;
}

/* Button overrides inside panel */
.ldap-browse-panel .btn-restart,
.ldap-browse-panel .btn-stop {
    margin-top: 0;
}

/* ===== LDAP Browse Panel Scrollbar and Textarea Styles ===== */

/* Scrollbar styling for LDAP browse panel and its tree container */
.ldap-browse-panel::-webkit-scrollbar,
#ldap-browse-tree::-webkit-scrollbar {
    width: 4px; /* slimmer scrollbar */
}

.ldap-browse-panel::-webkit-scrollbar-track,
#ldap-browse-tree::-webkit-scrollbar-track {
    background: transparent; /* changed to avoid overflow */
    border-radius: 4px;
}

.ldap-browse-panel::-webkit-scrollbar-thumb,
#ldap-browse-tree::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 4px;
    border: 2px solid transparent; /* changed */
}

.ldap-browse-panel::-webkit-scrollbar-thumb:hover,
#ldap-browse-tree::-webkit-scrollbar-thumb:hover {
    background-color: var(--primary);
}

/* Base DN textarea styling */
.tab-content-pane textarea {
    background: var(--background);
    color: var(--text);
    border: 1px solid var(--border);
    padding: 8px 10px;
    border-radius: 6px;
    font-size: 14px;
    width: 100%;
    max-width: 350px;
    transition: border-color 0.2s ease;
    resize: vertical; /* Allow vertical resize only */
}

.tab-content-pane textarea:focus {
    outline: none;
    border-color: #888888;
}

#ldap-browse-tree {
    /* Ensure scrollbar is clipped by border radius */
    clip-path: inset(0 round var(--radius));
}

/* Close button inside LDAP browse panel */
#ldap-browse-panel #close-ldap-browse {
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-size: 20px;
    cursor: pointer;
    transition: color 0.2s ease;
}
#ldap-browse-panel #close-ldap-browse:hover {
    color: var(--text);
}

/* Time input styling */
.tab-content-pane input[type="time"] {
    background: var(--background);
    color: var(--text);
    border: 1px solid var(--border);
    padding: 8px 10px;
    border-radius: 6px;
    font-size: 14px;
    width: 100%;
    max-width: 120px;
    transition: border-color 0.2s ease;
}

.tab-content-pane input[type="time"]::-webkit-inner-spin-button,
.tab-content-pane input[type="time"]::-webkit-clear-button {
    display: none;
}

.tab-content-pane input[type="time"]::-webkit-calendar-picker-indicator {
    filter: invert(80%);
    cursor: pointer;
}

.tab-content-pane input[type="time"]:focus {
    outline: none;
    border-color: #888888;
}

