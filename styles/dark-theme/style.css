body {
    background-color: #1c1c1c;
    color: #fff;
    overflow: hidden;
    margin: 0;
    padding: 0;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

header {
    background-color: #2a2a2a;
    color: #fff;
    padding: 20px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: 55px;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    box-sizing: border-box;
    position: relative;
}

header h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    letter-spacing: 0.5px;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.hamburger {
    display: flex;
    flex-direction: column;
    cursor: pointer;
    gap: 5px;
    z-index: 100;
    padding: 5px;
}

.hamburger:hover .hamburger-line {
    background-color: #cde06b;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background-color: #fff;
    transition: all 0.3s ease;
}

.header-buttons {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    border: 1px solid #717171;
    background-color: #2a2a2a;
    flex-direction: column;
    min-width: 250px;
    padding: 15px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-radius: 0 0 8px 8px; /* Only bottom corners rounded */
    z-index: 1003;
    gap: 10px;
}

.header-buttons.active {
    display: flex;
}

.hamburger.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.hamburger.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(5px, -5px);
}

.header-button {
    background-color: transparent;
    color: #f0f0f0;
    border: none;
    padding: 10px 20px;
    font-size: 15px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    text-decoration: none;
    text-align: left;
}

.header-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.header-button.connection-off {
    opacity: 0.5;
}

.search-container {
    width: 250px;
    font-size: 0;
}

/* Hostlist search container styles for subnets.php and hosts.php */
.hostlist-search-container {
    display: none;
    background-color: #222222;
    padding: 8px 15px;
    position: relative;
    border-bottom: 1px solid #333;
    z-index: 9;
    gap: 10px;
    align-items: center;
}

.hostlist-search-container.show {
    display: flex;
}

.hostlist-search-container .search-input-wrapper {
    position: relative;
    flex: 0 1 300px;
    display: flex;
    align-items: center;
}

.hostlist-search-container input {
    flex: 1;
    background-color: #2c2c2c;
    border: none;
    border-radius: 8px;
    color: #e0e0e0;
    padding: 8px 40px 8px 15px;
    font-size: 14px;
    height: 22px;
}

.hostlist-search-container input:focus {
    outline: none;
    background-color: #333;
}

.hostlist-search-container input::placeholder {
    color: #999;
}

.hostlist-search-container .search-mode-selector {
    min-width: 120px;
}

.hostlist-search-container .search-input-wrapper label,
.hostlist-search-container .view-toggle-selector label,
.hostlist-search-container .layout-controls label {
    display: inline-block;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-right: 8px;
    font-weight: 500;
    vertical-align: middle;
}

.hostlist-search-container .search-mode-selector select {
    background-color: #2c2c2c;
    border: none;
    border-radius: 8px;
    color: #e0e0e0;
    padding: 8px 10px;
    font-size: 14px;
    height: 36px;
    width: 100%;
    cursor: pointer;
}

.hostlist-search-container .search-mode-selector select:focus {
    outline: none;
}

.hostlist-search-container .hostlist-clear-search {
    background: transparent;
    border: none;
    color: #999;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
}

.hostlist-search-container .hostlist-clear-search:hover {
    color: #fff;
}

.hostlist-search-container .layout-controls { display: inline-flex; align-items: center; gap: 8px; }

/* Connected search bar styles for hosts.php */
.hostlist-search-container.connected .search-input-wrapper {
    margin-right: 5px;
}

.hostlist-search-container.connected input {
    border-radius: 8px 0 0 8px;
}

.hostlist-search-container.connected .search-mode-selector select {
    border-radius: 0;
}

.hostlist-search-container.connected .view-toggle-selector select {
    border-radius: 0 8px 8px 0;
}

.view-toggle-selector {
    min-width: 120px;
    display: flex;
    align-items: center;
}

.view-toggle-selector select {
    background-color: #2c2c2c;
    border: none;
    border-radius: 8px;
    color: #e0e0e0;
    padding: 8px 10px;
    font-size: 14px;
    height: 36px;
    width: 100%;
    cursor: pointer;
}

.view-toggle-selector select:focus {
    outline: none;
}

.view-toggle-container {
    display: flex;
    justify-content: center;
    padding: 10px 15px;
    background-color: #2a2a2a;
    border-bottom: 1px solid #777;
}

.view-toggle-container .view-toggle-selector {
    min-width: 150px;
}

.view-toggle-container .view-toggle-selector select {
    border-radius: 8px;
    border: 1px solid #777;
    background-color: #2a2a2a;
    color: #fff;
    font-size: 14px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-toggle-container .view-toggle-selector select:hover {
    border-color: #4CAF50;
}

.view-toggle-container .view-toggle-selector select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .hostlist-search-container {
        padding: 6px 12px;
    }

    .hostlist-search-container input {
        height: 20px;
        font-size: 13px;
        padding: 6px 35px 6px 12px;
    }

    .hostlist-search-container .search-mode-selector select {
        height: 32px;
        font-size: 13px;
        padding: 6px 8px;
    }

    .hostlist-search-container .hostlist-clear-search {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    .view-toggle-selector {
        min-width: 100px;
    }

    .view-toggle-selector select {
        height: 32px;
        font-size: 13px;
        padding: 6px 8px;
    }

    .view-toggle-container {
        padding: 8px 12px;
    }

    .view-toggle-container .view-toggle-selector {
        min-width: 120px;
    }

    .view-toggle-container .view-toggle-selector select {
        padding: 8px 12px;
        font-size: 13px;
    }
}

.breadcrumbs {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

.breadcrumbs a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.2s ease;
}

.breadcrumbs a:hover {
    color: #fff;
}

.separator {
    margin: 0 5px;
    color: rgba(255, 255, 255, 0.4);
}

.current {
    color: #fff;
    font-weight: 500;
}

#canvas-container {
    position: absolute;
    top: 55px;
    left: 0;
    width: 100%;
    height: calc(100vh - 55px);
}

#map {
    width: 100%;
    height: 100%;
    background-color: #2F2F2F;
}

.group-bubble {
    fill: rgba(255, 255, 255, 0.1);
    opacity: 0.5;
    stroke: #D3D3D3;
    stroke-dasharray: 20 10;
    stroke-width: 1px;
}

.host-bubble {
    fill: #4A4A4A; /* Solid muted gray, slightly lighter than #2F2F2F */
    cursor: grab;
    stroke-width: 1;
    stroke: #D3D3D3; /* Light gray stroke matching .group-bubble */
    user-select: none;
}

.host-bubble:hover {
    fill: #5E5E5E; /* A solid medium gray for hover, distinct from #777 */
}

.host-bubble:active {
    cursor: grabbing;
}

.host-bubble.ok {
    stroke: #cde06b;
    fill: #3b4032;
}

.host-bubble.ok:hover {
    stroke: #cde06b;
    fill: rgba(205, 224, 107, 0.7);
}

.host-bubble.warning {
    stroke: #ffa500;
    fill: #40382a;
}

.host-bubble.warning:hover {
    stroke: #ffa500;
    fill: rgba(255, 165, 0, 0.7);
}

.host-bubble.down {
    stroke: #d41d28;
    fill: #3c2e30;
    stroke-width: 3.5;
}

.host-bubble.down:hover {
    stroke: #d41d28;
    fill: rgba(212, 29, 40, 0.7);
}

.host-bubble.critical {
    stroke: #d41d28;
    fill: #3c2e30;
}

.host-bubble.critical:hover {
    stroke: #d41d28;
    fill: rgba(212, 29, 40, 0.7);
}

.host-bubble.pending {
    stroke: #808080 !important;
    fill: #373737 !important;
}

.host-bubble.pending:hover {
    stroke: #808080 !important;
    fill: rgba(128, 128, 128, 0.7) !important;
}

.host-bubble.unknown {
    stroke: #64748b;
    fill: #353739;
}

.host-bubble.unknown:hover {
    stroke: #64748b;
    fill: rgba(100, 116, 139, 0.7);
}

.host-bubble.not-added {
    stroke: #f9e876;
    stroke-width: 3px;
    fill: none;
    stroke-dasharray: 20 10;
    animation: scan 1.5s linear infinite;
}

@keyframes scan {
    0% {
        stroke-dashoffset: 0;
    }

    100% {
        stroke-dashoffset: 60;
    }
}

.bubble-text {
    fill: #e0e0e0;
    font-size: 14px;
    text-anchor: middle;
    pointer-events: none;
}

.group-text {
    fill: #e0e0e0;
    font-size: 18px;
    text-anchor: start;
    pointer-events: none;
    user-select: none;
}

#canvas-container {
    cursor: grab;
}

#canvas-container:active {
    cursor: grabbing;
}

.loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    overflow: hidden;
}

.spinner {
    position: relative;
    width: 120px;
    height: 120px;
    animation: float 3s ease-in-out infinite;
}

.spinner::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: #fde627;
    border-bottom-color: #fde627;
    animation: spin 1.5s linear infinite;
    filter: drop-shadow(0 0 12px rgba(107, 205, 230, 0.4));
}

.spinner::after {
    content: "";
    position: absolute;
    top: 12px;
    left: 12px;
    right: 12px;
    bottom: 12px;
    border-radius: 50%;
    border: 4px solid transparent;
    border-left-color: #fde627;
    border-right-color: #fde627;
    animation: spinReverse 2s linear infinite;
}

.spinner-dot {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
    animation: pulse 2s ease-out infinite;
}

.loading-text {
    position: absolute;
    bottom: 30%;
    color: white;
    font-size: 1.4em;
    letter-spacing: 2px;
    animation: textColor 3s ease-in-out infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes spinReverse {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(-720deg);
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.5;
    }

    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
    }

    100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.5;
    }
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-20px);
    }
}

@keyframes textColor {
    0% {
        color: #6bcde0;
    }

    50% {
        color: #ff6b6b;
    }

    100% {
        color: #6bcde0;
    }
}

.spinner-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #fff;
    border-radius: 50%;
    animation: sparkle 1.5s infinite;
}

@keyframes sparkle {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 0;
    }

    50% {
        transform: scale(1) rotate(180deg);
        opacity: 1;
    }

    100% {
        transform: scale(0) rotate(360deg);
        opacity: 0;
    }
}

.spinner-particle:nth-child(1) {
    top: 10%;
    left: 30%;
    animation-delay: 0.2s;
}

.spinner-particle:nth-child(2) {
    top: 70%;
    left: 80%;
    animation-delay: 0.6s;
}

.spinner-particle:nth-child(3) {
    top: 40%;
    left: 10%;
    animation-delay: 1s;
}

.spinner-particle:nth-child(4) {
    top: 85%;
    left: 40%;
    animation-delay: 1.4s;
}

.floating-menu {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    z-index: 99;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    cursor: move; /* Indicates it's draggable */
    user-select: none; /* Prevents text selection during drag */
    touch-action: none; /* Prevents default touch behaviors like scrolling */
    width: auto; /* Ensure width is stable */
    height: auto; /* Ensure height is stable */
}

.menu-item {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 3px solid;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.menu-item:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.reset-menu {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 0px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.reset-menu:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.menu-item.active {
    background-color: inherit; /* Will use the border color set by JS */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.menu-item[data-state="ok"] {
    border-color: #cde06b;
}

.menu-item[data-state="warning"] {
    border-color: #ffa500;
}

.menu-item[data-state="down"] {
    border-color: #d41d28;
}

.menu-item[data-state="critical"] {
    border-color: #d41d28;
}

.menu-item[data-state="pending"] {
    border-color: #808080 !important;
}

.menu-item[data-state="unknown"] {
    border-color: #64748b;
}

.menu-item.active {
    background-color: var(--border-color);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .floating-menu {
        right: 10px;
        gap: 8px;
        padding: 8px;
        border-radius: 16px;
    }

    .menu-item {
        width: 24px;
        height: 24px;
    }

    .reset-menu {
        width: 24px;
        height: 24px;
        border-radius: 15px;
    }

    .modal-content {
        background-color: #1c1c1c;
        padding: 22px;
        border: 1px solid #888;
        color: white;
        position: relative;
        transition: all 0.3s ease;
        transform: translateY(-100%);
        opacity: 0;
        width: 80%;
        height: 95vh;
        margin: auto;
        display: flex;
        flex-direction: column;
    }
}

.context-menu {
    display: none;
    position: absolute;
    background: #333;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 8px;
    max-width: 600px;
    font-weight: 200;
    border: 1px solid #404040;
}

.context-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.context-menu li,
.context-menu a {
    padding: 4px 6px;
    color: #fff;
    cursor: pointer;
    position: relative;
    text-decoration: none;
    font-size: 13px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    transition: all .1s ease-out;
}

.context-menu li:hover {
    background: #444;
    transform: translateX(2px);
}


.context-menu .fa {
    font-size: 13px;
    margin-right: 6px;
    color: #ccc;
}

.context-menu-title {
    padding-bottom: 6px;
    margin-top: 5px;
    border-bottom: 1px solid #555;
    margin-bottom: 6px;
    color: #fff;
    font-weight: 600;
    font-size: 14px;
}

.submenu {
    display: none;
    position: absolute;
    left: 100%;
    top: -100px;
    background: #333;
    border-radius: 8px;
    min-width: 180px;
    max-height: 250px;
    padding: 8px;
    box-sizing: border-box;
    border: 1px solid #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.submenu .search-item {
    position: sticky;
    top: 0;
    background-color: #333;
    padding: 0;
    margin: 0 0 5px 0;
    z-index: 1;
}

.submenu .search-item input[type="text"] {
    width: 100%;
    padding: 4px 6px;
    background-color: #444;
    border: 1px solid #555;
    border-radius: 4px;
    color: #fff;
    font-size: 13px;
    outline: none;
    box-sizing: border-box;
}

.submenu .search-item input[type="text"]::placeholder {
    color: #aaa;
    opacity: 1;
}

.submenu .search-item input[type="text"]:focus {
    border-color: #777;
    background-color: #4a4a4a;
}

.submenu .list-container {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: calc(250px - 35px);
    overflow-y: auto;
    overflow-x: hidden;
}

.submenu .list-container li {
    padding: 4px 6px;
    color: #fff;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    transition: all .1s ease-out;
}

.submenu .list-container li .fa {
    font-size: 13px;
    margin-right: 6px;
}

#move-to:hover .submenu {
    display: block;
}

#move-to.active .submenu {
    display: block;
}


.iframeModal {
    display: none;
    position: fixed;
    z-index: 1002;
    left: 0;
    top: 55px;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
}

.iframeModal-content {
    background-color: #1c1c1c;
    border-radius: 0 0 8px 8px; /* 8px bottom corners, square top */
    padding: 0 12px 12px 12px; /* Top padding 0, rest 12px */
    border: 1px solid rgba(255, 255, 255, 0.18); /* Thin border */
    color: white;
    position: relative;
    transition: none; /* Use keyframe animations instead */
    width: 92%;
    height: 90%;
    margin: auto;
    display: flex;
    flex-direction: column;
    box-shadow: 0 6px 28px rgba(0, 0, 0, 0.35);
    will-change: clip-path, transform, opacity;
}

.iframeModal.show .iframeModal-content {
    animation: modalRollDownDark 0.35s ease-out both;
}

.iframeModal.closing .iframeModal-content {
    animation: modalRollUpDark 0.3s ease-in both;
}

.iframeModal.small .iframeModal-content {
    width: 80%;
    max-width: 800px;
    height: auto;
    min-height: 100vh;
}

.iframeMclose {
    color: #f0f0f0; /* dark theme icon color */
    position: absolute;
    left: 10px;
    top: 10px;
    font-size: 22px;
    font-weight: 600;
    cursor: pointer;
    line-height: 22px;
    transition: all 0.15s ease;
    background: #404040; /* dark theme background */
    border: 1px solid #555555; /* dark theme border */
    border-radius: 50px;
    width: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.iframeMclose:hover,
.iframeMclose:focus {
    color: #fff;
    background: #505050;
    border-color: #777777;
    text-decoration: none;
}

.iframeMclose:focus-visible {
    outline: 3px solid rgba(205, 230, 39, 0.35);
    outline-offset: 2px;
}

iframe {
    flex: 1;
    border-radius: 0; /* Square corners */
    border: none;
}

@keyframes modalRollDownDark {
    0% {
        opacity: 0;
        transform: translateY(-10px);
        clip-path: inset(0 0 100% 0);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
        clip-path: inset(0 0 0 0);
    }
}

@keyframes modalRollUpDark {
    0% {
        opacity: 1;
        transform: translateY(0);
        clip-path: inset(0 0 0 0);
    }
    100% {
        opacity: 0;
        transform: translateY(-10px);
        clip-path: inset(0 0 100% 0);
    }
}

@media (max-width: 768px) {
    .iframeModal-content {
        background-color: #1c1c1c;
        padding: 22px;
        border: 1px solid #888;
        color: white;
        position: relative;
        transition: all 0.3s ease;
        transform: translateY(-100%);
        opacity: 0;
        height: 95vh;
        margin: auto;
        display: flex;
        flex-direction: column;
    }
}

.modal-form {
    width: auto;
    margin: 0 auto;
    padding: 20px;
    background-color: #1c1c1c;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    color: #fff;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

@media only screen and (min-width: 1024px) {
    .modal-form {
        min-width: 350px;
    }
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input[type="text"],
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background-color: rgba(60, 60, 60, 0.5);
    color: #fff;
    box-sizing: border-box;
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-group input[type="text"]:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background-color: rgba(80, 80, 80, 0.6);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.15);
}

.btn-submit {
    display: inline-block;
    padding: 10px 20px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.btn-submit:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-submit:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.2);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.formModal {
    display: none;
    position: fixed;
    z-index: 1003;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out forwards;
}

.formModal.show {
    display: block;
    animation: fadeIn 0.3s ease-out forwards;
}

.formModal-content {
    background-color: #2a2a2a;
    margin: auto;
    padding: 32px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 540px;
    width: 85%;
    max-height: 85vh;
    border-radius: 12px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    color: #fff;
    animation: slideIn 0.3s ease-out forwards;
    overflow-y: auto;
}

.formModal-close {
    color: rgba(255, 255, 255, 0.6);
    font-size: 24px;
    font-weight: 400;
    cursor: pointer;
    position: absolute;
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    z-index: 10;
}

.formModal-close:hover,
.formModal-close:focus {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.formModal .modal-form {
    width: 100%;
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
    text-align: center;
}

.formModal .form-group {
    margin-bottom: 20px;
    text-align: left;
}

.formModal .form-group:last-of-type {
    margin-bottom: 24px;
}

.formModal .form-group label {
    display: block;
    margin-bottom: 8px;
    color: #fff;
    font-weight: 500;
    font-size: 14px;
}

.formModal .form-group input[type="text"],
.formModal .form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background-color: rgba(60, 60, 60, 0.5);
    color: #fff;
    box-sizing: border-box;
    font-size: 14px;
    transition: all 0.2s ease;
    font-family: inherit;
}

.formModal .form-group input[type="text"]:focus,
.formModal .form-group select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background-color: rgba(80, 80, 80, 0.6);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.15);
}

.formModal .btn-submit {
    display: inline-block;
    width: 100%;
    padding: 12px 20px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    font-weight: 500;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    font-family: inherit;
}

.formModal .btn-submit:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.formModal .btn-submit:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.2);
}

.formModal .btn-submit:disabled {
    background-color: #555;
    color: #aaa;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .formModal-content {
        width: 90%;
        padding: 24px;
        border-radius: 8px;
        max-height: 90vh;
    }

    .formModal .form-group {
        margin-bottom: 18px;
    }

    .formModal .form-group input[type="text"],
    .formModal .form-group select {
        padding: 12px 14px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .formModal .btn-submit {
        padding: 12px 18px;
        font-size: 16px;
    }
}

.add-hostgroup-btn {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30px;
    box-sizing: border-box;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

.add-hostgroup-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.add-hostgroup-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background: rgba(255, 255, 255, 0.2);
}

.add-hostgroup-btn i {
    font-size: 13px;
    line-height: 1;
    color: rgba(255, 255, 255, 0.9);
    margin-right: 4px;
}

/* Approval Notification Pop-up */
.approval-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    /* Use a theme background, slightly lighter than body */
    background-color: #2a2a2a;
    color: #e0e0e0; /* Slightly softer white */
    padding: 15px;
    border-radius: 8px; /* Match theme's common radius */
    /* Match theme's shadow style */
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    max-width: 350px;
    transition: all 0.3s ease;
    /* Use theme font */
    font-family: 'Calibri', sans-serif;
    /* Use theme's subtle border */
    border: 1px solid rgba(255, 255, 255, 0.15);
}

.approval-notification.hidden {
    transform: translateY(150%);
    opacity: 0;
}

.approval-notification h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #fff; /* Keep heading bright white */
    font-weight: 500; /* Match header h1 weight */
}

/* General Button Style for Notification */
.approval-notification button {
    margin-top: 10px;
    margin-right: 10px;
    padding: 6px 12px;
    /* Use theme's button style */
    background-color: rgba(255, 255, 255, 0.1);
    color: #f0f0f0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px; /* Match theme's button radius */
    cursor: pointer;
    transition: all 0.2s ease; /* Match theme transition */
    font-size: 14px;
    position: relative; /* Keep for loading indicator positioning */
    overflow: hidden; /* Keep for loading indicator clipping */
}

/* Hover state for notification buttons */
.approval-notification button:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px); /* Subtle lift effect */
    box-shadow: 0 4px 8px rgba(0,0,0,0.15); /* Subtle shadow on hover */
}

/* Host Item within Notification */
.host-item {
    margin-bottom: 10px;
    padding: 5px 0; /* Adjusted padding slightly */
    /* Use theme's subtle divider */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
/* Remove border from last item */
.host-item:last-child {
    border-bottom: none;
    margin-bottom: 0; /* Remove margin if it's the last */
}


/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    /* Match theme's modal overlay */
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1001;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(4px); /* Add blur like formModal */
}

/* Modal Dialog */
.modal-dialog {
    /* Use theme's modal background */
    background-color: #2a2a2a;
    /* Use theme's modal radius */
    border-radius: 12px;
    max-width: 900px;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    padding: 0; /* Remove padding - will add to individual sections */
    color: #e0e0e0; /* Softer white */
    /* Use theme's modal shadow */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    /* Use theme's modal border */
    border: 1px solid rgba(255, 255, 255, 0.1);
    /* Use theme font */
    font-family: 'Calibri', sans-serif;
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* Use theme's subtle divider */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 22px; /* Match original theme padding */
    margin-bottom: 0;
    position: sticky;
    top: 0;
    z-index: 2;
    border-radius: 12px 12px 0 0; /* Round only top corners */
}

/* Modal Search Controls */
.modal-search-controls {
    padding: 10px 22px; /* Match header padding */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Theme divider */
    background-color: #2a2a2a; /* Match modal header background */
}

.modal-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2); /* Theme input border */
    border-radius: 6px; /* Theme input radius */
    font-size: 14px;
    color: #e0e0e0; /* Theme input text color */
    background-color: rgba(255, 255, 255, 0.05); /* Theme input background */
    box-sizing: border-box;
}

.modal-search-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1); /* Subtle focus shadow */
}

/* Content container for scrollable area */
/* Custom Dark Scrollbar Styles - Compatible with both Firefox and Chromium */
.modal-content-approval {
    overflow-y: auto;
    padding: 0 22px;
    flex-grow: 1;

    /* Firefox */
    scrollbar-width: thin;
    scrollbar-color: #555555 #333333; /* thumb and track color */
}

/* Chromium-based browsers (Chrome, Edge, Opera, etc.) */
.modal-content-approval::-webkit-scrollbar {
    width: 8px;
    height: 8px; /* For horizontal scrollbars if needed */
}

.modal-content-approval::-webkit-scrollbar-track {
    background: #333333;
    border-radius: 4px;
}

.modal-content-approval::-webkit-scrollbar-thumb {
    background-color: #555555;
    border-radius: 4px;
    border: 2px solid #333333; /* creates padding around scroll thumb */
}

.modal-content-approval::-webkit-scrollbar-thumb:hover {
    background: #777777;
}

/* For the corner where vertical and horizontal scrollbars meet */
.modal-content-approval::-webkit-scrollbar-corner {
    background: #333333;
}

/* Modal Footer */
.modal-footer {
    margin-top: 0;
    text-align: right; /* This will be overridden by flex-end but kept for safety */
    padding: 15px 22px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    bottom: 0;
    background-color: #2a2a2a;
    z-index: 2;
    border-radius: 0 0 12px 12px;
    display: flex; /* Ensured */
    justify-content: flex-end; /* Ensured */
    align-items: center; /* Ensured */
    /* flex-wrap: wrap; */ /* Explicitly removing/commenting out wrap */
    gap: 10px;
}

.modal-header h2 {
    margin: 0;
    color: #fff; /* Keep header bright white */
    font-size: 20px;
    font-weight: 500; /* Match theme weight */
}

/* Modal Close Button */
.modal-close {
    color: rgba(255, 255, 255, 0.6); /* Theme close color */
    transition: color 0.2s ease; /* Theme transition */
}

.modal-close:hover {
    color: #fff; /* Theme close hover color */
}

/* Modal Separator */
.modal-separator {
    width: 1px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.2);
    margin: 0 10px;
    align-self: center;
}

/* Host Table */
.host-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    font-family: 'Calibri', sans-serif; /* Ensure theme font */
}

.host-table th,
.host-table td {
    /* Theme's subtle table border */
    border: 1px solid rgba(255, 255, 255, 0.15);
    padding: 8px 10px; /* Adjusted padding */
    text-align: left;
    font-size: 14px; /* Consistent font size */
}

.host-table th {
    /* Darker background for header to distinguish from rows */
    background-color: #1a1a1a;
    font-weight: 600; /* Theme header weight */
    color: #fff;
    position: sticky;
    top: -2px;
    z-index: 10;
}

/* Alternating row color matches theme */
.host-table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.1); /* Darker subtle difference */
}
/* Keep base background consistent */
.host-table tr:nth-child(odd) {
    background-color: transparent;
}

.host-table tr:hover {
    /* Theme hover effect */
    background-color: rgba(255, 255, 255, 0.08);
}

/* Action Buttons within Table */
.host-service-action-cell {
    vertical-align: top;
    white-space: nowrap;
}

.host-service-action-cell button {
    display: inline-block;
    padding: 4px 8px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #f0f0f0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    font-weight: 500;
    margin-right: 5px;
}

.host-service-action-cell button:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.host-service-action-cell button:last-child {
    margin-right: 0;
}

/* Style for small action buttons */
.action-buttons button {
    padding: 4px 8px; /* Smaller padding */
    /* Base theme button style */
    background-color: rgba(255, 255, 255, 0.1);
    color: #f0f0f0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px; /* Slightly smaller radius */
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    position: relative; /* Keep for loading */
    overflow: hidden;  /* Keep for loading */
    flex-shrink: 0; /* Prevent buttons from shrinking */
}

.action-buttons button:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Service list cell styling */
.service-list {
    max-width: 300px;
    vertical-align: top; /* Align to top when content expands */
}

/* Ensure table cells maintain consistent height */
.host-table td {
    vertical-align: middle;
    min-height: 40px;
    height: auto;
}

/* Modal Footer Buttons */
.modal-footer button {
    padding: 8px 16px;
    margin-left: 10px;
    /* Base theme button style */
    background-color: rgba(255, 255, 255, 0.1);
    color: #f0f0f0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    position: relative; /* Keep for loading */
    overflow: hidden; /* Keep for loading */
    font-weight: 500; /* Add weight */
}

.modal-footer button:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Loading animation styles - adapted for theme */
.loading {
    pointer-events: none; /* Prevent clicking while loading */
    color: transparent !important; /* Hide text */
}

/* Overlay effect within the button */
.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    /* Slightly darker overlay matching button background */
    background: rgba(42, 42, 42, 0.7); /* Use #2a2a2a base */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5; /* Below spinner */
}

/* Spinner element */
.loading::before {
    content: "";
    box-sizing: border-box; /* Include border in size */
    position: absolute;
    /* Center the spinner */
    top: calc(50% - 8px);
    left: calc(50% - 8px);
    width: 16px;
    height: 16px;
    /* Subtle base border */
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    /* Use theme's loader accent color */
    border-top-color: #fde627;
    animation: spin 0.8s linear infinite;
    z-index: 10; /* Above overlay */
}

/* Keep the keyframes animation */
@keyframes spin {
    to { transform: rotate(360deg); }
}
/*  .os-icon {
    filter: brightness(0) invert(1);
} */

/* Status count wrapper styles */
.status-count-wrapper {
    display: flex;
    flex-direction: row;
    margin-left: 15px;
    gap: 10px;
    align-items: center;
}

/* Host Count Display Styles */
.host-count-container {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    background-color: #2a2a2a;
    border-radius: 4px;
}

.host-count-title {
    font-weight: 500;
    margin-right: 8px;
    color: #e0e0e0;
}

.host-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
    margin: 0 3px;
    padding: 0 6px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: help;
}

.host-count:hover, .service-count:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.5);
}

.host-count.up, .service-count.up {
    background-color: #4CAF50;
}

.host-count.down, .service-count.down {
    background-color: #F44336;
}

.host-count.unreachable {
    background-color: #FF9800;
}

.host-count.pending, .service-count.pending {
    background-color: #9E9E9E;
}

.host-count-error, .service-count-error {
    color: #F44336;
    font-size: 12px;
    font-weight: 500;
}

/* Service Count Display Styles */
.service-count-container {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    background-color: #2a2a2a;
    border-radius: 4px;
}

.service-count-title {
    font-weight: 500;
    margin-right: 8px;
    color: #e0e0e0;
}

.service-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
    margin: 0 3px;
    padding: 0 6px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: help;
}

.service-count.warning {
    background-color: #FFC107;
}

.service-count.unknown {
    background-color: #64748b;
}

/* Responsive styles for status counts */
@media (max-width: 768px) {
    .status-count-wrapper {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        margin: 0;
        padding: 10px;
        background-color: #2a2a2a;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        z-index: 100;
        display: none; /* Hidden by default on mobile */
        flex-direction: column; /* Stack vertically on mobile */
        gap: 10px;
    }

    .status-count-wrapper.show {
        display: flex;
    }

    .host-count-container, .service-count-container {
        background-color: transparent;
        box-shadow: none;
        border-radius: 0;
        padding: 0;
    }

    .host-count-toggle {
        display: flex; /* Show toggle on mobile */
    }

    /* Adjust header height when status counts are shown */
    header.status-count-visible {
        height: auto;
    }

    /* Ensure the canvas container adjusts when status counts are shown */
    #canvas-container.status-count-visible {
        top: calc(55px + 90px); /* Header height + status counts height */
    }

    /* APM Progress Indicator mobile styles */
    .apm-progress-simple {
        /* Remove from header breadcrumbs on mobile */
        display: none !important;
    }

    /* APM progress indicator within status counts wrapper */
    .status-count-wrapper .apm-progress-mobile {
        display: none;
        padding: 8px 15px;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 5px;
    }

    .status-count-wrapper .apm-progress-mobile.show {
        display: flex;
    }

    .status-count-wrapper .apm-progress-mobile .apm-label {
        color: rgba(255, 255, 255, 0.6);
        font-size: 13px;
    }

    .status-count-wrapper .apm-progress-mobile i {
        color: #cde06b;
        font-size: 13px;
        margin-right: 2px;
    }

    .status-count-wrapper .apm-progress-mobile #apm-progress-count-mobile {
        color: #fff;
        font-weight: 500;
    }
}

/* Added styles for Modal Search, Pagination, and Footer Details (Dark Theme) */

/* Search Input Wrapper and Clear Button */
.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

/* Ensure modal search input has space for clear button and grows */
.modal-search-input {
    padding-right: 30px !important; /* Ensure override */
    flex-grow: 1 !important; /* Ensure override */
}

.clear-search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #aaa; /* Dark theme text-muted color */
    font-size: 20px;
    cursor: pointer;
    padding: 0 5px;
    line-height: 1;
}

.clear-search-btn:hover {
    color: #e0e0e0; /* Dark theme text color */
}

/* Search Results Info */
.search-results-info {
    margin: 10px 0 0;
    padding: 5px 0;
    font-size: 14px;
    color: #aaa; /* Dark theme text color slightly muted */
    font-style: italic;
}

/* Modal Footer Action Descriptions and Separators */
.action-description {
    font-size: 14px;
    color: #aaa; /* Dark theme text-muted color */
    margin-right: 5px;
}

.action-button-group {
    display: flex;
    align-items: center;
    gap: 5px; /* Spacing between label and buttons within a group */
}

.action-separator {
    height: 20px;
    width: 1px;
    background-color: #444; /* Dark theme border/separator color */
    margin: 0 5px;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 0;
    margin-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1); /* Dark theme border color */
}

.pagination-info {
    margin-bottom: 10px;
    font-size: 14px;
    color: #e0e0e0; /* Dark theme text color */
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.pagination-btn {
    min-width: 32px;
    height: 32px;
    padding: 0 8px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.pagination-btn:hover:not(.disabled):not(.active) {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.pagination-btn.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    font-weight: bold;
    color: #fff;
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-ellipsis {
    margin: 0 2px;
    color: #e0e0e0;
}

/* Styles for service approval modal specifics */
.service-list {
    max-width: 300px;
}

.services-preview,
.services-full {
    max-width: 100%;
}

.services-full {
    background-color: rgba(255, 255, 255, 0.05);
    padding: 8px;
    border-radius: 4px;
    max-height: 250px;
    overflow-y: auto;
}

.toggle-services {
    display: inline-block;
    margin-top: 3px;
    color: #3a80bd;
    text-decoration: none;
    cursor: pointer;
}

.toggle-services:hover {
    text-decoration: underline;
}

.mt-2 {
    margin-top: 10px !important;
}

/* Subnet Detail Card Styles */
.subnet-detail-card {
    position: fixed;
    width: 350px;
    max-width: 90vw;
    max-height: 80vh; /* Limit height to ensure it stays in viewport */
    background-color: #2a2a2a;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
    z-index: 1000;
    overflow: auto; /* Changed from hidden to auto to enable scrolling */
    font-family: 'Calibri', sans-serif;
    color: #f0f0f0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: fadeIn 0.3s ease;
    transition: all 0.2s ease; /* Smooth transition when position is adjusted */
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive styles for subnet detail card */
@media (max-width: 768px) {
    .subnet-detail-card {
        width: calc(100% - 20px);
        max-width: none;
        max-height: 85vh;
        left: 10px !important; /* Override any JavaScript positioning */
        right: 10px !important; /* Ensure full width minus margins */
        top: 50% !important; /* Center vertically */
        transform: translateY(-50%);
        border-radius: 8px;
    }

    .subnet-card-body {
        padding: 10px 15px;
    }

    .status-count-row {
        justify-content: center;
    }
}

.subnet-card-header {
    padding: 20px;
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, #222, #2a2a2a);
}

.subnet-card-header.ok {
    background: linear-gradient(135deg, #3b4032, #2a2a2a);
    border-bottom: 1px solid rgba(205, 224, 107, 0.3);
}

.subnet-card-header.warning {
    background: linear-gradient(135deg, #40382a, #2a2a2a);
    border-bottom: 1px solid rgba(255, 165, 0, 0.3);
}

.subnet-card-header.critical,
.subnet-card-header.down {
    background: linear-gradient(135deg, #3c2e30, #2a2a2a);
    border-bottom: 1px solid rgba(212, 29, 40, 0.3);
}

.subnet-card-header.unknown,
.subnet-card-header.unreachable {
    background: linear-gradient(135deg, #353739, #2a2a2a);
    border-bottom: 1px solid rgba(100, 116, 139, 0.3);
}

.subnet-card-header.pending {
    background: linear-gradient(135deg, #373737, #2a2a2a);
    border-bottom: 1px solid rgba(128, 128, 128, 0.3);
}

.subnet-card-title {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #f0f0f0;
}

.subnet-card-subtitle {
    font-size: 14px;
    color: #bbb;
    margin: 0;
}

.subnet-card-close {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 20px;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    line-height: 1;
    transition: color 0.2s ease;
}

.subnet-card-close:hover {
    color: #fff;
}

.subnet-card-body {
    padding: 15px 20px;
}

.subnet-card-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.subnet-status-badge {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 10px;
    border-radius: 12px;
    color: white;
    margin-right: 15px;
}

.subnet-status-badge.ok {
    background-color: #cde06b;
    color: #333;
}

.subnet-status-badge.warning {
    background-color: #ffa500;
}

.subnet-status-badge.critical,
.subnet-status-badge.down {
    background-color: #d41d28;
}

.subnet-status-badge.unknown,
.subnet-status-badge.unreachable {
    background-color: #64748b;
}

.subnet-status-badge.pending {
    background-color: #808080;
}

.subnet-counts {
    display: flex;
    flex-grow: 1;
    justify-content: space-around;
}

.count-item {
    text-align: center;
    display: flex;
    flex-direction: column;
    min-width: 60px;
}

.count-item.highlight .count-value {
    color: #d41d28;
    font-weight: 700;
}

.count-value {
    font-size: 18px;
    font-weight: 600;
    color: #f0f0f0;
}

.count-label {
    font-size: 12px;
    color: #aaa;
    margin-top: 4px;
}

.status-count-section {
    margin-bottom: 15px;
}

.status-count-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #ccc;
}

.status-count-row {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 5px;
}

.subnet-detail-card .host-count,
.subnet-detail-card .service-count {
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 4px;
    color: #f0f0f0;
    font-weight: 500;
    display: inline-flex;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    min-width: 80px;
    justify-content: center;
    transition: transform 0.15s ease, box-shadow 0.15s ease;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.subnet-detail-card .host-count.ok {
    background-color: rgba(205, 224, 107, 0.1);
    border-left: 3px solid #cde06b;
}

.subnet-detail-card .host-count.down {
    background-color: rgba(212, 29, 40, 0.1);
    border-left: 3px solid #d41d28;
}

.subnet-detail-card .host-count.unreachable,
.subnet-detail-card .service-count.warning {
    background-color: rgba(255, 165, 0, 0.1);
    border-left: 3px solid #ffa500;
}

.subnet-detail-card .service-count.ok {
    background-color: rgba(205, 224, 107, 0.1);
    border-left: 3px solid #cde06b;
}

.subnet-detail-card .service-count.critical {
    background-color: rgba(212, 29, 40, 0.1);
    border-left: 3px solid #d41d28;
}

.subnet-detail-card .host-count.pending,
.subnet-detail-card .service-count.pending {
    background-color: rgba(128, 128, 128, 0.1);
    border-left: 3px solid #808080;
}

.subnet-detail-card .host-count.unknown,
.subnet-detail-card .service-count.unknown {
    background-color: rgba(100, 116, 139, 0.1);
    border-left: 3px solid #64748b;
}

.subnet-detail-card .host-count:hover,
.subnet-detail-card .service-count:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    background-color: rgba(255, 255, 255, 0.08);
}

.subnet-card-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.subnet-card-btn {
    padding: 8px 16px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #f0f0f0;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
}

.subnet-card-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.subnet-card-btn.primary {
    background: rgba(255, 255, 255, 0.1);
    color: #f0f0f0;
    border-color: rgba(255, 255, 255, 0.2);
}

.subnet-card-btn.primary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Simple APM Progress Indicator - blends with breadcrumbs */
.apm-progress-simple {
    display: none;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-left: 15px;
    padding-left: 15px;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.apm-progress-simple .apm-label {
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
}

.apm-progress-simple i {
    color: #cde06b;
    font-size: 13px;
    margin-right: 2px;
}

.apm-progress-simple #apm-progress-count {
    color: #fff;
    font-weight: 500;
}

/* Multi-select functionality styles */
.host-bubble.selected {
    stroke: #4da6ff !important;
    stroke-width: 3px !important;
    fill: rgba(77, 166, 255, 0.3) !important;
    filter: drop-shadow(0 0 8px rgba(77, 166, 255, 0.6));
}

.selection-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #2a2a2a;
    border: 2px solid #4da6ff;
    border-radius: 8px;
    padding: 10px 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: none;
    align-items: center;
    gap: 10px;
    font-family: 'Calibri', sans-serif;
    color: #e0e0e0;
}

.selection-count {
    font-weight: 600;
    color: #4da6ff;
}

.clear-selection-btn {
    background-color: #404040;
    color: #e0e0e0;
    border: 1px solid #777;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.clear-selection-btn:hover {
    background-color: #505050;
    border-color: #777;
}

/* Mobile responsive adjustments for selection indicator */
@media (max-width: 768px) {
    .selection-indicator {
        top: 10px;
        right: 10px;
        left: 10px;
        padding: 8px 12px;
        font-size: 14px;
        justify-content: space-between;
    }

    .clear-selection-btn {
        padding: 6px 10px;
        font-size: 12px;
    }
}

.multi-select-help {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    cursor: help;
    transition: all 0.2s ease;
}

.multi-select-help:hover {
    background-color: rgba(255, 255, 255, 0.15);
    color: #fff;
}

.multi-select-help i {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

/* ===== Infrastructure Select Wrapper ===== */
.infra-select-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}

.infra-select-wrapper .form-control {
    flex: 1;
}

.add-infra-btn {
    margin-left: 5px;
    padding: 8px;
    font-size: 14px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    height: 38px;
    width: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.add-infra-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.add-infra-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.2);
}

.add-infra-btn i {
    color: #fff;
    font-size: 14px;
}

/* ===== Parent-child arrow styling ===== */
.parent-child-arrow {
    stroke: #cde06b;
    stroke-width: 2px;
    stroke-dasharray: 6 4;
    animation: dashMove 1.6s linear infinite;
}

@keyframes dashMove {
    to {
        stroke-dashoffset: 40;
    }
}

/* ===== Speed label styling ===== */
.speed-label {
    fill: #e0e0e0;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.7);
    padding: 2px 4px;
    border-radius: 3px;
    white-space: nowrap;
}

/* ===== Speed tooltip styling ===== */
.speed-tooltip-container {
    background-color: #2a2a2a;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    color: #e0e0e0;
    font-family: 'Calibri', sans-serif;
    font-size: 13px;
    min-width: 150px;
    z-index: 10000;
}

.speed-tooltip .tooltip-header {
    font-weight: 600;
    color: #fff;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.speed-tooltip .tooltip-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    width: 100%;
}

.speed-tooltip .tooltip-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    white-space: nowrap;
    margin-right: 20px;
}

.speed-tooltip .tooltip-value {
    color: #fff;
    font-weight: 400;
    text-align: right;
    white-space: nowrap;
}

.speed-tooltip .tooltip-separator {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    margin: 2px 0;
}

/* --- Dropdown submenu styles --- */
.header-dropdown {
    position: relative;
}
/* Make Menu link a full-width row */
.header-dropdown > .header-button {
    display: block;
    width: 100%;
    box-sizing: border-box;
}

/* Show submenu on click (via .show class) */
.header-dropdown .header-dropdown-content.show {
    display: block;
}

.header-dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    background: #2a2a2a; /* match header background */
    border: 1px solid #717171;
    min-width: 200px;
    z-index: 1000;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.25);
    padding: 0;
    overflow: hidden;
}

.header-dropdown-content .header-button {
    display: block;
    width: 100%;
    padding: 10px 20px;
    text-align: left;
    white-space: nowrap;
    box-sizing: border-box;
}

.header-dropdown-content .header-button:hover {
    background: rgba(255,255,255,0.1);
}

/* Hover for Menu toggle */
.header-dropdown > .header-button:hover {
    background: rgba(255,255,255,0.1);
}



/* Connections Toggle Button - Use same style as other toggles */
.connections-toggle {
    display: inline-block;
    padding: 4px 8px;
    margin: 0 2px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    /* Default (inactive) — darker for inverted look */
    background-color: #2b2b2b;
    border: 1px solid rgba(255,255,255,0.07);
    transition: all 0.2s ease;
    text-decoration: none;
    min-width: 20px;
    text-align: center;
}

.connections-toggle:hover {
    /* Slightly lighter on hover but still dark */
    background-color: #393939;
}

/* Remove default focus ring for toggle pills */
.connections-toggle:focus {
    outline: none;
    box-shadow: none;
}

.connections-toggle.active {
    /* Active uses #555 as requested; keep text white for contrast */
    background-color: #555;
    border-color: rgba(255,255,255,0.2);
    color: #fff;
}

.connections-toggle.active:hover {
    background-color: #666;
}

.connections-toggle i {
    color: white;
    font-size: 11px;
}

/* ===== Host Group Modal Styles ===== */
#hostgroup-modal .formModal-content {
    max-width: 400px !important;
    max-height: 500px !important;
    overflow: hidden;
}

/* ===== Multi-select Modal Styles ===== */
.modal-multi-select-controls {
    display: none;
    justify-content: space-between;
    align-items: center;
    padding: 10px 22px;
    background-color: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== Ellipsis Dropdown Styles ===== */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    margin-right: 0 !important;
    transition: all 0.2s ease;
}

.dropdown-toggle i {
    color: rgba(255, 255, 255, 0.6);
    transition: color 0.2s ease;
}

.dropdown-toggle:hover i {
    color: #fff;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #2a2a2a;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 150px;
    padding: 4px 0;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 8px 12px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    color: #e0e0e0;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-item i {
    margin-right: 8px;
    width: 14px;
    text-align: center;
}

.multi-select-info {
    font-weight: 600;
    color: #e0e0e0;
}

.multi-select-actions {
    display: flex;
    gap: 10px;
}

.multi-select-actions button {
    padding: 8px 16px;
    margin-left: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #f0f0f0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.multi-select-actions button:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.host-checkbox {
    margin: 0;
    cursor: pointer;
    display: block;
    margin: 0 auto;
}

#checkboxHeader {
    width: 30px;
    text-align: center;
    top: -2px;
}

.host-table td:first-child {
    text-align: center;
}

/* Make deep scan and refresh icons match X button styling in dark theme */
.deep-scan-btn {
    color: rgba(255, 255, 255, 0.6) !important;
    transition: color 0.2s ease;
}

.deep-scan-btn:hover {
    color: #fff !important;
}

.modal-header button i.fa-refresh {
    color: rgba(255, 255, 255, 0.6) !important;
    transition: color 0.2s ease;
}

.modal-header button:hover i.fa-refresh {
    color: #fff !important;
}

#hostgroup-modal h3 {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 15px 0;
}

/* Context info styling */
#hostgroup-modal .context-info {
    margin-bottom: 15px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    border-left: 3px solid #cde06b;
}

#hostgroup-search {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
}

#hostgroup-search:focus {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    outline: none;
}

#hostgroup-search::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

#add-hostgroup-btn {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
    transition: all 0.2s ease;
}

#add-hostgroup-btn:hover {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-1px);
}

#hostgroup-list {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.hostgroup-item {
    color: #fff !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.hostgroup-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.hostgroup-item:last-child {
    border-bottom: none !important;
}

/* Custom scrollbar for hostgroup list */
#hostgroup-list::-webkit-scrollbar {
    width: 6px;
}

#hostgroup-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

#hostgroup-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

#hostgroup-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}