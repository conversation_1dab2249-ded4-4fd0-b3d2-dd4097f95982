/* Table View Styles - Dark Theme */

/* Minor layout tweaks specific to table view sections */
html, body { 
    height: 100%; 
}

body { 
    margin: 0; 
    display: flex; 
    flex-direction: column; 
    min-height: 100vh; 
    overflow: hidden; 
}

.tableview-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 20px;
    align-items: start;
    padding: 16px;
    max-width: 100%;
}

.tableview-section {
    background: #1e1e1e;
    border-radius: 6px;
    box-shadow: var(--card-shadow, 0 1px 3px rgba(0,0,0,0.3));
    overflow: hidden;
    min-width: 0;
    max-width: 100%;
    border: 1px solid #333;
}

.tableview-section h3 {
    margin: 0;
    padding: 8px 12px;
    background: #252525;
    border-bottom: 1px solid #333;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 16px;
    color: #ffffff;
}

.tableview-section .hostlist-table { 
    margin: 0; 
    table-layout: fixed; 
    width: 100%; 
    border-collapse: collapse;
}

.tableview-section .hostlist-table th,
.tableview-section .hostlist-table td {
    padding: 6px 8px;
    text-align: left;
    vertical-align: middle;
    word-wrap: break-word;
    overflow-wrap: break-word;
    font-size: 13px;
}

/* Center status and services columns */
.tableview-section .hostlist-table .col-status,
.tableview-section .hostlist-table .col-service {
    text-align: center;
}

.tableview-section .hostlist-table th {
    background: #252525;
    font-weight: 600;
    border-bottom: 1px solid #333;
    font-size: 12px;
    color: #ffffff;
}

/* Label styles for search fields */
.hostlist-search-container .search-input-wrapper label,
.hostlist-search-container .hostgroup-selector label,
.hostlist-search-container .servicegroup-selector label {
    display: inline-block;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-right: 8px;
    font-weight: 500;
    white-space: nowrap;
}

/* Ensure selectors with labels use flexbox for proper alignment */
.hostlist-search-container .hostgroup-selector,
.hostlist-search-container .servicegroup-selector {
    display: flex;
    align-items: center;
}

.tableview-section .hostlist-table td {
    color: #e0e0e0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.svc-badges {
    display: inline-flex; 
    gap: 4px; 
    align-items: center; 
    flex-wrap: wrap;
    margin: 1px 0;
}

.svc-badge { 
    padding: 2px 6px; 
    border-radius: 8px; 
    font-size: 10px; 
    font-weight: 500;
    white-space: nowrap;
}

.svc-badge.ok { 
    background: rgba(205, 224, 107, 0.2); 
    color: #cde06b; 
}

.svc-badge.warning { 
    background: rgba(255, 165, 0, 0.2); 
    color: #ffa500; 
}

.svc-badge.critical { 
    background: rgba(212, 29, 40, 0.2); 
    color: #d41d28; 
}

.svc-badge.unknown { 
    background: rgba(100, 116, 139, 0.2); 
    color: #64748b; 
}

.svc-badge.pending { 
    background: rgba(128, 128, 128, 0.2); 
    color: #808080; 
}

.tableview-empty { 
    padding: 24px; 
    text-align: center; 
    opacity: 0.7; 
    color: #aaaaaa;
}

.hostlist-status { 
    display: inline-block; 
    white-space: nowrap; 
    max-width: none; 
    padding: 3px 6px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 12px;
}

.hostlist-container { 
    flex: 1; 
    overflow: auto; 
    padding: 0; 
    margin: 0;
    background-color: #1a1a1a;
    display: flex;
    flex-direction: column;
}

.tableview-content { 
    padding: 4px; 
    margin: 0;
    overflow-x: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Loading indicator centering for tableview */
.tableview-content .hostlist-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    color: #aaaaaa;
    gap: 20px;
}

/* Custom scrollbar for dark theme */
.tableview-content::-webkit-scrollbar {
    width: 8px;
}

.tableview-content::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 4px;
}

.tableview-content::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.tableview-content::-webkit-scrollbar-thumb:hover {
    background: #777;
}

.tableview-content::-webkit-scrollbar-thumb:active {
    background: #777;
}

/* Host cell content styling */
.host-cell-content {
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 0;
    overflow: hidden;
}

.host-cell-content .hostname {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
    color: #e0e0e0;
}

/* Only highlight hostname when down */
.host-cell-content .hostname.down {
    color: #d41d28;
    font-weight: 600;
}

.host-cell-content .graph-icon {
    flex-shrink: 0;
    margin-right: 2px;
    font-size: 12px;
    color: #999;
}

.host-cell-content .graph-icon:hover {
    color: #4a7fbe;
}

/* Status column styling */
.col-status .hostlist-status {
    display: inline-block;
    padding: 3px 6px;
    border-radius: 4px;
    font-weight: 500;
    text-align: center;
    min-width: 50px;
    font-size: 12px;
}

/* Service badges container */
.col-service .svc-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    align-items: center;
}

/* Table row hover effects for dark theme */
.tableview-section .hostlist-table .hostlist-host-row:hover {
    background-color: rgba(255, 255, 255, 0.08) !important;
}

.tableview-section .hostlist-table .hostlist-host-row[data-status="down"] {
    background-color: rgba(212, 29, 40, 0.15);
}

.tableview-section .hostlist-table .hostlist-host-row[data-status="down"]:hover {
    background-color: rgba(212, 29, 40, 0.2) !important;
}

.tableview-section .hostlist-table .hostlist-host-row[data-status="unknown"] {
    background-color: rgba(100, 116, 139, 0.15);
}

.tableview-section .hostlist-table .hostlist-host-row[data-status="unknown"]:hover {
    background-color: rgba(100, 116, 139, 0.2) !important;
}

/* Minimal styles for draggable items - using reports.css for modal */
.draggable-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 8px;
}

/* Custom scrollbar for draggable list - dark theme */
.draggable-list::-webkit-scrollbar {
    width: 6px;
}

.draggable-list::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 4px;
}

.draggable-list::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.draggable-list::-webkit-scrollbar-thumb:hover {
    background: #777;
}

.draggable-list::-webkit-scrollbar-thumb:active {
    background: #777;
}

.draggable-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #2c2c2c;
    border: 2px solid #444;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: grab;
}

.draggable-item:active {
    cursor: grabbing;
}

.draggable-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    z-index: 1000;
}

.draggable-item.drag-above {
    margin-top: 8px;
    position: relative;
}

.draggable-item.drag-above::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 0;
    right: 0;
    height: 3px;
    background: #007bff;
    border-radius: 2px;
}

.draggable-item.drag-below {
    margin-bottom: 8px;
    position: relative;
}

.draggable-item.drag-below::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    right: 0;
    height: 3px;
    background: #007bff;
    border-radius: 2px;
}

/* Highlight effect after moving */
.draggable-item.moved {
    animation: moveHighlight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes moveHighlight {
    0% {
        background-color: #1a4a8a;
    }
    50% {
        background-color: #1a4a8a;
    }
    100% {
        background-color: #2c2c2c;
    }
}

.draggable-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.group-name {
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
}

.group-host-count {
    font-size: 12px;
    color: #aaa;
}

.drag-handle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    color: #888;
    cursor: grab;
    transition: color 0.2s ease;
}

.drag-handle:hover {
    color: #007bff;
}

.drag-instructions {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.3);
    border-radius: 4px;
    color: #007bff;
    font-size: 12px;
    margin-bottom: 12px;
}

.drag-instructions i {
    font-size: 14px;
}





/* Table order controls */
.table-order-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.table-order-controls .generate-btn {
    background: transparent;
    border: 1px solid #555;
    color: #aaa;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.table-order-controls .generate-btn:hover {
    background-color: #444;
    color: #fff;
}

.table-order-controls .generate-btn:active {
    background-color: #333;
    transform: translateY(1px);
}

/* Disabled button states for table order controls */
.table-order-controls .generate-btn:disabled {
    background-color: #2a2a2a;
    color: #666;
    border-color: #444;
    cursor: not-allowed;
    opacity: 0.6;
}

.table-order-controls .generate-btn:disabled:hover {
    background-color: #2a2a2a;
    color: #666;
    border-color: #444;
    transform: none;
}

.table-order-controls .generate-btn i {
    font-size: 11px;
}

/* Status colors for dark theme */
.hostlist-status.ok {
    background-color: rgba(205, 224, 107, 0.2);
    color: #cde06b;
}

.hostlist-status.down {
    background-color: rgba(212, 29, 40, 0.2);
    color: #d41d28;
}

.hostlist-status.unknown {
    background-color: rgba(100, 116, 139, 0.2);
    color: #64748b;
}

.hostlist-status.warning {
    background-color: rgba(255, 165, 0, 0.2);
    color: #ffa500;
}

.hostlist-status.critical {
    background-color: rgba(212, 29, 40, 0.2);
    color: #d41d28;
}

.hostlist-status.pending {
    background-color: rgba(128, 128, 128, 0.2);
    color: #808080;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tableview-sections {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 8px;
    }
    .tableview-section .hostlist-table th,
    .tableview-section .hostlist-table td {
        padding: 5px 6px;
    }
    
    .hostlist-search-container {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .table-order-controls {
        order: -1;
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .tableview-sections {
        padding: 6px;
    }
    .tableview-section h3 {
        padding: 8px 10px;
        font-size: 13px;
    }
    
    .generate-btn {
        padding: 8px 16px;
        font-size: 13px;
    }
}

/* Modal styles - matching reports.css filters modal */
.sr-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.sr-modal-content {
    background: #252525;
    padding: 25px 30px;
    border-radius: 8px;
    min-width: 320px;
    max-width: 1200px;
    max-height: 90vh;
    color: #fff;
    overflow-y: auto;
}

.sr-modal-content h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 20px;
}

.sr-close {
    float: right;
    font-size: 28px;
    cursor: pointer;
    color: #aaa;
}

.sr-close:hover {
    color: #fff;
}

.sr-field {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 15px;
}

.sr-field label {
    font-size: 14px;
    color: #e0e0e0;
    font-weight: 500;
}

.sr-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #444;
}

/* Time shortcuts styling - matching reports.css */
.time-shortcuts {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 5px;
}

.time-shortcut-btn {
    background: #333;
    border: 1px solid #555;
    color: #e0e0e0;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.time-shortcut-btn:hover {
    background: #444;
    border-color: #666;
    color: #fff;
}

.time-shortcut-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

/* Generate button styling - matching reports.css */
.generate-btn {
    background: #444;
    background-color: #444;
    color: #f8f9fa;
    padding: 6px 10px;
    min-width: 32px;
    height: 32px;
    border: 1px solid #888;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.generate-btn:hover {
    background-color: #555;
    border: 1px solid #888;
    border-radius: 4px;
}
