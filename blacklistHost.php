<?php
include "loadenv.php";

// Global flag to optionally skip per-request verification (used for bulk operations)
$GLOBALS['SKIP_VERIFY'] = (isset($_GET['skipVerify']) && $_GET['skipVerify'] == '1');

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "blesk";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();

    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "username" => $row["username"],
            "password" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }

    $conn->close();

    return $userCredentials;
}

function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getDatabaseConnectionNagiosql() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "db_nagiosql_v3";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getAllServicesForHost($ip) {
    try {
        $nagiosConn = getDatabaseConnectionNagiosql();
        
        // 1. Get host ID from tbl_host where address matches the IP
        $hostQuery = "SELECT id FROM tbl_host WHERE address = ?";
        $hostStmt = $nagiosConn->prepare($hostQuery);
        $hostStmt->bind_param("s", $ip);
        $hostStmt->execute();
        $hostResult = $hostStmt->get_result();
        
        if ($hostResult->num_rows === 0) {
            $hostStmt->close();
            $nagiosConn->close();
            return array(); // No host found
        }
        
        $hostRow = $hostResult->fetch_assoc();
        $hostId = $hostRow['id'];
        $hostStmt->close();
        
        // 2. Get all services linked to this host
        $servicesQuery = "
            SELECT DISTINCT s.service_description 
            FROM tbl_service s 
            INNER JOIN tbl_lnkServiceToHost lsh ON s.id = lsh.idMaster 
            WHERE lsh.idSlave = ? AND s.register = '1' AND s.active = '1'
        ";
        $servicesStmt = $nagiosConn->prepare($servicesQuery);
        $servicesStmt->bind_param("i", $hostId);
        $servicesStmt->execute();
        $servicesResult = $servicesStmt->get_result();
        
        $services = array();
        while ($serviceRow = $servicesResult->fetch_assoc()) {
            $services[] = $serviceRow['service_description'];
        }
        $servicesStmt->close();
        $nagiosConn->close();
        
        return $services;
        
    } catch (Exception $e) {
        error_log("Error getting services for host: " . $e->getMessage());
        return array();
    }
}

function simulateVerifyActions($hostname) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        // Initialize cURL session with cookies
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        // Login to Nagios
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => $_ENV['NAGIOS_USER'],
            'tfPassword' => $_ENV['NAGIOS_PASS'],
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Login failed: ' . curl_error($ch));

        // Handle redirect
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        // Perform verification actions
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if(curl_errno($ch)) throw new Exception("Action $action failed");
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log($e->getMessage());
    }
}

function deleteServiceDirectly($ip, $serviceToDelete) {
    try {
        $nagiosConn = getDatabaseConnectionNagiosql();
        
        // 1. Get host ID from tbl_host where address matches the IP
        $hostQuery = "SELECT id FROM tbl_host WHERE address = ?";
        $hostStmt = $nagiosConn->prepare($hostQuery);
        $hostStmt->bind_param("s", $ip);
        $hostStmt->execute();
        $hostResult = $hostStmt->get_result();
        
        if ($hostResult->num_rows === 0) {
            $hostStmt->close();
            $nagiosConn->close();
            throw new Exception("Host with IP $ip not found in Nagios database");
        }
        
        $hostRow = $hostResult->fetch_assoc();
        $hostId = $hostRow['id'];
        $hostStmt->close();
        
        // Support both single service and array of services
        $servicesToDelete = is_array($serviceToDelete) ? $serviceToDelete : array($serviceToDelete);
        $deletedCount = 0;
        
        foreach ($servicesToDelete as $service) {
            // 2. Get service IDs and config_name from tbl_service where service_description matches
            $serviceQuery = "SELECT id, config_name FROM tbl_service WHERE service_description = ? AND register = '1' AND active = '1'";
            $serviceStmt = $nagiosConn->prepare($serviceQuery);
            $serviceStmt->bind_param("s", $service);
            $serviceStmt->execute();
            $serviceResult = $serviceStmt->get_result();
            
            if ($serviceResult->num_rows === 0) {
                error_log("Service $service not found for host $ip");
                $serviceStmt->close();
                continue; // Skip to next service
            }
            
            $serviceData = array();
            while ($serviceRow = $serviceResult->fetch_assoc()) {
                $serviceData[] = array(
                    'id' => $serviceRow['id'],
                    'config_name' => $serviceRow['config_name']
                );
            }
            $serviceStmt->close();
            
            $serviceDeleted = false;
            
            // 3. Delete from tbl_lnkServiceToHost where idMaster is service ID and idSlave is host ID
            foreach ($serviceData as $serviceInfo) {
                $serviceId = $serviceInfo['id'];
                $configName = $serviceInfo['config_name'];
                
                $deleteQuery = "DELETE FROM tbl_lnkServiceToHost WHERE idMaster = ? AND idSlave = ?";
                $deleteStmt = $nagiosConn->prepare($deleteQuery);
                $deleteStmt->bind_param("ii", $serviceId, $hostId);
                $deleteResult = $deleteStmt->execute();
                
                if ($deleteResult && $deleteStmt->affected_rows > 0) {
                    $serviceDeleted = true;
                    $deletedCount++;
                    
                    // 4. Check if the service is still linked to any host
                    $checkQuery = "SELECT COUNT(*) as count FROM tbl_lnkServiceToHost WHERE idMaster = ?";
                    $checkStmt = $nagiosConn->prepare($checkQuery);
                    $checkStmt->bind_param("i", $serviceId);
                    $checkStmt->execute();
                    $checkResult = $checkStmt->get_result();
                    $checkRow = $checkResult->fetch_assoc();
                    $checkStmt->close();
                    
                    // 5. If no more links, update tbl_service to set register='0' and active='0'
                    if ($checkRow['count'] == 0) {
                        // Check if config_name starts with "imp_" or "azure_"
                        if ($configName && (strpos($configName, 'imp_') === 0 || strpos($configName, 'azure_') === 0)) {
                            // For imp_ or azure_ services, check if any other services have the same config_name
                            $checkConfigQuery = "SELECT COUNT(*) as count FROM tbl_service WHERE config_name = ? AND id != ?";
                            $checkConfigStmt = $nagiosConn->prepare($checkConfigQuery);
                            $checkConfigStmt->bind_param("si", $configName, $serviceId);
                            $checkConfigStmt->execute();
                            $checkConfigResult = $checkConfigStmt->get_result();
                            $checkConfigRow = $checkConfigResult->fetch_assoc();
                            $checkConfigStmt->close();
                            
                            // If no other services have this config_name, delete the service and config file
                            if ($checkConfigRow['count'] == 0) {
                                // Delete the service from tbl_service
                                $deleteServiceQuery = "DELETE FROM tbl_service WHERE id = ?";
                                $deleteServiceStmt = $nagiosConn->prepare($deleteServiceQuery);
                                $deleteServiceStmt->bind_param("i", $serviceId);
                                $deleteServiceStmt->execute();
                                $deleteServiceStmt->close();
                                
                                // Delete the config file
                                $configPath = "/etc/nagiosql/services/" . $configName . ".cfg";
                                if (file_exists($configPath)) {
                                    if (!unlink($configPath)) {
                                        error_log("Failed to delete config file: $configPath");
                                    } else {
                                        error_log("Successfully deleted config file: $configPath");
                                    }
                                }
                            } else {
                                // Other services have this config_name, just delete this service
                                $deleteServiceQuery = "DELETE FROM tbl_service WHERE id = ?";
                                $deleteServiceStmt = $nagiosConn->prepare($deleteServiceQuery);
                                $deleteServiceStmt->bind_param("i", $serviceId);
                                $deleteServiceStmt->execute();
                                $deleteServiceStmt->close();
                            }
                        } else {
                            // For non-imp_/azure_ services, just set register='0' and active='0'
                            $updateQuery = "UPDATE tbl_service SET register = '0', active = '0' WHERE id = ?";
                            $updateStmt = $nagiosConn->prepare($updateQuery);
                            $updateStmt->bind_param("i", $serviceId);
                            $updateStmt->execute();
                            $updateStmt->close();
                            
                            // Delete the config file if it exists
                            if ($configName) {
                                $configPath = "/etc/nagiosql/services/" . $configName . ".cfg";
                                if (file_exists($configPath)) {
                                    if (!unlink($configPath)) {
                                        error_log("Failed to delete config file: $configPath");
                                    } else {
                                        error_log("Successfully deleted config file: $configPath");
                                    }
                                }
                            }
                        }
                    }
                }
                $deleteStmt->close();
            }
            
            if (!$serviceDeleted) {
                error_log("Failed to delete service $service for host $ip");
            }
        }
        
        $nagiosConn->close();
        
        // 7. Run simulateVerifyActions after successful deletion
        if ($deletedCount > 0 && empty($GLOBALS['SKIP_VERIFY'])) {
            $hostname = getSelfIp();
            simulateVerifyActions($hostname);
        }
        
        return array(
            'success' => $deletedCount > 0,
            'deleted_count' => $deletedCount,
            'total_requested' => count($servicesToDelete)
        );
        
    } catch (Exception $e) {
        error_log("Error deleting service: " . $e->getMessage());
        return array(
            'success' => false,
            'error' => $e->getMessage()
        );
    }
}

function deleteServices($ip, $selfIP, $usr, $pwd) {
    try {
        // Get all services for this host
        $allServices = getAllServicesForHost($ip);
        
        if (empty($allServices)) {
            error_log("No services found for host $ip");
            return "No services found for host $ip";
        }
        
        // Delete all services using the deleteServiceDirectly function
        $result = deleteServiceDirectly($ip, $allServices);
        
        if ($result['success']) {
            error_log("Successfully deleted {$result['deleted_count']} services for host $ip");
            return "Successfully deleted {$result['deleted_count']} services for host $ip";
        } else {
            $errorMsg = isset($result['error']) ? $result['error'] : "Unknown error occurred";
            error_log("Failed to delete services for host $ip: $errorMsg");
            return "Failed to delete services for host $ip: $errorMsg";
        }
        
    } catch (Exception $e) {
        error_log("Error in deleteServices: " . $e->getMessage());
        return "Error: " . $e->getMessage();
    }
}

function checkHostIsUp($ip) {
    // If it's not a valid IP address, don't check if it's up - just return false to delete it
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
        return false;
    }
    
    // For valid IP addresses, use nmap to check if they're up
    $command = "sudo nmap -sn -PE --disable-arp-ping " . escapeshellarg($ip) . " 2>&1";
    $output = shell_exec($command);
    
    // Check if output contains "Host is up" which indicates the host is reachable
    return (strpos($output, "Host is up") !== false);
}

function deleteHost($ip, $selfIP, $usr, $pwd){
    try {
        $nagiosConn = getDatabaseConnectionNagiosql();
        
        // First get the real hostname from tbl_host before deleting
        $hostQuery = "SELECT host_name FROM tbl_host WHERE address = ?";
        $hostStmt = $nagiosConn->prepare($hostQuery);
        $hostStmt->bind_param("s", $ip);
        $hostStmt->execute();
        $hostResult = $hostStmt->get_result();
        
        $hostName = null;
        if ($hostResult->num_rows > 0) {
            $hostRow = $hostResult->fetch_assoc();
            $hostName = $hostRow['host_name'];
        }
        $hostStmt->close();
        
        // Delete the host from tbl_host where address matches the IP/hostname
        $deleteQuery = "DELETE FROM tbl_host WHERE address = ?";
        $deleteStmt = $nagiosConn->prepare($deleteQuery);
        $deleteStmt->bind_param("s", $ip);
        $deleteResult = $deleteStmt->execute();
        
        if ($deleteResult && $deleteStmt->affected_rows > 0) {
            error_log("Successfully deleted host $ip from Nagios database");
            
            // Delete the host config file if we have the hostname
            if ($hostName) {
                $configPath = "/etc/nagiosql/hosts/" . $hostName . ".cfg";
                if (file_exists($configPath)) {
                    if (!unlink($configPath)) {
                        error_log("Failed to delete host config file: $configPath");
                    } else {
                        error_log("Successfully deleted host config file: $configPath");
                    }
                } else {
                    error_log("Host config file not found: $configPath");
                }
            }
            
            // Run the doit.php script to verify changes
            if (empty($GLOBALS['SKIP_VERIFY'])) {
                $hostname = getSelfIp();
                simulateVerifyActions($hostname);
            }
            
        } else {
            error_log("Host $ip not found in Nagios database or already deleted");
        }
        
        $deleteStmt->close();
        $nagiosConn->close();
        
    } catch (Exception $e) {
        error_log("Error deleting host from Nagios database: " . $e->getMessage());
    }
}

function blacklistHostByIp() {
    if (isset($_GET['ip'])) {
        $ip = $_GET['ip'];
        $selfIP = getSelfIp();
        $adminUser = getUserCredentials();
        $usr = $adminUser["username"];
        $pwd = $adminUser["password"];

        // Step 1: Check if host is up using nmap
        $isHostUp = checkHostIsUp($ip);

        // Step 2: Connect to database
        $conn = getDatabaseConnection();

        // Delete from servicesPending table for all instances of this IP
        $deletePendingSql = "DELETE FROM servicesPending WHERE host_ip = ?";
        if ($deletePendingStmt = $conn->prepare($deletePendingSql)) {
            $deletePendingStmt->bind_param('s', $ip);
            if ($deletePendingStmt->execute()) {
                echo "Deleted from servicesPending for IP: $ip\n";
            } else {
                echo "Error deleting from servicesPending: " . $deletePendingStmt->error . "\n";
            }
            $deletePendingStmt->close();
        } else {
            echo "Error preparing deletePending query: " . $conn->error . "\n";
        }

        if ($isHostUp) {
            // Host is up, blacklist all instances of this IP
            $sql = "UPDATE hosts SET blacklist = 1, hostgroup = NULL, pending_count = 0, ok_count = 0, warning_count = 0, unknown_count = 0, critical_count = 0 WHERE ip = ?";
            if ($stmt = $conn->prepare($sql)) {
                $stmt->bind_param('s', $ip);
                
                if ($stmt->execute()) {
                    // Always delete from Nagios since we're blacklisting all instances
                    deleteServices($ip, $selfIP, $usr, $pwd);
                    deleteHost($ip, $selfIP, $usr, $pwd);
                    echo "Success: All instances of host blacklisted";
                } else {
                    echo "Error blacklisting host: " . $stmt->error;
                }
                
                $stmt->close();
            } else {
                echo "Error preparing blacklist query: " . $conn->error;
            }
        } else {
            // Host is down, delete all instances of this IP from the database
            $sql = "DELETE FROM hosts WHERE ip = ?";
            if ($stmt = $conn->prepare($sql)) {
                $stmt->bind_param('s', $ip);
                
                if ($stmt->execute()) {
                    // Always delete from Nagios since we're removing all instances
                    deleteServices($ip, $selfIP, $usr, $pwd);
                    deleteHost($ip, $selfIP, $usr, $pwd);
                    echo "Success: All instances of host deleted (unreachable)";
                } else {
                    echo "Error deleting host: " . $stmt->error;
                }
                
                $stmt->close();
            } else {
                echo "Error preparing delete query: " . $conn->error;
            }
        }

        $conn->close();
    } else {
        echo "IP parameter is required.";
    }
}

blacklistHostByIp();
?>