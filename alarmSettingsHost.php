<?php
include "loadenv.php";
include 'theme_loader.php'; // Include the theme loader
// Database connection
$db_host = $_ENV["DB_SERVER"];
$db_user = $_ENV["DB_USER"];
$db_pass = $_ENV["DB_PASSWORD"];
$db_name = 'db_nagiosql_v3';
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Prevent caching
header("Cache-Control: no-cache, must-revalidate");

// Fetch host data based on hostname GET parameter
$hostname = $_GET['hostname'] ?? '';
$data = [];
$host_id = null;
$included_contact_groups = [];
$excluded_contact_groups = [];
$included_contacts = [];
$excluded_contacts = [];
$all_contact_groups = [];
$all_contacts = [];
$timeperiods = [];

// Fetch all time periods
$sql = "SELECT id, timeperiod_name, alias FROM tbl_timeperiod ORDER BY timeperiod_name";
$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) {
    $timeperiods[] = $row;
}

if (!empty($hostname)) {
    // Get host ID and data
    $sql = "SELECT * FROM tbl_host WHERE host_name = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $hostname);
    $stmt->execute();
    $result = $stmt->get_result();
    $data = $result->fetch_assoc() ?? [];
    $host_id = $data['id'] ?? null;
    $stmt->close();

    if ($host_id) {
        // Fetch linked contact groups with exclude status
        $sql = "SELECT idSlave, `exclude` FROM tbl_lnkHostToContactgroup WHERE idMaster = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $host_id);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $sql_cg = "SELECT alias FROM tbl_contactgroup WHERE id = ?";
            $stmt_cg = $conn->prepare($sql_cg);
            $stmt_cg->bind_param("i", $row['idSlave']);
            $stmt_cg->execute();
            $cg_result = $stmt_cg->get_result();
            if ($cg_row = $cg_result->fetch_assoc()) {
                if ($row['exclude'] == 1) {
                    $excluded_contact_groups[] = $cg_row['alias'];
                } else {
                    $included_contact_groups[] = $cg_row['alias'];
                }
            }
            $stmt_cg->close();
        }
        $stmt->close();

        // Fetch linked contacts with exclude status
        $sql = "SELECT idSlave, `exclude` FROM tbl_lnkHostToContact WHERE idMaster = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $host_id);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $sql_c = "SELECT contact_name FROM tbl_contact WHERE id = ?";
            $stmt_c = $conn->prepare($sql_c);
            $stmt_c->bind_param("i", $row['idSlave']);
            $stmt_c->execute();
            $c_result = $stmt_c->get_result();
            if ($c_row = $c_result->fetch_assoc()) {
                if ($row['exclude'] == 1) {
                    $excluded_contacts[] = $c_row['contact_name'];
                } else {
                    $included_contacts[] = $c_row['contact_name'];
                }
            }
            $stmt_c->close();
        }
        $stmt->close();
    }

    // Fetch all contact groups
    $sql = "SELECT alias FROM tbl_contactgroup";
    $result = $conn->query($sql);
    while ($row = $result->fetch_assoc()) {
        $all_contact_groups[] = $row['alias'];
    }

    // Fetch all contacts
    $sql = "SELECT contact_name FROM tbl_contact";
    $result = $conn->query($sql);
    while ($row = $result->fetch_assoc()) {
        $all_contacts[] = $row['contact_name'];
    }
}

// Simulate verification actions
function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function simulateVerifyActions($ip) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    try {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
        ]);

        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit' => 'Login'
        ]));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception('Login failed: ' . curl_error($ch));
        }

        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                throw new Exception("Action $action failed: " . curl_error($ch));
            }
        }
        curl_close($ch);
    } catch (Exception $e) {
        error_log("Error in simulateVerifyActions: " . $e->getMessage());
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($hostname) && $host_id) {
    $fields = [
        'notification_interval', 'notification_period', 'first_notification_delay',
        'notifications_enabled', 'contact_groups_tploptions', 'contacts_tploptions'
    ];

    $updates = [];
    $params = [];
    $types = '';

    // Handle contact_groups
    $new_included_contact_groups = !empty($_POST['included_contact_groups']) ? $_POST['included_contact_groups'] : [];
    $new_excluded_contact_groups = !empty($_POST['excluded_contact_groups']) ? $_POST['excluded_contact_groups'] : [];

    // Update tbl_lnkHostToContactgroup
    $stmt_del = $conn->prepare("DELETE FROM tbl_lnkHostToContactgroup WHERE idMaster = ?");
    $stmt_del->bind_param("i", $host_id);
    $stmt_del->execute();
    $stmt_del->close();

    foreach ($new_included_contact_groups as $cg_name) {
        $stmt_id = $conn->prepare("SELECT id FROM tbl_contactgroup WHERE alias = ?");
        $stmt_id->bind_param("s", $cg_name);
        $stmt_id->execute();
        $result = $stmt_id->get_result();
        if ($row = $result->fetch_assoc()) {
            $cg_id = $row['id'];
            $exclude = 0;
            $stmt_ins = $conn->prepare("INSERT INTO tbl_lnkHostToContactgroup (idMaster, idSlave, `exclude`) VALUES (?, ?, ?)");
            $stmt_ins->bind_param("iii", $host_id, $cg_id, $exclude);
            $stmt_ins->execute();
            $stmt_ins->close();
        }
        $stmt_id->close();
    }

    foreach ($new_excluded_contact_groups as $cg_name) {
        $stmt_id = $conn->prepare("SELECT id FROM tbl_contactgroup WHERE alias = ?");
        $stmt_id->bind_param("s", $cg_name);
        $stmt_id->execute();
        $result = $stmt_id->get_result();
        if ($row = $result->fetch_assoc()) {
            $cg_id = $row['id'];
            $exclude = 1;
            $stmt_ins = $conn->prepare("INSERT INTO tbl_lnkHostToContactgroup (idMaster, idSlave, `exclude`) VALUES (?, ?, ?)");
            $stmt_ins->bind_param("iii", $host_id, $cg_id, $exclude);
            $stmt_ins->execute();
            $stmt_ins->close();
        }
        $stmt_id->close();
    }

    // Handle contacts
    $new_included_contacts = !empty($_POST['included_contacts']) ? $_POST['included_contacts'] : [];
    $new_excluded_contacts = !empty($_POST['excluded_contacts']) ? $_POST['excluded_contacts'] : [];

    // Update tbl_lnkHostToContact
    $stmt_del = $conn->prepare("DELETE FROM tbl_lnkHostToContact WHERE idMaster = ?");
    $stmt_del->bind_param("i", $host_id);
    $stmt_del->execute();
    $stmt_del->close();

    foreach ($new_included_contacts as $c_name) {
        $stmt_id = $conn->prepare("SELECT id FROM tbl_contact WHERE contact_name = ?");
        $stmt_id->bind_param("s", $c_name);
        $stmt_id->execute();
        $result = $stmt_id->get_result();
        if ($row = $result->fetch_assoc()) {
            $c_id = $row['id'];
            $exclude = 0;
            $stmt_ins = $conn->prepare("INSERT INTO tbl_lnkHostToContact (idMaster, idSlave, `exclude`) VALUES (?, ?, ?)");
            $stmt_ins->bind_param("iii", $host_id, $c_id, $exclude);
            $stmt_ins->execute();
            $stmt_ins->close();
        }
        $stmt_id->close();
    }

    foreach ($new_excluded_contacts as $c_name) {
        $stmt_id = $conn->prepare("SELECT id FROM tbl_contact WHERE contact_name = ?");
        $stmt_id->bind_param("s", $c_name);
        $stmt_id->execute();
        $result = $stmt_id->get_result();
        if ($row = $result->fetch_assoc()) {
            $c_id = $row['id'];
            $exclude = 1;
            $stmt_ins = $conn->prepare("INSERT INTO tbl_lnkHostToContact (idMaster, idSlave, `exclude`) VALUES (?, ?, ?)");
            $stmt_ins->bind_param("iii", $host_id, $c_id, $exclude);
            $stmt_ins->execute();
            $stmt_ins->close();
        }
        $stmt_id->close();
    }

    // Handle notification_options
    $notification_options = !empty($_POST['notification_options']) ? implode(',', $_POST['notification_options']) : '';
    if ($notification_options !== ($data['notification_options'] ?? '')) {
        $updates[] = "notification_options = ?";
        $params[] = $notification_options;
        $types .= 's';
    }

    // Handle stalking_options
    $stalking_options = !empty($_POST['stalking_options']) ? implode(',', $_POST['stalking_options']) : '';
    if ($stalking_options !== ($data['stalking_options'] ?? '')) {
        $updates[] = "stalking_options = ?";
        $params[] = $stalking_options;
        $types .= 's';
    }

    // Handle other fields
    foreach ($fields as $field) {
        $value = $_POST[$field] ?? '';
        $current_value = $data[$field] ?? '';
        if ($value !== $current_value) {
            if ($value === '') {
                $updates[] = "$field = NULL";
            } else {
                $updates[] = "$field = ?";
                $params[] = $value;
                $types .= 's';
            }
        }
    }

    if (!empty($updates)) {
        $sql = "UPDATE tbl_host SET " . implode(', ', $updates) . " WHERE host_name = ?";
        $params[] = $hostname;
        $types .= 's';

        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param($types, ...$params);
            $stmt->execute();
            $stmt->close();
        }
    }

    simulateVerifyActions(getSelfIp());

    // Refresh data after update
    $sql = "SELECT * FROM tbl_host WHERE host_name = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $hostname);
    $stmt->execute();
    $result = $stmt->get_result();
    $data = $result->fetch_assoc() ?? [];
    $stmt->close();

    echo '<script>';
    echo 'if (window.self !== window.top) {';
    echo '  window.parent.location.reload();';
    echo '} else {';
    echo '  window.location.reload();';
    echo '}';
    echo '</script>';
    exit;
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alarm Settings - <?php echo htmlspecialchars($hostname ?: 'No Host'); ?></title>
    <link href="styles/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/advancedCommands.css">
</head>
<body>
    <div class="container">
        <h1><?php echo htmlspecialchars($hostname ?: 'No Host'); ?></h1>

        <?php if (empty($data) && !empty($hostname)): ?>
            <p class="no-data">No data found for hostname: <?php echo htmlspecialchars($hostname); ?></p>
        <?php endif; ?>

        <form method="POST">
            <!-- Contacts -->
            <div class="form-section">
                <h2>Contacts</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Included Contacts</label>
                        <select name="included_contacts[]" class="select2 included" multiple>
                            <?php foreach ($all_contacts as $contact): ?>
                                <option value="<?php echo htmlspecialchars($contact); ?>" <?php echo in_array($contact, $included_contacts) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($contact); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Excluded Contacts</label>
                        <select name="excluded_contacts[]" class="select2 excluded" multiple disabled>
                            <?php foreach ($all_contacts as $contact): ?>
                                <option value="<?php echo htmlspecialchars($contact); ?>" <?php echo in_array($contact, $excluded_contacts) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($contact); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Template Options</label>
                        <div class="radio-group">
                            <label><input type="radio" name="contacts_tploptions" value="" <?php echo ($data['contacts_tploptions'] ?? '') === '' ? 'checked' : ''; ?>> None</label>
                            <label><input type="radio" name="contacts_tploptions" value="1" <?php echo (string)($data['contacts_tploptions'] ?? '') === '1' ? 'checked' : ''; ?>> Null</label>
                            <label><input type="radio" name="contacts_tploptions" value="2" <?php echo (string)($data['contacts_tploptions'] ?? '') === '2' ? 'checked' : ''; ?>> Standard</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Groups -->
            <div class="form-section">
                <h2>Contact Groups</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Included Contact Groups</label>
                        <select name="included_contact_groups[]" class="select2 included" multiple>
                            <?php foreach ($all_contact_groups as $cg): ?>
                                <option value="<?php echo htmlspecialchars($cg); ?>" <?php echo in_array($cg, $included_contact_groups) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cg); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Excluded Contact Groups</label>
                        <select name="excluded_contact_groups[]" class="select2 excluded" multiple disabled>
                            <?php foreach ($all_contact_groups as $cg): ?>
                                <option value="<?php echo htmlspecialchars($cg); ?>" <?php echo in_array($cg, $excluded_contact_groups) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cg); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Template Options</label>
                        <div class="radio-group">
                            <label><input type="radio" name="contact_groups_tploptions" value="" <?php echo ($data['contact_groups_tploptions'] ?? '') === '' ? 'checked' : ''; ?>> None</label>
                            <label><input type="radio" name="contact_groups_tploptions" value="1" <?php echo (string)($data['contact_groups_tploptions'] ?? '') === '1' ? 'checked' : ''; ?>> Null</label>
                            <label><input type="radio" name="contact_groups_tploptions" value="2" <?php echo (string)($data['contact_groups_tploptions'] ?? '') === '2' ? 'checked' : ''; ?>> Standard</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="form-section">
                <h2>Notification Settings</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Notification Interval (s)</label>
                        <input type="number" name="notification_interval" min="0" value="<?php echo htmlspecialchars($data['notification_interval'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label>Notification Period</label>
                        <select name="notification_period">
                            <option value="0" <?php echo (string)($data['notification_period'] ?? '') === '0' ? 'selected' : ''; ?>>Null</option>
                            <?php foreach ($timeperiods as $tp): ?>
                                <option value="<?php echo $tp['id']; ?>" <?php echo (string)($data['notification_period'] ?? '') === (string)$tp['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($tp['timeperiod_name']); ?> (<?php echo htmlspecialchars($tp['alias']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Notification Options</label>
                        <div class="checkbox-group">
                            <?php $notif_options = explode(',', $data['notification_options'] ?? ''); ?>
                            <label><input type="checkbox" name="notification_options[]" value="d" <?php echo in_array('d', $notif_options) ? 'checked' : ''; ?>> Down</label>
                            <label><input type="checkbox" name="notification_options[]" value="u" <?php echo in_array('u', $notif_options) ? 'checked' : ''; ?>> Unreachable</label>
                            <label><input type="checkbox" name="notification_options[]" value="r" <?php echo in_array('r', $notif_options) ? 'checked' : ''; ?>> Recovery</label>
                            <label><input type="checkbox" name="notification_options[]" value="f" <?php echo in_array('f', $notif_options) ? 'checked' : ''; ?>> Flapping</label>
                            <label><input type="checkbox" name="notification_options[]" value="s" <?php echo in_array('s', $notif_options) ? 'checked' : ''; ?>> Scheduled Downtime</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>First Notification Delay (min)</label>
                        <input type="number" name="first_notification_delay" min="0" value="<?php echo htmlspecialchars($data['first_notification_delay'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label>Notifications Enabled</label>
                        <div class="radio-group">
                            <label><input type="radio" name="notifications_enabled" value="1" <?php echo (string)($data['notifications_enabled'] ?? '') === '1' ? 'checked' : ''; ?>> On</label>
                            <label><input type="radio" name="notifications_enabled" value="0" <?php echo (string)($data['notifications_enabled'] ?? '') === '0' ? 'checked' : ''; ?>> Off</label>
                            <label><input type="radio" name="notifications_enabled" value="2" <?php echo (string)($data['notifications_enabled'] ?? '') === '2' ? 'checked' : ''; ?>> Skip</label>
                            <label><input type="radio" name="notifications_enabled" value="3" <?php echo (string)($data['notifications_enabled'] ?? '') === '3' ? 'checked' : ''; ?>> Null</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stalking Settings -->
            <div class="form-section">
                <h2>Stalking Settings</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Stalking Options</label>
                        <div class="checkbox-group">
                            <?php $stalk_options = explode(',', $data['stalking_options'] ?? ''); ?>
                            <label><input type="checkbox" name="stalking_options[]" value="o" <?php echo in_array('o', $stalk_options) ? 'checked' : ''; ?>> Okay</label>
                            <label><input type="checkbox" name="stalking_options[]" value="d" <?php echo in_array('d', $stalk_options) ? 'checked' : ''; ?>> Down</label>
                            <label><input type="checkbox" name="stalking_options[]" value="u" <?php echo in_array('u', $stalk_options) ? 'checked' : ''; ?>> Unreachable</label>
                        </div>
                    </div>
                </div>
            </div>

            <button type="submit" id="save-button" class="save-button">Save Changes</button>
        </form>
    </div>

    <script src="functions/jquery-3.6.0.min.js"></script>
    <script src="functions/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for included items (blue)
            $('.select2.included').select2({
                placeholder: "Select included",
                allowClear: true,
                width: '100%'
            });

            // Initialize Select2 for excluded items (red)
            $('.select2.excluded').select2({
                placeholder: "Select excluded",
                allowClear: true,
                width: '100%'
            });

            // Flag to prevent recursive calls
            let isSyncing = false;

            // Sync function to disable options in one select if selected in the other
            function syncSelects(includedSelector, excludedSelector) {
                if (isSyncing) return; // Prevent recursion
                isSyncing = true;

                var $included = $(includedSelector);
                var $excluded = $(excludedSelector);
                var includedValues = $included.val() || [];
                var excludedValues = $excluded.val() || [];

                // Update disabled state in included select
                $included.find('option').each(function() {
                    var value = $(this).val();
                    $(this).prop('disabled', excludedValues.includes(value));
                });

                // Update disabled state in excluded select
                $excluded.find('option').each(function() {
                    var value = $(this).val();
                    $(this).prop('disabled', includedValues.includes(value));
                });

                // Trigger Select2 to update its internal state without reinitializing
                $included.trigger('change.select2');
                $excluded.trigger('change.select2');

                isSyncing = false;
            }

            // Bind change events
            $('select[name="included_contact_groups[]"]').on('change', function() {
                syncSelects('select[name="included_contact_groups[]"]', 'select[name="excluded_contact_groups[]"]');
            });
            $('select[name="excluded_contact_groups[]"]').on('change', function() {
                syncSelects('select[name="included_contact_groups[]"]', 'select[name="excluded_contact_groups[]"]');
            });
            $('select[name="included_contacts[]"]').on('change', function() {
                syncSelects('select[name="included_contacts[]"]', 'select[name="excluded_contacts[]"]');
            });
            $('select[name="excluded_contacts[]"]').on('change', function() {
                syncSelects('select[name="included_contacts[]"]', 'select[name="excluded_contacts[]"]');
            });

            // Initial sync
            syncSelects('select[name="included_contact_groups[]"]', 'select[name="excluded_contact_groups[]"]');
            syncSelects('select[name="included_contacts[]"]', 'select[name="excluded_contacts[]"]');

            // Update button text on submit
            $('#save-button').on('click', function() {
                $(this).text('Saving...');
            });
        });
    </script>
</body>
</html>