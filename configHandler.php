<?php
// configHandler.php - handles reading and writing bubblemap configuration preferences

header('Content-Type: application/json');

// Define the path to the configuration file
$configFile = __DIR__ . '/conf/bubblemap_configs';

// Function to parse configuration file
function parseConfigFile() {
    global $configFile;
    
    $config = [
        'viewType' => 'card',
        'connectionToggle' => 'on',
        'speedTextToggle' => 'on',
        'mainView' => 'bubble' // Add main view support
    ];
    
    if (!file_exists($configFile)) {
        return $config;
    }
    
    $content = trim(file_get_contents($configFile));
    
    // Check if it's the old format (just a single value)
    if (in_array($content, ['list', 'card'])) {
        $config['viewType'] = $content;
        return $config;
    }
    
    // Parse new format with multiple settings
    $lines = explode("\n", $content);
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        if (strpos($line, 'hostpage view type:') === 0) {
            $value = trim(str_replace('hostpage view type:', '', $line));
            if (in_array($value, ['list', 'card'])) {
                $config['viewType'] = $value;
            }
        } elseif (strpos($line, 'connection toggle:') === 0) {
            $value = trim(str_replace('connection toggle:', '', $line));
            if (in_array($value, ['on', 'off'])) {
                $config['connectionToggle'] = $value;
            }
        } elseif (strpos($line, 'speed text toggle:') === 0) {
            $value = trim(str_replace('speed text toggle:', '', $line));
            if (in_array($value, ['on', 'off'])) {
                $config['speedTextToggle'] = $value;
            }
        } elseif (strpos($line, 'main view:') === 0) {
            $value = trim(str_replace('main view:', '', $line));
            if (in_array($value, ['bubble', 'list', 'table', 'dashboards'])) {
                $config['mainView'] = $value;
            }
        }
    }
    
    return $config;
}

// Function to read current view type
function getViewType() {
    $config = parseConfigFile();
    return $config['viewType'];
}

// Function to write configuration file while preserving existing lines
function writeConfigFile($config) {
    global $configFile;
    
    // Read existing content to preserve other settings
    $existingLines = [];
    if (file_exists($configFile)) {
        $content = file_get_contents($configFile);
        $existingLines = explode("\n", $content);
        
        // Remove empty lines at the end
        while (!empty($existingLines) && trim(end($existingLines)) === '') {
            array_pop($existingLines);
        }
    }
    
    // Create a map of existing settings
    $existingSettings = [];
    foreach ($existingLines as $line) {
        $trimmed = trim($line);
        if (empty($trimmed)) continue;
        
        if (strpos($trimmed, 'hostpage view type:') === 0) {
            $existingSettings['hostpage_view_type'] = $line;
        } elseif (strpos($trimmed, 'connection toggle:') === 0) {
            $existingSettings['connection_toggle'] = $line;
        } elseif (strpos($trimmed, 'speed text toggle:') === 0) {
            $existingSettings['speed_text_toggle'] = $line;
        } elseif (strpos($trimmed, 'main view:') === 0) {
            $existingSettings['main_view'] = $line;
        } else {
            // Preserve any other lines we don't recognize
            $existingSettings['other'][] = $line;
        }
    }
    
    // Build new content
    $newLines = [];
    
    // Add our updated settings
    $newLines[] = "hostpage view type: " . $config['viewType'];
    $newLines[] = "connection toggle: " . $config['connectionToggle'];
    $newLines[] = "speed text toggle: " . $config['speedTextToggle'];
    
    // Preserve main view if it exists
    if (isset($existingSettings['main_view'])) {
        $newLines[] = $existingSettings['main_view'];
    }
    
    // Preserve any other lines
    if (isset($existingSettings['other'])) {
        foreach ($existingSettings['other'] as $otherLine) {
            $newLines[] = $otherLine;
        }
    }
    
    $content = implode("\n", $newLines) . "\n";
    $result = file_put_contents($configFile, $content);
    return $result !== false;
}

// Function to write view type
function setViewType($viewType) {
    if (!in_array($viewType, ['list', 'card'])) {
        return false;
    }
    
    $config = parseConfigFile();
    $config['viewType'] = $viewType;
    
    return writeConfigFile($config);
}

// Function to set connection toggle
function setConnectionToggle($toggle) {
    if (!in_array($toggle, ['on', 'off'])) {
        return false;
    }
    
    $config = parseConfigFile();
    $config['connectionToggle'] = $toggle;
    
    return writeConfigFile($config);
}

// Function to get connection toggle
function getConnectionToggle() {
    $config = parseConfigFile();
    return $config['connectionToggle'];
}

// Function to set speed text toggle
function setSpeedTextToggle($toggle) {
    if (!in_array($toggle, ['on', 'off'])) {
        return false;
    }

    $config = parseConfigFile();
    $config['speedTextToggle'] = $toggle;

    return writeConfigFile($config);
}

// Function to get speed text toggle
function getSpeedTextToggle() {
    $config = parseConfigFile();
    return $config['speedTextToggle'];
}

// Handle different request methods
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Check if specific parameter is requested
    $param = $_GET['param'] ?? null;
    
    if ($param === 'connectionToggle') {
        echo json_encode([
            'success' => true,
            'connectionToggle' => getConnectionToggle()
        ]);
    } elseif ($param === 'speedTextToggle') {
        echo json_encode([
            'success' => true,
            'speedTextToggle' => getSpeedTextToggle()
        ]);
    } else {
        // Return current view type (default behavior for backward compatibility)
        echo json_encode([
            'success' => true,
            'viewType' => getViewType()
        ]);
    }
} elseif ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Check if we're setting view type
    if (isset($input['viewType'])) {
        $viewType = $input['viewType'];
        
        if (setViewType($viewType)) {
            echo json_encode([
                'success' => true,
                'message' => 'View type updated successfully',
                'viewType' => $viewType
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Failed to update view type'
            ]);
        }
    }
    // Check if we're setting connection toggle
    elseif (isset($input['connectionToggle'])) {
        $connectionToggle = $input['connectionToggle'];

        if (setConnectionToggle($connectionToggle)) {
            echo json_encode([
                'success' => true,
                'message' => 'Connection toggle updated successfully',
                'connectionToggle' => $connectionToggle
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Failed to update connection toggle'
            ]);
        }
    }
    // Check if we're setting speed text toggle
    elseif (isset($input['speedTextToggle'])) {
        $speedTextToggle = $input['speedTextToggle'];

        if (setSpeedTextToggle($speedTextToggle)) {
            echo json_encode([
                'success' => true,
                'message' => 'Speed text toggle updated successfully',
                'speedTextToggle' => $speedTextToggle
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Failed to update speed text toggle'
            ]);
        }
    }
    else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Either viewType, connectionToggle, or speedTextToggle parameter is required'
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>