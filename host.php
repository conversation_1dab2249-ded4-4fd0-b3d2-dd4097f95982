<?php
include 'theme_loader.php'; // Include the theme loader
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Host Status</title>
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/host.css">
    <style>
        .os-icon {
            display: none;
        }
        .os-icon.loaded {
            display: inline-block;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <h1 class="header-title">
                <?php
                $nickname = $_GET['nickname'];
                $hostip = $_GET['hostip'];
                $hostTitle = ($nickname === $hostip) ? $nickname : "{$nickname} ({$hostip})";
                echo $hostTitle;
                ?>
            </h1>
            <div class="header-actions">
                <div class="os-dropdown">
                    <img src="imgs/OS-Vendors/unknown-svgrepo-com.svg" class="os-icon" title="Operating System/Vendor" alt="Loading...">
                    <div class="os-menu">
                        <div class="os-item" data-os="windows">
                            <img src="imgs/OS-Vendors/windows-174-svgrepo-com.svg" alt="Windows">
                            Windows
                        </div>
                        <div class="os-item" data-os="linux">
                            <img src="imgs/OS-Vendors/linux-svgrepo-com.svg" alt="Linux">
                            Linux
                        </div>
                        <div class="os-item" data-os="mac">
                            <img src="imgs/OS-Vendors/apple-inc-svgrepo-com.svg" alt="macOS">
                            macOS
                        </div>
                        <div class="os-item" data-os="android">
                            <img src="imgs/OS-Vendors/android-svgrepo-com.svg" alt="Android">
                            Android
                        </div>
                        <div class="os-item" data-os="freebsd">
                            <img src="imgs/OS-Vendors/freebsd-svgrepo-com.svg" alt="FreeBSD">
                            FreeBSD
                        </div>
                        <div class="os-item" data-os="openbsd">
                            <img src="imgs/OS-Vendors/openbsd-svgrepo-com.svg" alt="OpenBSD">
                            OpenBSD
                        </div>
                        <div class="os-item" data-os="cisco">
                            <img src="imgs/OS-Vendors/cisco-svgrepo-com.svg" alt="Cisco">
                            Cisco
                        </div>
                        <div class="os-item" data-os="aruba">
                            <img src="imgs/OS-Vendors/Aruba_Networks_logo.svg" alt="Aruba Networks">
                            Aruba
                        </div>
                        <div class="os-item" data-os="ruckus">
                            <img src="imgs/OS-Vendors/ruckus-logo.png" alt="Ruckus">
                            Ruckus
                        </div>
                        <div class="os-item" data-os="apc">
                            <img src="imgs/OS-Vendors/APC-logo.svg" alt="APC">
                            APC
                        </div>
                        <div class="os-item" data-os="dell">
                            <img src="imgs/OS-Vendors/dell-2-logo-svgrepo-com.svg" alt="Dell">
                            Dell
                        </div>
                        <div class="os-item" data-os="fortinet">
                            <img src="imgs/OS-Vendors/fortinet-svgrepo-com.svg" alt="Fortinet">
                            Fortinet
                        </div>
                        <div class="os-item" data-os="hp">
                            <img src="imgs/OS-Vendors/hp-svgrepo-com.svg" alt="HP">
                            HP
                        </div>
                        <div class="os-item" data-os="vmware">
                            <img src="imgs/OS-Vendors/vmware-svgrepo-com.svg" alt="VMware">
                            VMware
                        </div>
                        <div class="os-item" data-os="canon">
                            <img src="imgs/OS-Vendors/Canon_Logo.svg" alt="Canon">
                            Canon
                        </div>
                        <div class="os-item" data-os="samsung">
                            <img src="imgs/OS-Vendors/samsung-svg.svg" alt="Samsung">
                            Samsung
                        </div>
                        <div class="os-item" data-os="lexmark">
                            <img src="imgs/OS-Vendors/Lexmark-Logo.svg" alt="Lexmark">
                            Lexmark
                        </div>
                        <div class="os-item" data-os="azure">
                            <img src="imgs/OS-Vendors/azure-svgrepo-com.svg" alt="Azure">
                            Azure
                        </div>
                        <div class="os-item" data-os="external">
                            <img src="imgs/OS-Vendors/internet-svgrepo-com.svg" alt="External Network">
                            External
                        </div>
                        <div class="os-item" data-os="unknown">
                            <img src="imgs/OS-Vendors/unknown-svgrepo-com.svg" alt="Unknown">
                            Unknown
                        </div>
                    </div>
                </div>
                
                <!-- Primary Actions -->
                <div class="primary-actions">
                    <i class="fa fa-refresh header-icons" title="Refresh" onclick="window.location.reload()"></i>
                </div>
                
                <!-- Tools Dropdown -->
                <div class="tools-dropdown">
                    <button class="tools-button" title="Tools and Actions">
                        <i class="fa fa-cog"></i>
                    </button>
                    <div class="tools-menu">
                        <div class="tools-item" onclick="launchTroubleshooting('result')">
                            <img src="imgs/icons/radar.svg" alt="Troubleshooting" style="width: 16px; height: 16px;">
                            <span>Protocol Availability Check</span>
                        </div>
                        <div class="tools-item" onclick="openPdfExportModal()">
                            <i class="fa fa-file-pdf-o"></i>
                            <span>Export Availability Report</span>
                        </div>
                        <div class="tools-item" onclick="scanForNewServices()">
                            <i class="fa fa-search"></i>
                            <span>Scan for New Services</span>
                        </div>
                        <div class="tools-item" onclick="stopScanningForServices()">
                            <i class="fa fa-ban"></i>
                            <span>Stop Service Discovery</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <div class="content-wrapper">
        <?php include 'hostApm.php' ?>
    </div>
    <script>
        const isExternal = urlParams.get('subnet') === 'External';
        const osDropdown = document.querySelector('.os-dropdown');
        const osIcon = osDropdown.querySelector('.os-icon');
        const osMenu = osDropdown.querySelector('.os-menu');
        const osItems = osDropdown.querySelectorAll('.os-item');

        // Tools dropdown functionality
        const toolsDropdown = document.querySelector('.tools-dropdown');
        const toolsButton = toolsDropdown.querySelector('.tools-button');
        const toolsMenu = toolsDropdown.querySelector('.tools-menu');
        const toolsItems = toolsDropdown.querySelectorAll('.tools-item');

        // Function to scan for new services
        function scanForNewServices() {
            const hostIp = '<?php echo $_GET['hostip']; ?>';
            const hostname = '<?php echo $_GET['ip']; ?>';
            
            if (confirm('This will scan the host for new services that can be added to monitoring. This may take a few minutes. Continue?')) {
                // Show loading modal
                showScanLoadingModal();
                
                // Make the scan request
                fetch('scanNewServices.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `hostip=${encodeURIComponent(hostIp)}&hostname=${encodeURIComponent(hostname)}`
                })
                .then(response => response.json())
                .then(data => {
                    hideScanLoadingModal();
                    if (data.success) {
                        showScanResultsModal(data);
                    } else {
                        alert('Error scanning for services: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    hideScanLoadingModal();
                    console.error('Error:', error);
                    alert('Error scanning for services: ' + error.message);
                });
            }
        }

        // Function to show scan loading modal
        function showScanLoadingModal() {
            const modal = document.createElement('div');
            modal.id = 'scan-loading-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;
            
            modal.innerHTML = `
                <div style="background: var(--surface); padding: 30px; border-radius: 8px; text-align: center; color: var(--text);">
                    <div style="margin-bottom: 20px;">
                        <i class="fa fa-spinner fa-spin" style="font-size: 32px; color: var(--primary);"></i>
                    </div>
                    <h3 style="margin: 0 0 10px 0;">Scanning for New Services</h3>
                    <p style="margin: 0; color: var(--text-secondary);">This may take a few minutes...</p>
                </div>
            `;
            
            document.body.appendChild(modal);
            document.body.style.overflow = 'hidden';
        }

        // Function to hide scan loading modal
        function hideScanLoadingModal() {
            const modal = document.getElementById('scan-loading-modal');
            if (modal) {
                modal.remove();
                document.body.style.overflow = 'auto';
            }
        }

        // Function to group services by category
        function groupServicesByCategory(services) {
            const categories = {};
            services.forEach((service, index) => {
                const category = service.category || 'Services';
                if (!categories[category]) {
                    categories[category] = [];
                }
                categories[category].push({...service, index});
            });
            
            let html = '';
            Object.keys(categories).forEach(category => {
                html += `
                    <div style="margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: var(--text); font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; border-bottom: 1px solid var(--border); padding-bottom: 5px;">${category}</h4>
                        <div style="background: var(--surface); border: 1px solid var(--border); border-radius: 4px;">
                            ${categories[category].map(service => `
                                <div style="display: flex; align-items: center; padding: 10px; border-bottom: 1px solid var(--border); transition: background-color 0.2s ease;" onmouseover="this.style.backgroundColor='var(--surface-hover)'" onmouseout="this.style.backgroundColor='transparent'">
                                    <span style="color: var(--text); font-size: 14px;">${service.name}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            });
            
            return html;
        }

        // Function to show scan results modal
        function showScanResultsModal(data) {
            const modal = document.createElement('div');
            modal.id = 'scan-results-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                overflow-y: auto;
            `;
            
            let servicesHtml = '';
            if (data.services && data.services.length > 0) {
                servicesHtml = data.services.map(service => `
                    <div class="service-item" style="display: flex; align-items: center; padding: 10px; border-bottom: 1px solid var(--border);">
                        <input type="checkbox" id="service-${service.id}" value="${service.id}" checked style="margin-right: 10px;">
                        <label for="service-${service.id}" style="flex: 1; cursor: pointer;">
                            <strong>${service.name}</strong><br>
                            <small style="color: var(--text-secondary);">${service.description || 'No description'}</small>
                        </label>
                    </div>
                `).join('');
            } else {
                servicesHtml = '<p style="text-align: center; color: var(--text-secondary); padding: 20px;">No new services found to add.</p>';
            }
            
            modal.innerHTML = `
                <div style="background: var(--surface); padding: 20px; border-radius: 8px; color: var(--text); max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0;">Scan Results - New Services Found</h3>
                        <button onclick="closeScanResultsModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: var(--text);">×</button>
                    </div>
                    
                    ${data.services && data.services.length > 0 ? `
                        <div style="margin-bottom: 20px;">
                            <p>Found ${data.services.length} new service(s) that can be added to monitoring. All discovered services will be imported:</p>
                        </div>
                        
                        <div style="margin-bottom: 20px; max-height: 400px; overflow-y: auto;">
                            ${groupServicesByCategory(data.services)}
                        </div>
                        
                        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 10px;">
                            <button onclick="closeScanResultsModal()" style="background: var(--surface); color: var(--text); border: 1px solid var(--border); padding: 8px 16px; border-radius: 4px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.backgroundColor='var(--surface-hover)'" onmouseout="this.style.backgroundColor='var(--surface)'">Cancel</button>
                            <button onclick="importAllServices()" style="background: var(--text); color: var(--surface); border: 1px solid var(--text); padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: 600; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.9'" onmouseout="this.style.opacity='1'">Add Services</button>
                        </div>
                    ` : `
                        ${servicesHtml}
                        <div style="text-align: center; margin-top: 20px;">
                            <button onclick="closeScanResultsModal()" style="background: var(--text); color: var(--surface); border: 1px solid var(--text); padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: 600; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.9'" onmouseout="this.style.opacity='1'">Close</button>
                        </div>
                    `}
                </div>
            `;
            
            document.body.appendChild(modal);
            document.body.style.overflow = 'hidden';
            
            // Store scan data for import
            window.scanData = data;
        }

        // Function to close scan results modal
        function closeScanResultsModal() {
            const modal = document.getElementById('scan-results-modal');
            if (modal) {
                modal.remove();
                document.body.style.overflow = 'auto';
            }
        }

        // Function to import all discovered services
        function importAllServices() {
            // Show loading
            const importButton = document.querySelector('#scan-results-modal button[onclick="importAllServices()"]');
            const originalText = importButton.innerHTML;
            importButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Adding All Services...';
            importButton.disabled = true;
            
            // Make import request - import all services from the scanfile
            fetch('importSelectedServices.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    scanfile: window.scanData.scanfile,
                    hostip: '<?php echo $_GET['hostip']; ?>',
                    importAll: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('All services added successfully! The page will refresh to show the new services.');
                    closeScanResultsModal();
                    window.location.reload();
                } else {
                    alert('Error adding services: ' + (data.error || 'Unknown error'));
                    importButton.innerHTML = originalText;
                    importButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error adding services: ' + error.message);
                importButton.innerHTML = originalText;
                importButton.disabled = false;
            });
        }

        // Function to stop scanning for new services
        function stopScanningForServices() {
            const hostIp = '<?php echo $_GET['hostip']; ?>';
            const infra = '<?php echo isset($_GET['infra']) ? $_GET['infra'] : ''; ?>';
            
            if (confirm('Are you sure you want to exclude this host from service discovery? This will prevent new services from being detected on this host and mute it from alerts. You can re-enable service discovery later in the settings.')) {
                fetch(`service_approval.php?action=do_not_scan&ip=${encodeURIComponent(hostIp)}&infra=${encodeURIComponent(infra)}`)
                    .then(response => response.text())
                    .then(result => {
                        alert(result);
                        // Optionally reload the page to reflect changes
                        // location.reload();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to update scan status.');
                    });
            }
        }

        function updateIcon(os, svg, alt) {
            osIcon.src = svg;
            osIcon.alt = alt;
            osIcon.setAttribute('data-os', os);
            osIcon.classList.add('loaded');
        }

        async function detectOS() {
            try {
                const ip = '<?php echo $_GET['hostip']; ?>';
                const response = await fetch(`detect_os.php?ip=${encodeURIComponent(ip)}${isExternal ? '&external=True' : ''}`);
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const data = await response.json();
                updateIcon(data.os, data.svg, data.alt);
            } catch (error) {
                console.error('Error detecting OS:', error);
                updateIcon('unknown', 'imgs/OS-Vendors/unknown-svgrepo-com.svg', 'Unknown');
            }
        }

        async function updateVendor(ip, vendor) {
            try {
                const formData = new FormData();
                formData.append('ip', ip);
                formData.append('vendor', vendor);

                const response = await fetch('update_vendor.php', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const data = await response.json();
                if (!data.success) {
                    console.error('Failed to update vendor:', data.message);
                }
            } catch (error) {
                console.error('Error updating vendor:', error);
            }
        }

        window.addEventListener('load', () => {
            detectOS();
        });

        osIcon.addEventListener('click', (e) => {
            e.stopPropagation();
            osMenu.classList.toggle('active');
        });

        document.addEventListener('click', (e) => {
            if (!osDropdown.contains(e.target)) {
                osMenu.classList.remove('active');
            }
            if (!toolsDropdown.contains(e.target)) {
                toolsMenu.classList.remove('active');
            }
        });

        // Tools dropdown event listeners
        toolsButton.addEventListener('click', (e) => {
            e.stopPropagation();
            toolsMenu.classList.toggle('active');
        });

        toolsItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                toolsMenu.classList.remove('active');
            });
        });

        osItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                const os = item.getAttribute('data-os');
                let svg, alt;

                switch (os) {
                    case 'windows':
                        svg = 'imgs/OS-Vendors/windows-174-svgrepo-com.svg';
                        alt = 'Windows';
                        break;
                    case 'linux':
                        svg = 'imgs/OS-Vendors/linux-svgrepo-com.svg';
                        alt = 'Linux';
                        break;
                    case 'mac':
                        svg = 'imgs/OS-Vendors/apple-inc-svgrepo-com.svg';
                        alt = 'macOS';
                        break;
                    case 'android':
                        svg = 'imgs/OS-Vendors/android-svgrepo-com.svg';
                        alt = 'Android';
                        break;
                    case 'freebsd':
                        svg = 'imgs/OS-Vendors/freebsd-svgrepo-com.svg';
                        alt = 'FreeBSD';
                        break;
                    case 'openbsd':
                        svg = 'imgs/OS-Vendors/openbsd-svgrepo-com.svg';
                        alt = 'OpenBSD';
                        break;
                    case 'cisco':
                        svg = 'imgs/OS-Vendors/cisco-svgrepo-com.svg';
                        alt = 'Cisco';
                        break;
                    case 'aruba':
                        svg = 'imgs/OS-Vendors/Aruba_Networks_logo.svg';
                        alt = 'Aruba Networks';
                        break;
                    case 'ruckus':
                        svg = 'imgs/OS-Vendors/ruckus-logo.png';
                        alt = 'Ruckus';
                        break;
                    case 'apc':
                        svg = 'imgs/OS-Vendors/APC-logo.svg';
                        alt = 'APC';
                        break;
                    case 'dell':
                        svg = 'imgs/OS-Vendors/dell-2-logo-svgrepo-com.svg';
                        alt = 'Dell';
                        break;
                    case 'fortinet':
                        svg = 'imgs/OS-Vendors/fortinet-svgrepo-com.svg';
                        alt = 'Fortinet';
                        break;
                    case 'hp':
                        svg = 'imgs/OS-Vendors/hp-svgrepo-com.svg';
                        alt = 'HP';
                        break;
                    case 'vmware':
                        svg = 'imgs/OS-Vendors/vmware-svgrepo-com.svg';
                        alt = 'VMware';
                        break;
                    case 'canon':
                        svg = 'imgs/OS-Vendors/Canon_Logo.svg';
                        alt = 'Canon';
                        break;
                    case 'samsung':
                        svg = 'imgs/OS-Vendors/samsung-svg.svg';
                        alt = 'Samsung';
                        break;
                    case 'lexmark':
                        svg = 'imgs/OS-Vendors/Lexmark-Logo.svg';
                        alt = 'Lexmark';
                        break;
                    case 'azure':
                        svg = 'imgs/OS-Vendors/azure-svgrepo-com.svg';
                        alt = 'Azure';
                        break;
                    case 'external':
                        svg = 'imgs/OS-Vendors/internet-svgrepo-com.svg';
                        alt = 'External Network';
                        break;
                    case 'unknown':
                    default:
                        svg = 'imgs/OS-Vendors/unknown-svgrepo-com.svg';
                        alt = 'Unknown';
                        break;
                }

                updateIcon(os, svg, alt);
                const ip = '<?php echo $_GET['hostip']; ?>';
                updateVendor(ip, os);
                osMenu.classList.remove('active');
            });
        });
    </script>
</body>
</html>