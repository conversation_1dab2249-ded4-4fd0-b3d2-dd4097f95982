<?php
include 'language_selector.php';
include 'theme_loader.php'; // Include the theme loader
include 'components/statusIndicators.php';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blesk</title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/hostlist.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
            <style>
        /* Search bar always visible - responsive canvas positioning */
        #canvas-container {
            top: 103px;
        }
        
        @media (max-width: 768px) {
            #canvas-container {
                top: 95px;
            }
        }
        </style>
    <script src="functions/d3.v7.min.js"></script>
    <script src="functions/helperFunctions.js"></script>
    <script src="functions/fetchHostGroups.js"></script>
    <script src="functions/fetchSubnetsBubbles.js"></script>
    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <script src="functions/background_beacon.js"></script>
    <script src="functions/host_approval.js"></script>
    <script src="functions/hostsPhpFunctions/hostCount.js"></script>
    <script src="functions/hostlistPhpFunctions/hostListView.js"></script>
    <script src="functions/shared/featureStatusIndicators.js"></script>
    <script src="functions/subnetsPhpFunctions/apmProgress.js"></script>
    <script src="functions/scanModal.js"></script>
    <script src="functions/subnetsPhpFunctions/modalHandlers.js"></script>
    <script src="functions/subnetsPhpFunctions/contextMenu.js"></script>
    <script src="functions/subnetsPhpFunctions/headerHandlers.js"></script>
    <script src="functions/subnetsPhpFunctions/filtering.js"></script>
    <script src="functions/subnetsPhpFunctions/toggleConnections.js"></script>
    <script src="functions/setMainView.js"></script>
    <script src="functions/globalAiAssistant.js"></script>
</head>

<body>
    <header>
        <div style="display: flex; align-items: center; gap: 15px;">
            <a href="../../index.php">
                <img style="width: 25px;" src="../../images/blesk-favicon.svg" alt="Home">
            </a>
            <div class="breadcrumbs">
                <a href="infra.php<?php echo isset($_GET['subnet']) ? '?subnet=true' : ''; ?>"><i class="fa fa-home"></i> Home</a>
                <span class="separator">/</span>
                <span class="current">
                    <i class="fa fa-sitemap"></i> 
                    <?php echo htmlspecialchars($_GET['infra'], ENT_QUOTES, 'UTF-8'); ?>
                </span>
            </div>
            
            <!-- Simple APM Progress Indicator -->
            <div id="apm-progress-simple" class="apm-progress-simple" title="Updating ALL hosts status..." style="display: none;">
                <i class="fa fa-refresh fa-spin"></i>
                <span id="apm-progress-count">0/0</span>
            </div>
            
            <!-- Feature Status Indicators -->
            <?php renderStatusIndicators(); ?>
            
            <!-- Status Count Wrapper for toggle connections button -->
            <div id="status-count-wrapper" class="status-count-wrapper"></div>
        </div>
        
        <div class="header-content">
            <div class="hamburger">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </div>
            <div class="header-buttons">
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="viewDropdownBtn"><i class="fa fa-eye"></i> View <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="viewDropdownContent">
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'list', 'hostlist.php')"><i class="fa fa-list"></i> Asset Overview</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'table', 'tableview.php')"><i class="fa fa-table"></i> Data Grid</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'dashboards', 'networkmap.php')"><i class="fa fa-map"></i> Dashboards and Visualization</a>
                    </div>
                </div>
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="menuDropdownBtn"><i class="fa fa-bars"></i> Control Panel <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="menuDropdownContent">
                        <a class="header-button" id="formModal-button"><i class="fa fa-search"></i> Scan</a>
                        <a class="header-button" onclick='showModal("https://<?php echo $_SERVER["HTTP_HOST"]; ?>/bubblemaps/credentials.php")'><i class="fa fa-key"></i> Credentials</a>
                        <a class="header-button" href="notifications.php"><i class="fa fa-bell"></i> Notifications</a>
                        <a class="header-button" href="reports.php"><i class="fa fa-file-text"></i> Reports</a>
                    </div>
                </div>
                <a class="header-button" id="openSettingsBtn"><i class="fa fa-cog"></i> Advanced Settings</a>
            </div>
        </div>
    </header>
    
    <!-- Search Container -->
    <div class="hostlist-search-container show">
        <div class="search-input-wrapper">
            <label for="searchBar">Search:</label>
            <input type="text" id="searchBar" placeholder="Filter by subnet..." oninput="filterSubnets()">
            <span class="search-actions">
                <button class="hostlist-clear-search">&times;</button>
            </span>
        </div>
        <div class="view-toggle-selector">
            <label for="viewToggle">View:</label>
            <select id="viewToggle" onchange="handleViewToggle()">
                <option value="all-hosts">All Hosts</option>
                <option value="subnets" selected>Subnets View</option>
            </select>
        </div>
        <div class="layout-controls">
            <label class="layout-label">Layout:</label>
            <span class="connections-toggle" id="toggle-connections-btn" title="Toggle Connections"><i class="fa fa-link"></i> Connections</span>
        </div>
    </div>
    
    <!-- Mobile Status Container (hidden by default, shown via JS on mobile) -->
    <div class="mobile-status-container" id="mobile-status-container">
        <!-- APM Progress Indicator for mobile -->
        <div id="apm-progress-mobile" class="apm-progress-mobile" title="Updating ALL hosts status..." style="display: none;">
            <i class="fa fa-refresh fa-spin"></i>
            <span id="apm-progress-count-mobile">0/0</span>
        </div>
        
        <!-- Feature Status Indicators for mobile -->
        <?php renderStatusIndicators(true); ?>
    </div>
    
    <!-- Feature Issue Popover -->
    <div id="feature-issue-popover" class="status-hosts-popover" style="display: none;">
        <h4 id="popover-title">Feature Issues</h4>
        <ul id="popover-list">
            <!-- Populated by JavaScript -->
        </ul>
    </div>
    
    <div id="context-menu" class="context-menu">
        <div class="context-menu-title"></div>
        <ul>
            <li id="rename">Rename</li>
            <li id="view">View</li>
        </ul>
    </div>
    <?php include "settingsModal.php"; ?>
    <div class="loader-container" id="loading-overlay">
        <div class="spinner">
            <div class="spinner-dot"></div>
            <div class="spinner-particle"></div>
            <div class="spinner-particle"></div>
            <div class="spinner-particle"></div>
            <div class="spinner-particle"></div>
        </div>
        <div class="loading-text">Scanning for Subnets...</div>
    </div>
    <!-- Scan Modal will be created by scanModal.js -->
    <!-- iframe Modal -->
    <div id="infoModal" class="iframeModal">
        <div class="iframeModal-content" id="iframeModal-content">
            <span class="iframeMclose" title="Back" aria-label="Back">←</span>
            <div class="iframe-loader"></div>
            <iframe id="modal-frame"></iframe>
        </div>
    </div>
    <div id="canvas-container">
        <svg id="map"></svg>
    </div>
    <script>
        // Global constants and variables
        const urlParams = new URLSearchParams(window.location.search);
        const dictionaries = <?php echo json_encode($dictionaries); ?>;
        const selectedLang = '<?php echo $selectedLang; ?>';
        let translator;


        
        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all modules
            // initScanModal(); // Now handled by scanModal.js
            initIframeModal();
            initContextMenu();
            initHeader();
            initWindowEvents();
            addMobileStyles();
            
            // Initialize translator
            translator = new Translator(dictionaries, selectedLang);
            translator.init();
            initTranslationObserver(translator);
            
            // Initialize feature status indicators
            initFeatureStatusIndicators();
            
            // Start APM progress monitoring
            startApmProgressMonitoring();
            
            // Initialize host approval notifications
            if (typeof initHostApproval === 'function') {
                initHostApproval(urlParams.get('infra'));
            }
            
            // Load infrastructure names and fetch data
            getInfrasNames();

            const dropdownBtn = document.getElementById('menuDropdownBtn');
            const dropdownContent = document.getElementById('menuDropdownContent');
            if(dropdownBtn && dropdownContent){
                dropdownBtn.addEventListener('click', function(e){
                    e.preventDefault();
                    e.stopPropagation();
                    // Close other dropdowns first
                    const viewDropdownContent = document.getElementById('viewDropdownContent');
                    if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                    dropdownContent.classList.toggle('show');
                });
            }
            
            // Handle View dropdown
            const viewDropdownBtn = document.getElementById('viewDropdownBtn');
            const viewDropdownContent = document.getElementById('viewDropdownContent');
            if(viewDropdownBtn && viewDropdownContent){
                viewDropdownBtn.addEventListener('click', function(e){
                    e.preventDefault();
                    e.stopPropagation();
                    // Close other dropdowns first
                    if(dropdownContent) dropdownContent.classList.remove('show');
                    viewDropdownContent.classList.toggle('show');
                });
            }
            
            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e){
                if(!e.target.closest('.header-dropdown')){
                    if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                    if(dropdownContent) dropdownContent.classList.remove('show');
                }
            });
        });

        // Load and display subnet bubbles
        fetchSubnetsBubbles();
        document.querySelector('.loader-container').style.display = 'none';
        
        // Clear search functionality only
        document.addEventListener('DOMContentLoaded', function() {
            const clearSearchBtn = document.querySelector('.hostlist-search-container .hostlist-clear-search');
            const searchInput = document.getElementById('searchBar');
            
            // Clear search functionality
            if (clearSearchBtn && searchInput) {
                clearSearchBtn.addEventListener('click', () => {
                    // Clear search and reset all filters (feature status indicators)
                    searchInput.value = '';
                    if (typeof resetStatusFilters === 'function') {
                        resetStatusFilters(true); // Preserve search mode (though subnets don't have search modes)
                    }
                    filterSubnets();
                });
            }
        });

        // Handle view toggle dropdown
        function handleViewToggle() {
            const viewToggle = document.getElementById('viewToggle');
            const selectedValue = viewToggle.value;
            const infraParam = urlParams.get('infra');
            
            if (selectedValue === 'all-hosts') {
                window.location.href = `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${encodeURIComponent(infraParam || '')}`;
            } else if (selectedValue === 'subnets') {
                window.location.href = `subnets.php?infra=${encodeURIComponent(infraParam || '')}&subnet=true`;
            }
        }
    </script>
</body>

</html>