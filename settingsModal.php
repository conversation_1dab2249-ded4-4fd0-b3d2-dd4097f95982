<?php
    // Include the theme loader 
    include 'theme_loader.php';
    ?>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/settings.css">
    <script src="functions/jquery-3.6.0.min.js"></script>
    <!-- The Modal -->
    <div id="settingsModal" class="settings-modal">
        <!-- Modal content -->
        <div class="settings-modal-content">
            <div class="settings-modal-header">
                <span class="settings-modal-title">Settings</span>
                <span id="closeModalBtn" class="settings-modal-close"><i class="fa fa-times"></i></span>
            </div>

            <!-- Top Tabs -->
            <div class="modal-top-tabs">
                <a class="top-tab-link active" data-target="general">General</a>
                <a class="top-tab-link" data-target="detection">Auto Detection</a> <!-- NEW -->
                <a class="top-tab-link" data-target="apm">APM Settings</a>
            </div>

            <div class="modal-body-container">
                <!-- Left Navigation (Sub-Tabs) -->
                <nav class="modal-sub-tabs-nav">
                    <!-- General Sub-Tabs (Grouped) -->
                    <ul class="sub-tabs-container active" id="sub-tabs-general">
                        <!-- Accordion Heading -->
                        <li class="sub-tab-heading">
                            <i class="fa fa-chevron-down accordion-icon"></i> System
                        </li>
                        <!-- Accordion Items -->
                        <li class="sub-tab-item"><span class="sub-tab-link active" data-target="general-system-information"><i class="fa fa-info-circle"></i> Information</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-system-logs"><i class="fa fa-file-text-o"></i> Logs</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-system-console"><i class="fa fa-terminal"></i> Console</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-system-services"><i class="fa fa-cogs"></i> Services</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-system-updates"><i class="fa fa-arrow-circle-o-up"></i> Updates</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-system-reboot"><i class="fa fa-power-off"></i> Reboot</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-system-language-theme"><i class="fa fa-language"></i> Language & Theme</span></li>

                        <!-- Accordion Heading -->
                        <li class="sub-tab-heading">
                            <i class="fa fa-chevron-down accordion-icon"></i> Configuration
                        </li>
                        <!-- Accordion Items -->
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-config-ip"><i class="fa fa-sitemap"></i> IP</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-config-proxy"><i class="fa fa-globe"></i> Proxy</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-config-mail"><i class="fa fa-envelope-o"></i> Mail</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-config-backup"><i class="fa fa-database"></i> Backup</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-config-auth"><i class="fa fa-shield"></i> Authentication</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-config-hostname"><i class="fa fa-laptop"></i> Hostname</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="general-config-time"><i class="fa fa-clock-o"></i> Time</span></li>
                    </ul>

                    <!-- Auto Detection Sub-Tabs (NEW) -->
                    <ul class="sub-tabs-container" id="sub-tabs-detection">
                        <!-- No Headings in this section, just items -->
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="detection-agent-type"><i class="fa fa-laptop"></i> Agent Type</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="detection-network"><i class="fa fa-exchange"></i> Network Devices</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="detection-illegal-chars"><i class="fa fa-ban"></i> Illegal Characters</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="detection-hostnames"><i class="fa fa-tag"></i> Hostnames</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="detection-service-discovery"><i class="fa fa-cogs"></i> Service Discovery</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="detection-host-discovery"><i class="fa fa-sitemap"></i> Host Discovery</span></li>
                    </ul>

                    <!-- APM Settings Sub-Tabs -->
                    <ul class="sub-tabs-container" id="sub-tabs-apm">
                        <!-- Accordion Heading -->
                        <li class="sub-tab-heading">
                            <i class="fa fa-chevron-down accordion-icon"></i> Objects
                        </li>
                        <!-- Accordion Items -->
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-hosts"><i class="fa fa-server"></i> Hosts</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-services"><i class="fa fa-cog"></i> Services</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-host-groups"><i class="fa fa-object-group"></i> Host Groups</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-service-groups"><i class="fa fa-object-ungroup"></i> Service Groups</span></li>

                        <!-- Accordion Heading -->
                        <li class="sub-tab-heading">
                            <i class="fa fa-chevron-down accordion-icon"></i> Notifications
                        </li>
                        <!-- Accordion Items -->
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-contacts"><i class="fa fa-user"></i> Contacts</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-contact-groups"><i class="fa fa-users"></i> Contact Groups</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-time"><i class="fa fa-clock-o"></i> Time Periods</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-commands"><i class="fa fa-code"></i> Commands</span></li>

                        <!-- Accordion Heading -->
                        <li class="sub-tab-heading">
                            <i class="fa fa-chevron-down accordion-icon"></i> Advanced
                        </li>
                        <!-- Accordion Item -->
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-control"><i class="fa fa-check-circle"></i> Control</span></li>
                        <li class="sub-tab-item"><span class="sub-tab-link" data-target="apm-other"><i class="fa fa-sliders"></i> Other Settings</span></li>
                    </ul>
                </nav>

                <!-- Right Content Area -->
                <div class="modal-content-area">
                    <!-- PHP to read initial mail settings -->
                    <?php
                        // Include the helper file
                        require_once 'src/mailConfig/mailConfigHelpers.php';

                        // Use the function from the helper file
                        $receiverEmailCurrent = readMailFileContent('/var/lib/blesk/emailaddr');
                        $senderEmailCurrent = readMailFileContent('/var/lib/blesk/emailsender');
                        $smtpHostCurrent = readMailFileContent('/var/lib/blesk/smtphost');
                    ?>
                    <!-- General Content Panes -->
                    <div id="general-system-information" class="tab-content-pane active">
                        <?php
                        include "src/settingsphp/systemInfo.php";
                        ?>
                    </div>
                    <div id="general-system-logs" class="tab-content-pane">
                        <div id="logs-loading" style="text-align: center; padding: 20px;">
                            <i class="fa fa-spinner fa-spin"></i> Loading logs...
                        </div>
                    </div>
                    <div id="general-system-console" class="tab-content-pane">
                        <h2><i class="fa fa-terminal"></i> System Console</h2>
                        <p>Provides access to a command-line interface for advanced diagnostics and management. Use with caution.</p>
                        <div id="console-container" style="text-align:left; font-weight:300; font-family:'Raleway',sans-serif; letter-spacing:1px; font-size:13px;">
                            <!-- Iframe will be dynamically inserted here -->
                        </div>
                    </div>
                    <div id="general-system-services" class="tab-content-pane">
                        <div id="services-loading" style="text-align: center; padding: 20px;">
                            <i class="fa fa-spinner fa-spin"></i> Loading services...
                        </div>
                    </div>
                    <div id="general-system-updates" class="tab-content-pane">
                        <h2><i class="fa fa-arrow-circle-o-up"></i> System Updates</h2>
                        <p>Check for and apply system software updates. Keep your system secure and up-to-date.</p>
                        
                        <fieldset style="margin-bottom: 20px;">
                            <legend>Automatic Updates</legend>
                            <label class="radio-label">
                                <input type="radio" name="autoUpdates" value="on" id="auto-updates-on"> On
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="autoUpdates" value="off" id="auto-updates-off" checked> Off
                            </label>
                        </fieldset>

                        <button id="run-dnf-update-btn" class="btn-restart">
                            <i class="fa fa-arrow-circle-o-up"></i>&nbsp;Update Now
                        </button>
                        <div id="update-status">
                            <!-- Status messages will appear here -->
                        </div>
                    </div>
                    <div id="general-system-reboot" class="tab-content-pane">
                        <h2><i class="fa fa-power-off"></i> Reboot System</h2>
                        <p>Reboot the system. Ensure all work is saved before proceeding.</p>

                        <form method="post" action="src/settingsphp/rebootSystem.php" onsubmit="return confirm('Are you sure you want to reboot the system?');">
                            <button type="submit" class="btn-stop" style="margin-top: 20px;">
                                <i class="fa fa-power-off"></i><span>&nbsp;Reboot Now</span>
                            </button>
                        </form>
                    </div>
                    <div id="general-system-language-theme" class="tab-content-pane">
                        <h2><i class="fa fa-language"></i> Language & Theme</h2>
                        <p>Configure the display language and interface theme.</p>
                        
                        <fieldset style="margin-bottom: 20px;">
                            <legend>Language</legend>
                            <?php echo renderLanguageSelector(); ?>
                        </fieldset>

                        <fieldset>
                            <legend>Theme</legend>
                            <?php
                                $themeJsonPath = 'styles/chosen-theme.json';
                                $currentTheme = 'dark-theme'; // Fallback
                                $availableThemes = ['dark-theme']; // Fallback
                                if (file_exists($themeJsonPath)) {
                                    $themeJsonContent = @file_get_contents($themeJsonPath);
                                    if ($themeJsonContent !== false) {
                                        $themeData = json_decode($themeJsonContent, true);
                                        if (json_last_error() === JSON_ERROR_NONE) {
                                            if (isset($themeData['chosen_theme']) && is_string($themeData['chosen_theme'])) {
                                                $currentTheme = $themeData['chosen_theme'];
                                            }
                                            if (isset($themeData['available_themes']) && is_array($themeData['available_themes'])) {
                                                // Basic validation for available themes
                                                $availableThemes = array_filter($themeData['available_themes'], function($theme) {
                                                    return is_string($theme) && preg_match('/^[a-zA-Z0-9_-]+$/', $theme);
                                                });
                                                if (empty($availableThemes)) { // Ensure there's at least the default if filtering removed everything
                                                    $availableThemes = ['dark-theme'];
                                                }
                                            }
                                        } else {
                                             error_log("SettingsModal: Error decoding JSON in $themeJsonPath: " . json_last_error_msg());
                                        }
                                    } else {
                                         error_log("SettingsModal: Could not read theme file: $themeJsonPath");
                                    }
                                } else {
                                     error_log("SettingsModal: Theme configuration file not found: $themeJsonPath");
                                }
                            ?>
                            <label for="theme-selector">Select Theme:</label>
                            <select id="theme-selector" name="theme-selector" style="min-width: 15em; padding: 5px;">
                                <?php foreach ($availableThemes as $theme): ?>
                                    <option value="<?php echo htmlspecialchars($theme); ?>" <?php echo ($theme === $currentTheme) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars(ucwords(str_replace('-', ' ', $theme))); // Make theme name more readable ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div id="theme-update-status" class="message" style="margin-top: 10px; display: none;"></div>
                        </fieldset>
                    </div>
                    <div id="general-config-ip" class="tab-content-pane">
                        <h2><i class="fa fa-sitemap"></i> IP Configuration</h2>
                        <?php include "src/ipConfig/ipConfig.php"; ?>
                    </div>
                    <div id="general-config-proxy" class="tab-content-pane">
                        <!-- Proxy Settings Content Loaded Here -->
                        <?php include "src/proxyConfig/proxySettings.php"; ?>
                    </div>
                    <div id="general-config-mail" class="tab-content-pane">
                        <h2><i class="fa fa-envelope-o"></i> Mail Server Configuration</h2>
                        <p>Configure settings for sending email notifications. These settings are used by various system components.</p>

                        <form id="mail-config-form">
                            <div class="input-group" style="display: flex; flex-direction: column;">
                                <label for="receiverEmail" style="margin-bottom: 5px;"><i class="fa fa-inbox"></i> Notification Recipient Email:</label>
                                <input type="email" id="receiverEmail" name="receiverEmail" value="<?php echo htmlspecialchars($receiverEmailCurrent); ?>" required>
                                <small>The email address where system notifications will be sent.</small>
                            </div>

                            <div class="input-group" style="display: flex; flex-direction: column;">
                                <label for="senderEmail" style="margin-bottom: 5px;"><i class="fa fa-paper-plane-o"></i> Sender Email Address:</label>
                                <input type="email" id="senderEmail" name="senderEmail" value="<?php echo htmlspecialchars($senderEmailCurrent); ?>" required>
                                <small>The "From" address used in notification emails.</small>
                            </div>

                            <div class="input-group" style="display: flex; flex-direction: column;">
                                <label for="smtpHost" style="margin-bottom: 5px;"><i class="fa fa-server"></i> SMTP Server Host:</label>
                                <input type="text" id="smtpHost" name="smtpHost" value="<?php echo htmlspecialchars($smtpHostCurrent); ?>" required>
                                <small>The hostname or IP address of your outgoing mail server (e.g., smtp.example.com).</small>
                                <!-- Add fields for port, authentication, username, password if needed -->
                            </div>

                            <button type="button" id="test-mail-btn" class="btn-restart" style="margin-top: 15px;">
                                <i class="fa fa-paper-plane"></i>&nbsp;Send Test Email
                            </button>
                        </form>
                        <div id="mail-config-status" class="message" style="margin-top: 15px; display: none;">
                            <!-- Status messages will appear here -->
                        </div>
                        <div id="mail-test-log-output" style="margin-top: 15px; display: none;">
                            <h4><i class="fa fa-file-text-o"></i> Test Email Logs:</h4>
                            <pre style="background: var(--background); border: 1px solid var(--border); border-radius: 8px; padding: 15px; max-height: 600px; overflow-y: auto; font-family: monospace; font-size: 13px; color: var(--text-secondary); white-space: pre-wrap; word-break: break-word;"></pre>
                        </div>
                    </div>
                    <div id="general-config-backup" class="tab-content-pane">
                        <h2><i class="fa fa-database"></i> Backup & Restore</h2>
                        <p>Manage system configuration backups. Create, schedule, download, and restore backups.</p>
                        
                        <div class="auth-tabs">
                            <a href="#" class="backup-tab-link active" data-target="backup-settings-tab">
                                <i class="fa fa-cog"></i> Backup Settings
                            </a>
                            <a href="#" class="backup-tab-link" data-target="backup-files-tab">
                                <i class="fa fa-file-archive-o"></i> Available Backup Files
                            </a>
                        </div>
                        
                        <!-- Backup Settings Tab -->
                        <div id="backup-settings-tab" class="backup-tab-content">
                            <form id="backup-config-form">
                                <fieldset style="margin-bottom: 20px;">
                                    <legend>Backup Storage</legend>
                                    <label class="radio-label">
                                        <input type="radio" name="backup_type" value="local" checked> Store backups locally
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="backup_type" value="scp"> Store backups on remote server (SCP)
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="backup_type" value="ftp"> Legacy method (FTP)
                                        <small style="display: block; margin-left: 25px; color: #f97316;">Warning: FTP is not secure as it sends credentials in plain text</small>
                                    </label>
                                </fieldset>

                                <div id="scp-settings-container" style="display: none; border: 1px solid var(--border); padding: 15px; margin-bottom: 20px; border-radius: var(--radius);">
                                    <h4 style="margin-top: 0; margin-bottom: 15px; color: var(--text-primary);"><i class="fa fa-server"></i> Remote Server Details (SCP)</h4>
                                    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 10px;">
                                        <label for="scp_server" style="margin-bottom: 5px;">Server Address:</label>
                                        <input type="text" id="scp_server" name="scp_server" placeholder="e.g., yourserver.com or IP">
                                    </div>
                                    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 10px;">
                                        <label for="scp_user" style="margin-bottom: 5px;">Username:</label>
                                        <input type="text" id="scp_user" name="scp_user" placeholder="e.g., backupuser">
                                    </div>
                                    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 10px;">
                                        <label for="scp_password" style="margin-bottom: 5px;">Password:</label>
                                        <div style="position: relative;">
                                            <input type="password" id="scp_password" name="scp_password">
                                            <span id="toggle-scp-password" class="fa fa-eye toggle-password"></span>
                                        </div>
                                        <small>Note: For SSH key authentication, leave the password field empty. For password authentication, simply enter your password.</small>
                                    </div>
                                    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 15px;">
                                        <label for="scp_folder" style="margin-bottom: 5px;">Remote Folder Path:</label>
                                        <input type="text" id="scp_folder" name="scp_folder" placeholder="e.g., /backup/blesk/">
                                        <small>Ensure this folder exists on the remote server and the user has write permissions.</small>
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="scp_delete_local" id="scp_delete_local" value="yes"> Delete local backup after successful remote transfer
                                        </label>
                                    </div>
                                    <button type="button" id="test-scp-btn" class="btn-restart">
                                        <i class="fa fa-plug"></i>&nbsp;Test SCP Connection
                                    </button>
                                </div>
                                
                                <div id="ftp-settings-container" style="display: none; border: 1px solid var(--border); padding: 15px; margin-bottom: 20px; border-radius: var(--radius);">
                                    <h4 style="margin-top: 0; margin-bottom: 15px; color: var(--text-primary);"><i class="fa fa-server"></i> Remote Server Details (FTP)</h4>
                                    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 10px;">
                                        <label for="ftp_server" style="margin-bottom: 5px;">FTP Server Address:</label>
                                        <input type="text" id="ftp_server" name="ftp_server" placeholder="e.g., ftp.example.com or IP">
                                    </div>
                                    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 10px;">
                                        <label for="ftp_user" style="margin-bottom: 5px;">FTP Username:</label>
                                        <input type="text" id="ftp_user" name="ftp_user" placeholder="e.g., ftpuser">
                                    </div>
                                    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 10px;">
                                        <label for="ftp_password" style="margin-bottom: 5px;">FTP Password:</label>
                                        <div style="position: relative;">
                                            <input type="password" id="ftp_password" name="ftp_password">
                                            <span id="toggle-ftp-password" class="fa fa-eye toggle-password"></span>
                                        </div>
                                    </div>
                                    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 15px;">
                                        <label for="ftp_folder" style="margin-bottom: 5px;">Remote Folder Path:</label>
                                        <input type="text" id="ftp_folder" name="ftp_folder" placeholder="e.g., ~">
                                        <small>FTP folder path (use ~ for home directory)</small>
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="ftp_delete_local" id="ftp_delete_local" value="yes"> Delete local backup after successful remote transfer
                                        </label>
                                    </div>
                                    <button type="button" id="test-ftp-btn" class="btn-restart">
                                        <i class="fa fa-plug"></i>&nbsp;Test FTP Connection
                                    </button>
                                </div>

                                <fieldset>
                                    <legend>Modules to Backup</legend>                                    
                                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;">
                                        <label class="checkbox-label" title="Application Performance Monitor">
                                            <input type="checkbox" name="backup_module" value="apm"> APM
                                        </label>
                                        <label class="checkbox-label" title="Network Performance Monitor">
                                            <input type="checkbox" name="backup_module" value="npm"> NPM
                                        </label>
                                        <label class="checkbox-label" title="Network Traffic Analyzer">
                                            <input type="checkbox" name="backup_module" value="nta"> NTA
                                        </label>
                                        <label class="checkbox-label" title="Switch Port Manager">
                                            <input type="checkbox" name="backup_module" value="spm"> SPM
                                        </label>
                                        <label class="checkbox-label" title="Event Log Monitor">
                                            <input type="checkbox" name="backup_module" value="elm"> ELM
                                        </label>
                                        <label class="checkbox-label" title="Network Configuration Manager">
                                            <input type="checkbox" name="backup_module" value="ncm"> NCM
                                        </label>
                                        <label class="checkbox-label" title="Network Security Monitor">
                                            <input type="checkbox" name="backup_module" value="nsm"> NSM
                                        </label>
                                        <label class="checkbox-label" title="Asset Life Manager">
                                            <input type="checkbox" name="backup_module" value="alm"> ALM
                                        </label>
                                    </div>
                                </fieldset>
                                
                                <div style="margin-top: 20px;">
                                    <button type="button" id="start-backup-btn" class="btn-restart">
                                        <i class="fa fa-play-circle"></i>&nbsp;Start Backup Now
                                    </button>
                                </div>

                                <!-- Automatic Backup Schedule -->
                                <fieldset style="margin-top: 20px;">
                                    <legend>Automatic Backup Schedule</legend>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="auto_schedule" name="auto_schedule" value="yes"> Enable automatic backup
                                    </label>
                                    <div id="auto-schedule-options" style="margin-top: 10px; display:flex; gap:15px; align-items:center;">
                                        <div>
                                            <label for="auto_frequency" style="margin-right:5px;">Frequency:</label>
                                            <select id="auto_frequency" name="auto_frequency">
                                                <option value="daily">Daily</option>
                                                <option value="weekly">Weekly</option>
                                                <option value="monthly">Monthly</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label for="auto_time" style="margin-right:5px;">Time:</label>
                                            <input type="time" id="auto_time" name="auto_time" value="02:00" style="min-width:120px;">
                                        </div>
                                    </div>
                                    <small style="display:block;margin-top:5px;">When enabled, the system will perform a backup based on the schedule and the settings configured above.</small>
                                </fieldset>
                            </form>
                            
                            <div id="backup-status" class="message" style="margin-top: 15px; display: none;">
                                <!-- Status messages will appear here -->
                            </div>
                        </div>
                        
                        <!-- Backup Files Tab -->
                        <div id="backup-files-tab" class="backup-tab-content" style="display: none;">
                            <div class="action-header" style="display: flex; justify-content: flex-end; margin-bottom: 15px;">
                                <button type="button" id="refresh-backups-btn" class="btn-restart">
                                    <i class="fa fa-refresh"></i>&nbsp;Refresh
                                </button>
                            </div>
                            
                            <div id="backup-files-container">
                                <div id="backup-files-loading" style="text-align: center; padding: 20px;">
                                    <i class="fa fa-spinner fa-spin"></i> Loading backup files...
                                </div>
                                
                                <div id="backup-files-table-container" style="display: none; border: 1px solid var(--border); border-radius: var(--radius); overflow: hidden;">
                                    <table class="settings-table" style="width: 100%; border-collapse: collapse; margin: 0;">
                                        <thead>
                                            <tr>
                                                <th style="background-color: var(--surface); padding: 12px 15px; text-align: left; border-bottom: 1px solid var(--border); font-weight: 600;">Filename</th>
                                                <th style="background-color: var(--surface); padding: 12px 15px; text-align: left; border-bottom: 1px solid var(--border); font-weight: 600;">Date</th>
                                                <th style="background-color: var(--surface); padding: 12px 15px; text-align: left; border-bottom: 1px solid var(--border); font-weight: 600;">Size</th>
                                                <th style="background-color: var(--surface); padding: 12px 15px; text-align: center; border-bottom: 1px solid var(--border); font-weight: 600; width: 120px;">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="backup-files-table-body">
                                            <!-- Backup files will be listed here -->
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div id="no-backup-files" style="text-align: center; padding: 40px 20px; display: none; background-color: var(--background); border: 1px solid var(--border); border-radius: var(--radius); margin-top: 15px;">
                                    <i class="fa fa-info-circle" style="font-size: 24px; margin-bottom: 10px; color: var(--text-secondary);"></i>
                                    <p style="margin: 0; color: var(--text-secondary);">No backup files found.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="general-config-auth" class="tab-content-pane">
                        <h2><i class="fa fa-shield"></i> Authentication</h2>
                        <p>Configure authentication methods and manage user module access.</p>
                        
                        <?php
                        // Include the helper file
                        require_once 'src/authConfig/authConfigHelpers.php';

                        // Use the function from the helper file
                        $currentAuthType = getCurrentAuthType();
                        ?>
                        
                        <!-- Tabs for Authentication sub-sections -->
                        <div class="auth-tabs">
                            <a href="#" class="auth-tab-link active" data-target="auth-settings-tab">
                                <i class="fa fa-cog"></i> Auth Method
                            </a>
                            <a href="#" class="auth-tab-link" data-target="users-tab">
                                <i class="fa fa-users"></i> Local Users
                            </a>
                            <a href="#" class="auth-tab-link" data-target="ldap-tab">
                                <i class="fa fa-globe"></i> LDAP
                            </a>
                        </div>
                        
                        <!-- Tab content containers -->
                        <div id="auth-settings-tab" class="auth-tab-content">
                            <form id="auth-config-form">
                                <fieldset style="margin-bottom: 20px;">
                                    <legend>Authentication Method</legend>
                                    <label class="radio-label">
                                        <input type="radio" name="auth_type" value="local" <?php echo ($currentAuthType === 'local' ? 'checked' : ''); ?>> Local user authentication only
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="auth_type" value="ldap" <?php echo ($currentAuthType === 'ldap' ? 'checked' : ''); ?>> Use both local user and LDAP
                                        <small style="display: block; margin-left: 20px;">Note: Local usernames must be different from LDAP usernames</small>
                                    </label>
                                </fieldset>

                                <button type="button" id="save-auth-btn" class="btn-restart">
                                    <i class="fa fa-save"></i>&nbsp;Save Authentication Settings
                                </button>
                            </form>
                            <div id="auth-config-status" class="message" style="margin-top: 15px; display: none;">
                                <!-- Status messages will appear here -->
                            </div>
                        </div>
                        
                        <div id="users-tab" class="auth-tab-content" style="display: none;">
                            <div class="action-header" style="display: flex; justify-content: flex-end; margin-bottom: 15px;">
                                <button type="button" id="add-user-btn" class="btn-restart">
                                    <i class="fa fa-user-plus"></i>&nbsp;Add User
                                </button>
                            </div>
                            
                            <div id="user-modules-container">
                                <div id="users-loading" style="text-align: center; padding: 20px;">
                                    <i class="fa fa-spinner fa-spin"></i> Loading user data...
                                </div>
                                <div id="user-modules-table-container" style="display: none;"></div>
                            </div>
                            
                            <!-- User edit form (initially hidden) -->
                            <div id="user-edit-form-container" style="display: none; margin-top: 20px;">
                                <h3><i class="fa fa-user-edit"></i> Edit User</h3>
                                <form id="user-edit-form">
                                    <input type="hidden" id="edit-user-id" name="user_id">
                                    
                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="edit-username" style="margin-bottom: 5px;"><i class="fa fa-user"></i> Username:</label>
                                        <input type="text" id="edit-username" name="username" readonly>
                                    </div>
                                    
                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="edit-password" style="margin-bottom: 5px;"><i class="fa fa-key"></i> Password:</label>
                                        <div style="position: relative;">
                                            <input type="password" id="edit-password" name="password" required>
                                            <span id="toggle-edit-password" class="fa fa-eye"></span>
                                        </div>
                                        <small>Enter a new password for this user.</small>
                                    </div>
                                    
                                    <fieldset style="margin-top: 15px;">
                                        <legend>Module Access</legend>
                                        <div id="module-checkboxes-container" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;"></div>
                                    </fieldset>
                                    
                                    <div style="margin-top: 20px;">
                                        <button type="submit" class="btn-restart" id="save-user-btn">
                                            <i class="fa fa-save"></i>&nbsp;Save User
                                        </button>
                                        <button type="button" class="btn-stop" id="cancel-edit-btn" style="margin-left: 10px;">
                                            <i class="fa fa-times"></i>&nbsp;Cancel
                                        </button>
                                        <button type="button" class="btn-stop" id="delete-user-btn" style="margin-left: 10px;">
                                            <i class="fa fa-trash"></i>&nbsp;Delete User
                                        </button>
                                    </div>
                                </form>
                                <div id="user-edit-status" class="message" style="margin-top: 15px; display: none;"></div>
                            </div>
                        </div>

                        <div id="ldap-tab" class="auth-tab-content" style="display: none;">
                            <div id="ldap-enable-message" style="display: none; margin-top: 20px;" class="message message-info">
                                <i class="fa fa-info-circle"></i> Please select "Use both local user and LDAP" in the 
                                <a href="#" class="auth-tab-link-inline" data-target="auth-settings-tab">Auth Method</a> tab to configure LDAP settings.
                            </div>

                            <div id="ldap-form-wrapper">
                                <form id="ldap-config-form">
                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="ldap_server" style="display: block; margin-bottom: 5px;"><i class="fa fa-server"></i> Server:</label>
                                        <input type="text" id="ldap_server" name="ldap_server" placeholder="LDAP server address">
                                        <small>The hostname or IP address of your LDAP server</small>
                                    </div>

                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="ldap_rootdn" style="display: block; margin-bottom: 5px;"><i class="fa fa-user"></i> Root DN:</label>
                                        <input type="text" id="ldap_rootdn" name="ldap_rootdn" placeholder="<EMAIL>">
                                        <small>The distinguished name of the user with search privileges</small>
                                    </div>

                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="ldap_password" style="display: block; margin-bottom: 5px;"><i class="fa fa-key"></i> Password:</label>
                                        <div style="position: relative;">
                                            <input type="password" id="ldap_password" name="ldap_password">
                                            <span id="toggle-ldap-password" class="fa fa-eye"></span>
                                        </div>
                                        <small>The password for the root DN user</small>
                                    </div>

                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="ldap_basedn" style="display: block; margin-bottom: 5px;"><i class="fa fa-sitemap"></i> Base DN:</label>
                                        <textarea id="ldap_basedn" name="ldap_basedn" rows="3" placeholder="OU=Users,DC=YourDomain,DC=lan"></textarea>
                                        <small>Enter one DN per line to add multiple branches.</small>
                                        <button type="button" id="browse-ldap-btn" class="btn-restart" style="margin-top: 8px; max-width: 180px;">
                                            <i class="fa fa-folder-open"></i>&nbsp;Browse Directory
                                        </button>
                                    </div>

                                    <button type="button" id="save-ldap-btn" class="btn-restart" style="margin-top: 15px;">
                                        <i class="fa fa-save"></i>&nbsp;Save LDAP Settings
                                    </button>
                                </form>
                                <div id="ldap-config-status" class="message" style="margin-top: 15px; display: none;">
                                    <!-- Status messages will appear here -->
                                </div>
                            </div>
                            <div id="ldap-browse-panel" class="ldap-browse-panel" style="display:none;">
                                <button id="close-ldap-browse" class="btn-stop" style="float:right;"><i class="fa fa-times"></i></button>
                                <h3 style="margin-top:0;">
                                    <i class="fa fa-sitemap"></i>&nbsp;Browse Directory
                                </h3>
                                <div class="input-group" style="display:flex; align-items:center; gap:8px; margin-bottom:10px;">
                                    <label for="ldap-browse-basedn" style="margin:0; white-space:nowrap;">Base DN:&nbsp;</label>
                                    <input type="text" id="ldap-browse-basedn" placeholder="DC=example,DC=com" style="flex:1; min-width:220px;">
                                    <button id="ldap-browse-load" class="btn-restart" style="min-width: 90px;">
                                        <i class="fa fa-refresh"></i>&nbsp;Load
                                    </button>
                                </div>
                                <div id="ldap-browse-tree" style="border:1px solid var(--border); border-radius:var(--radius); padding:10px; max-height:60%; overflow:auto;"></div>
                                <button id="ldap-browse-apply" class="btn-restart" style="margin-top:15px;">
                                    <i class="fa fa-check"></i>&nbsp;Apply Selection
                                </button>
                            </div>
                        </div>
                    </div>
                    <div id="general-config-hostname" class="tab-content-pane">
                        <h2><i class="fa fa-laptop"></i> Hostname</h2>
                        <p>Set the system's hostname and domain name.</p>
                        <!-- Hostname Form -->
                        <form id="hostname-config-form">
                            <div class="input-group" style="display: flex; flex-direction: column;">
                                <label for="local_hostname" style="margin-bottom: 5px;"><i class="fa fa-laptop"></i> Hostname:</label>
                                <?php
                                    $currentHostname = exec('hostname'); // Get the current hostname
                                    $currentHostname = htmlspecialchars($currentHostname, ENT_QUOTES, 'UTF-8'); // Escape for HTML
                                ?>
                                <input type="text" id="local_hostname" name="local_hostname" value="<?php echo $currentHostname; ?>" required>
                                <small>Enter the desired hostname (e.g., server.example.com).</small>
                            </div>
                        </form>
                        <div id="hostname-config-status" class="message" style="margin-top: 15px; display: none;">
                            <!-- Status messages will appear here -->
                        </div>
                    </div>
                    <div id="general-config-time" class="tab-content-pane">
                        <h2><i class="fa fa-clock-o"></i> Time Settings</h2>
                        <p>Configure the system clock, time zone, and time server settings.</p>
                        
                        <!-- Time settings form -->
                        <div id="time-settings-container">
                            <div id="current-time-display" style="margin-bottom: 15px;">
                                <span class="badge" style="font-size: 14px; padding: 8px 12px; background-color: var(--pending-bg); color: var(--text);">
                                    <i class="fa fa-clock-o"></i> <span id="current-date-time">Loading current time...</span>
                                </span>
                            </div>
                            
                            <form id="time-settings-form" action="settings-time-proc.php" method="post">
                                <!-- Hidden fields for old timeserver values -->
                                <input type="hidden" name="ts1_old" id="ts1_old" value="">
                                <input type="hidden" name="ts2_old" id="ts2_old" value="">
                                <input type="hidden" name="ts3_old" id="ts3_old" value="">
                                <input type="hidden" name="ts4_old" id="ts4_old" value="">
                                
                                <fieldset style="margin-bottom: 20px;">
                                    <legend>Time Source</legend>
                                    <label class="radio-label">
                                        <input type="radio" name="time_method" id="time_method_auto" value="auto"> 
                                        Use Time Server
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="time_method" id="time_method_manual" value="manual"> 
                                        Set Date and Time Manually
                                    </label>
                                </fieldset>
                                
                                <div id="timeserver-settings">
                                    <h3><i class="fa fa-server"></i> Time Server Configuration</h3>
                                    
                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="ts1" style="margin-bottom: 5px;"><i class="fa fa-globe"></i> Primary Time Server:</label>
                                        <input type="text" id="ts1" name="ts1" placeholder="e.g., pool.ntp.org">
                                        <small>Required. The primary time server to synchronize with.</small>
                                    </div>
                                    
                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="ts2" style="margin-bottom: 5px;"><i class="fa fa-globe"></i> Secondary Time Server:</label>
                                        <input type="text" id="ts2" name="ts2" placeholder="e.g., 0.pool.ntp.org">
                                        <small>Optional failover server.</small>
                                    </div>
                                    
                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="ts3" style="margin-bottom: 5px;"><i class="fa fa-globe"></i> Additional Time Server:</label>
                                        <input type="text" id="ts3" name="ts3" placeholder="e.g., 1.pool.ntp.org">
                                        <small>Optional additional server.</small>
                                    </div>
                                    
                                    <div class="input-group" style="display: flex; flex-direction: column;">
                                        <label for="ts4" style="margin-bottom: 5px;"><i class="fa fa-globe"></i> Additional Time Server:</label>
                                        <input type="text" id="ts4" name="ts4" placeholder="e.g., 2.pool.ntp.org">
                                        <small>Optional additional server.</small>
                                    </div>
                                </div>
                                
                                <div id="manual-time-settings" style="display: none;">
                                    <h3><i class="fa fa-calendar"></i> Manual Date & Time</h3>
                                    
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px;">
                                        <div class="input-group" style="display: flex; flex-direction: column;">
                                            <label for="year" style="margin-bottom: 5px;"><i class="fa fa-calendar"></i> Year:</label>
                                            <select id="year" name="year" class="form-control" style="max-width: none;">
                                                <!-- JS will populate years -->
                                            </select>
                                        </div>
                                        
                                        <div class="input-group" style="display: flex; flex-direction: column;">
                                            <label for="month" style="margin-bottom: 5px;"><i class="fa fa-calendar"></i> Month:</label>
                                            <select id="month" name="month" class="form-control" style="max-width: none;">
                                                <!-- JS will populate months -->
                                            </select>
                                        </div>
                                        
                                        <div class="input-group" style="display: flex; flex-direction: column;">
                                            <label for="day" style="margin-bottom: 5px;"><i class="fa fa-calendar"></i> Day:</label>
                                            <select id="day" name="day" class="form-control" style="max-width: none;">
                                                <!-- JS will populate days -->
                                            </select>
                                        </div>
                                        
                                        <div class="input-group" style="display: flex; flex-direction: column;">
                                            <label for="hour" style="margin-bottom: 5px;"><i class="fa fa-clock-o"></i> Hour:</label>
                                            <select id="hour" name="hour" class="form-control" style="max-width: none;">
                                                <!-- JS will populate hours -->
                                            </select>
                                        </div>
                                        
                                        <div class="input-group" style="display: flex; flex-direction: column;">
                                            <label for="min" style="margin-bottom: 5px;"><i class="fa fa-clock-o"></i> Minute:</label>
                                            <select id="min" name="min" class="form-control" style="max-width: none;">
                                                <!-- JS will populate minutes -->
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="margin-top: 25px;">
                                    <button type="submit" id="save-time-settings" class="btn-restart">
                                        <i class="fa fa-save"></i>&nbsp;Save Settings
                                    </button>
                                    <button type="button" id="sync-time-now" class="btn-restart" style="margin-left: 10px;">
                                        <i class="fa fa-refresh"></i>&nbsp;Synchronize Now
                                    </button>
                                </div>
                                
                                <div id="time-settings-status" class="message" style="display: none; margin-top: 20px;"></div>
                            </form>
                        </div>
                    </div>

                    <!-- Auto Detection Content Panes (NEW) -->
                    <div id="detection-agent-type" class="tab-content-pane">
                        <h2><i class="fa fa-laptop"></i> Agent Type</h2>
                        <p>Select the default monitoring method for newly discovered Windows and Unix/Linux systems.</p>

                        <fieldset>
                            <legend>Windows</legend>
                            <p>Select the type of monitoring for Windows systems.</p>
                            <label class="radio-label">
                                <input type="radio" name="windowsAgentType" value="wmi" checked> WMI (no agent required. Uses TCP port 135)
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="windowsAgentType" value="nsclient"> NSClient++ (requires agent installation on monitored servers. Uses TCP port 12489)
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="windowsAgentType" value="snmp"> SNMP (no agent required, but SNMP must be enabled on servers. Uses UDP port 161)
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="windowsAgentType" value="ping"> Ping checks only
                            </label>
                        </fieldset>

                        <fieldset>
                            <legend>Unix/Linux</legend>
                            <p>Select the type of monitoring for Unix/Linux systems.</p>
                            <label class="radio-label">
                                <input type="radio" name="linuxAgentType" value="snmp" checked> SNMP (no agent required, but SNMP must be enabled on servers. Uses UDP port 161)
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="linuxAgentType" value="nrpe"> NRPE (requires agent installation on monitored servers. Uses TCP port 5666)
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="linuxAgentType" value="ping"> Ping checks only
                            </label>
                        </fieldset>
                    </div>
                    <div id="detection-network" class="tab-content-pane">
                        <h2><i class="fa fa-exchange"></i> Network Device Monitoring Options</h2>
                        <fieldset>
                            <legend>Switch Port Detection</legend>
                            <p>Automatically detect and monitor switch port interfaces in APM?</p>
                            <label class="radio-label">
                                <input type="radio" name="switchPortDetection" value="all_up" checked> Yes, detect and monitor all switchports that are up.
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="switchPortDetection" value="uplinks_only"> Yes, but detect and monitor switch uplinks only.
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="switchPortDetection" value="no"> No
                            </label>
                        </fieldset>

                        <fieldset>
                            <legend>Additional Options</legend>
                            <label class="checkbox-label">
                                <input type="checkbox" name="monitorSpanningTree"> Monitor Spanning Tree on switch port in APM (uplink ports only)
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="importToNCM"> Import network devices into NCM for automatic configuration backups.
                            </label>
                        </fieldset>
                    </div>
                    <div id="detection-illegal-chars" class="tab-content-pane">
                        <h2><i class="fa fa-ban"></i> Illegal Character Override</h2>
                        <p>The special characters below are generally reserved for system use and may be problematic if they are used in your passwords, usernames, hostnames, etc. By default they are considered illegal in blësk.</p>
                        <p>However, if there are one or more special characters you wish to use in certain situations you may do so by removing them from the list below. <strong>Please be aware that this action may cause problems for certain devices being monitored.</strong></p>

                        <div class="input-group">
                            <label for="snmpIllegalChars">SNMP illegal characters:</label>
                            <input type="text" id="snmpIllegalChars" name="snmpIllegalChars" value="!|:$@">
                            <small>Default values: <code>!|:$@</code></small>
                        </div>

                        <div class="input-group">
                            <label for="wmiIllegalChars">WMI illegal characters:</label>
                            <input type="text" id="wmiIllegalChars" name="wmiIllegalChars" value="$%@">
                            <small>Default values: <code>$%@</code></small>
                        </div>
                    </div>
                    <div id="detection-hostnames" class="tab-content-pane">
                        <h2><i class="fa fa-tag"></i> Hostnames</h2>
                        <fieldset>
                            <legend>Shortened Hostnames</legend>
                            <p>The names of monitored hosts can be automatically shortened for easier viewing in the graphical user interface. If your hostnames are in the form of a fully qualified domain name (ex: <code>serv01.domain.lan</code>), choose a shortened style below to display.</p>
                            <p><strong>Note:</strong> This only affects new hosts added via discovery. Any hosts already being monitored will not be affected.</p>
                            <label class="radio-label">
                                <input type="radio" name="hostnameStyle" value="short" checked> SERV01 (Short name only)
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="hostnameStyle" value="domain"> SERV01.DOMAIN (Short name and first domain part)
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="hostnameStyle" value="full"> Neither, keep the full hostname (serv01.domain.lan)
                            </label>
                        </fieldset>
                    </div>
                    <div id="detection-service-discovery" class="tab-content-pane">
                        <h2><i class="fa fa-cogs"></i> Service Discovery Management</h2>
                        <p>Configure service discovery settings and manage exclusions.</p>
                        
                        <!-- Tabs for Service Discovery sub-sections -->
                        <div class="auth-tabs">
                            <a href="#" class="service-discovery-tab-link active" data-target="service-discovery-settings-tab">
                                <i class="fa fa-cogs"></i> Settings
                            </a>
                            <a href="#" class="service-discovery-tab-link" data-target="service-discovery-muted-tab">
                                <i class="fa fa-volume-off"></i> Muted Hosts
                            </a>
                            <a href="#" class="service-discovery-tab-link" data-target="service-discovery-exclusions-tab">
                                <i class="fa fa-ban"></i> Exclusions
                            </a>
                        </div>
                        
                        <!-- Tab content containers -->
                        <div id="service-discovery-settings-tab" class="service-discovery-tab-content">
                            <p>Configure automatic service discovery settings for hosts.</p>
                            <!-- Service Discovery Toggle -->
                            <fieldset style="margin-bottom: 20px;">
                                <legend>Service Discovery</legend>
                                <p>Enable or disable automatic service discovery for hosts.</p>
                                <label class="radio-label">
                                    <input type="radio" name="serviceDiscovery" value="on" id="service-discovery-on"> On
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="serviceDiscovery" value="off" id="service-discovery-off"> Off
                                </label>
                                <small style="display: block; margin-top: 5px;">When enabled, the system will automatically scan hosts for new services in the background.</small>
                            </fieldset>
                        </div>
                        
                        <div id="service-discovery-muted-tab" class="service-discovery-tab-content" style="display: none;">
                            <p>Hosts listed here are muted from service discovery notifications. Notifications will not be shown for services that have previously been rejected on these hosts. If new, never-before-rejected services are discovered, notifications will be sent as usual.</p>
                            <div class="search-row">
                                <input type="text" id="muted-search-input" placeholder="Search IP, hostname, infra..." style="max-width: 320px; width: 100%; margin-bottom: 10px;">
                            </div>
                            <div id="muted-no-results" class="message message-info" style="display: none; margin-bottom: 10px;">
                                No matching hosts.
                            </div>
                            <button id="refresh-muted-btn" class="btn btn-secondary btn-restart" style="margin-bottom: 15px;"><i class="fa fa-refresh"></i>&nbsp;Refresh Muted List</button>
                            <div id="muted-table-container">
                                <!-- Muted hosts table will be loaded here -->
                                <div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Loading muted hosts...</div>
                            </div>
                        </div>
                        
                        <div id="service-discovery-exclusions-tab" class="service-discovery-tab-content" style="display: none;">
                            <p>Hosts that have been marked to never scan for new services. These hosts will be completely excluded from background scanning. Even if service discovery is enabled, these hosts will not be scanned for new services.</p>
                            <div class="search-row">
                                <input type="text" id="do-not-scan-search-input" placeholder="Search IP, hostname, infra..." style="max-width: 320px; width: 100%; margin-bottom: 10px;">
                            </div>
                            <div id="do-not-scan-no-results" class="message message-info" style="display: none; margin-bottom: 10px;">
                                No matching hosts.
                            </div>
                            <button id="refresh-do-not-scan-btn" class="btn btn-secondary btn-restart" style="margin-bottom: 15px;"><i class="fa fa-refresh"></i>&nbsp;Refresh List</button>
                            <div id="do-not-scan-table-container">
                                <!-- DO-NOT-SCAN hosts table will be loaded here -->
                                <div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Loading service discovery exclusions...</div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="detection-host-discovery" class="tab-content-pane">
                        <h2><i class="fa fa-sitemap"></i> Host Discovery Management</h2>
                        <p>Configure host discovery settings and manage blacklisted hosts.</p>
                        
                        <!-- Tabs for Host Discovery sub-sections -->
                        <div class="auth-tabs">
                            <a href="#" class="host-discovery-tab-link active" data-target="host-discovery-settings-tab">
                                <i class="fa fa-cogs"></i> Settings
                            </a>
                            <a href="#" class="host-discovery-tab-link" data-target="host-discovery-blacklist-tab">
                                <i class="fa fa-list"></i> Host Blacklist
                            </a>
                            <a href="#" class="host-discovery-tab-link" data-target="host-discovery-subnet-blacklist-tab">
                                <i class="fa fa-ban"></i> Subnet Blacklist
                            </a>
                        </div>
                        
                        <!-- Tab content containers -->
                        <div id="host-discovery-settings-tab" class="host-discovery-tab-content">
                            <p>Configure automatic host discovery settings.</p>
                            <!-- Host Discovery Toggle -->
                            <fieldset style="margin-bottom: 20px;">
                                <legend>Host Discovery</legend>
                                <p>Enable or disable automatic host discovery.</p>
                                <label class="radio-label">
                                    <input type="radio" name="hostDiscovery" value="on" id="host-discovery-on"> On
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="hostDiscovery" value="off" id="host-discovery-off"> Off
                                </label>
                                <small style="display: block; margin-top: 5px;">When enabled, the system will automatically discover new hosts on known subnets.</small>
                            </fieldset>
                        </div>
                        
                        <div id="host-discovery-blacklist-tab" class="host-discovery-tab-content" style="display: none;">
                            <p>Hosts that have been manually blacklisted from discovery or monitoring.</p>
                            <div class="search-row">
                                <input type="text" id="blacklist-search-input" placeholder="Search IP, hostname, infra, subnet..." style="max-width: 320px; width: 100%; margin-bottom: 10px;">
                            </div>
                            <div id="blacklist-no-results" class="message message-info" style="display: none; margin-bottom: 10px;">
                                No matching hosts.
                            </div>
                            <button id="refresh-blacklist-btn" class="btn btn-secondary btn-restart" style="margin-bottom: 15px;"><i class="fa fa-refresh"></i>&nbsp;Refresh List</button>
                            <div id="blacklist-table-container">
                                <!-- Blacklist table will be loaded here -->
                                <div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Loading blacklist...</div>
                            </div>
                        </div>
                        
                        <div id="host-discovery-subnet-blacklist-tab" class="host-discovery-tab-content" style="display: none;">
                            <p>Blacklist entire subnets to prevent host discovery scanning on all IPs within those networks. Select a subnet from the dropdown below and click "Blacklist Subnet" to exclude it from scanning. This will not affect hosts that are currently monitored.</p>
                            <div style="margin-bottom: 20px;">
                                <label for="subnet-select" style="display: block; margin-bottom: 8px; font-weight: bold;">Select Subnet to Blacklist:</label>
                                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <select id="subnet-select" style="min-width: 350px; width: 350px; padding: 8px; margin-right: 15px; background-color: var(--surface); color: var(--text); border: 1px solid var(--border); border-radius: 4px; font-size: 13px; height: 36px;">
                                        <option value="">Loading subnets...</option>
                                    </select>
                                    <button id="add-subnet-blacklist-btn" class="btn btn-stop">
                                        <i class="fa fa-ban"></i>&nbsp;Blacklist Subnet
                                    </button>
                                </div>
                                <div>
                                    <button id="refresh-subnet-blacklist-btn" class="btn btn-secondary btn-restart">
                                        <i class="fa fa-refresh"></i>&nbsp;Refresh
                                    </button>
                                </div>
                            </div>
                            <div id="subnet-blacklist-status" class="message" style="display: none; margin-bottom: 15px;"></div>
                            <div id="subnet-blacklist-container">
                                <!-- Subnet blacklist will be loaded here -->
                                <div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Loading subnet blacklist...</div>
                            </div>
                        </div>
                    </div>
                    

                    <?php include "src/apmNagiosQLSettings/apmSettings.php" ?>
                </div>
            </div>
        </div>
    </div>
    <script src="functions/settingsFunctions/settingsDOMLoaded.js"></script>
    <script src="functions/settingsFunctions/serviceActions.js"></script>
    <script src="functions/settingsFunctions/helperFunctions.js"></script>
    <script src="functions/settingsFunctions/configurationHelpers.js"></script>
    <script src="functions/settingsFunctions/licenseFunctions.js"></script>
    <script src="functions/settingsFunctions/authConfigHelpers.js"></script>
    <script src="functions/settingsFunctions/userModuleHelpers.js"></script>
    <script src="functions/settingsFunctions/timeSettingsHelpers.js"></script>
    <script src="functions/settingsFunctions/backupConfigHelpers.js"></script>
    <script src="functions/settingsFunctions/blacklistManager.js"></script>
    <script src="functions/settingsFunctions/apmIframeLoader.js"></script>
    <script src="functions/settingsFunctions/serviceDiscoveryHelpers.js"></script>
    <script src="functions/settingsFunctions/hostDiscoveryHelpers.js"></script>