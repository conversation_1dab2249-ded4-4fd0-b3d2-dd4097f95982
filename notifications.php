<?php
include 'language_selector.php';
include 'theme_loader.php'; // Load chosenTheme variable
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Blesk - Notifications</title>
    <!-- Base + page-specific styles -->
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/notifications.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">

    <!-- External libs / helpers reused across the project -->
    <script src="functions/d3.v7.min.js"></script>
    <script src="functions/fetchHostGroups.js"></script>
    <script src="functions/helperFunctions.js"></script>
    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <!-- Hostlist modal helper -->
    <script src="functions/hostlistPhpFunctions/modalHandlers.js"></script>
    <!-- Page-specific logic -->
    <script defer src="functions/notificationsFunctions/notifications-core.js"></script>
    <script defer src="functions/notificationsFunctions/notifications-ui.js"></script>
    <script defer src="functions/notificationsFunctions/notifications-renderer.js"></script>
    <script defer src="functions/notificationsFunctions/notifications-filters.js"></script>
    <script defer src="functions/notificationsFunctions/notifications-filters-modal.js"></script>
    <script defer src="functions/notificationsFunctions/notifications.js"></script>
    <script src="functions/setMainView.js"></script>
    <script src="functions/globalAiAssistant.js"></script>
</head>
<body>
    <!-- Page Header -->
    <header>
        <div style="display: flex; align-items: center; gap: 15px;">
            <a href="../../index.php">
                <img style="width: 25px;" src="../../images/blesk-favicon.svg" alt="Home">
            </a>
            <div class="breadcrumbs">
                <a href="infra.php"><i class="fa fa-home"></i> Home</a>
                <span class="separator">/</span>
                <span class="current"><i class="fa fa-bell"></i> Notifications</span>
            </div>
        </div>
        <div class="header-content">
            <div class="hamburger">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </div>
            <div class="header-buttons">
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="viewDropdownBtn"><i class="fa fa-eye"></i> View <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="viewDropdownContent">
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'bubble', 'https://<?php echo $_SERVER['HTTP_HOST']; ?>/bubblemaps/infra.php')"><i class="fa fa-th"></i> Infrastructure Mapping</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'list', 'hostlist.php')"><i class="fa fa-list"></i> Asset Overview</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'table', 'tableview.php')"><i class="fa fa-table"></i> Data Grid</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'dashboards', 'networkmap.php')"><i class="fa fa-map"></i> Dashboards and Visualization</a>
                    </div>
                </div>
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="menuDropdownBtn"><i class="fa fa-bars"></i> Control Panel <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="menuDropdownContent">
                        <a class="header-button" onclick='showModal("https://<?php echo $_SERVER["HTTP_HOST"]; ?>/bubblemaps/credentials.php")'><i class="fa fa-key"></i> Credentials</a>
                        <a class="header-button" href="reports.php"><i class="fa fa-file-text"></i> Reports</a>
                    </div>
                </div>
                <a class="header-button" id="openSettingsBtn"><i class="fa fa-cog"></i> Advanced Settings</a>
            </div>
        </div>
    </header>
    <!-- Main container -->
    <div class="notifications-container">
        <!-- Controls bar -->
        <div class="notifications-controls">
            <div class="controls-row primary-controls">
                <div class="control-group">
                    <label for="notification-search">Search:</label>
                    <div class="search-input-wrapper">
                        <input type="text" id="notification-search" placeholder="Search hosts or services...">
                        <div class="search-actions">
                            <button type="button" id="notification-search-clear" class="notification-clear-search" title="Clear search">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <button id="notification-filters" class="generate-btn" title="Open filters">
                    <i class="fa fa-filter"></i> Filters
                </button>
                
                <!-- Hidden inputs to store filter values -->
                <input type="hidden" id="notification-start">
                <input type="hidden" id="notification-end">
                <input type="hidden" id="notification-type-filter" value="all">
            </div>
        </div>

        <!-- Results / table goes here -->
        <div id="notifications-content" class="notifications-content">
            <div class="notifications-placeholder"></div>
        </div>
    </div>
    <!-- iframe Modal for credentials and other content -->
    <div id="infoModal" class="iframeModal">
        <div class="iframeModal-content" id="iframeModal-content">
            <span class="iframeMclose" title="Back" aria-label="Back">←</span>
            <div class="iframe-loader"></div>
            <iframe id="modal-frame"></iframe>
        </div>
    </div>

    <?php include "settingsModal.php"; ?>

    <!-- Filters Modal -->
    <div id="filtersModal" class="sr-modal">
        <div class="sr-modal-content" style="min-width: 600px; max-width: 800px;">
            <span class="sr-close" onclick="document.getElementById('filtersModal').style.display='none'">×</span>
            <h2>Notification Filters</h2>
            <form id="filters-form">
                <div class="form-row">
                    <div class="sr-field">
                        <label>Quick Time Range</label>
                        <div class="time-shortcuts">
                            <button type="button" class="time-shortcut-btn" data-days="1">Last 24h</button>
                            <button type="button" class="time-shortcut-btn" data-days="7">Last 7 days</button>
                            <button type="button" class="time-shortcut-btn" data-days="30">Last 30 days</button>
                            <button type="button" class="time-shortcut-btn" data-days="90">Last 90 days</button>
                            <button type="button" class="time-shortcut-btn" data-days="365">Last year</button>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="sr-field">
                        <label for="filter-start">Start Date & Time</label>
                        <input type="datetime-local" id="filter-start">
                    </div>
                    <div class="sr-field">
                        <label for="filter-end">End Date & Time</label>
                        <input type="datetime-local" id="filter-end">
                    </div>
                </div>

                <div class="form-row">
                    <div class="sr-field">
                        <label for="filter-type">Notification Type</label>
                        <select id="filter-type">
                            <option value="all">All notifications</option>
                            <optgroup label="General Notifications">
                                <option value="all-service">All service notifications</option>
                                <option value="all-host">All host notifications</option>
                            </optgroup>
                            <optgroup label="Service Notifications">
                                <option value="service-custom">Service custom</option>
                                <option value="service-ack">Service acknowledgements</option>
                                <option value="service-warning">Service warning</option>
                                <option value="service-unknown">Service unknown</option>
                                <option value="service-critical">Service critical</option>
                                <option value="service-recovery">Service recovery</option>
                                <option value="service-flap">Service flapping</option>
                                <option value="service-downtime">Service downtime</option>
                            </optgroup>
                            <optgroup label="Host Notifications">
                                <option value="host-custom">Host custom</option>
                                <option value="host-ack">Host acknowledgements</option>
                                <option value="host-down">Host down</option>
                                <option value="host-unreachable">Host unreachable</option>
                                <option value="host-recovery">Host recovery</option>
                                <option value="host-flap">Host flapping</option>
                                <option value="host-downtime">Host downtime</option>
                            </optgroup>
                        </select>
                    </div>
                </div>

                <div class="sr-actions">
                    <button type="button" id="filters-cancel" class="generate-btn">Cancel</button>
                    <button type="submit" id="filters-apply" class="generate-btn">Apply Filters</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Hamburger toggle script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const hamburger = document.querySelector('.hamburger');
            const headerButtons = document.querySelector('.header-buttons');
            if (hamburger) {
                hamburger.addEventListener('click', function(e) {
                    e.stopPropagation();
                    this.classList.toggle('active');
                    headerButtons.classList.toggle('active');
                });
                document.addEventListener('click', function(event) {
                    if (!event.target.closest('.header-content')) {
                        hamburger.classList.remove('active');
                        headerButtons.classList.remove('active');
                    }
                });
            }
        });

        
        // Initialize the translator
        const dictionaries = <?php echo json_encode($dictionaries); ?>;
        const selectedLang = '<?php echo $selectedLang; ?>';
        const translator = new Translator(dictionaries, selectedLang);
        translator.init();
        initTranslationObserver(translator);
        
        // Handle View dropdown
        const viewDropdownBtn = document.getElementById('viewDropdownBtn');
        const viewDropdownContent = document.getElementById('viewDropdownContent');
        if(viewDropdownBtn && viewDropdownContent){
            viewDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                // Close other dropdowns first
                const menuDropdownContent = document.getElementById('menuDropdownContent');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
                viewDropdownContent.classList.toggle('show');
            });
        }
        
        // Handle Control Panel dropdown
        const menuDropdownBtn = document.getElementById('menuDropdownBtn');
        const menuDropdownContent = document.getElementById('menuDropdownContent');
        if(menuDropdownBtn && menuDropdownContent){
            menuDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                // Close other dropdowns first
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                menuDropdownContent.classList.toggle('show');
            });
        }
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e){
            if(!e.target.closest('.header-dropdown')){
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
            }
        });
    </script>
</body>
</html>
