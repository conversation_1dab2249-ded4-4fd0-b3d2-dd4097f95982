<?php
include 'language_selector.php';
include 'theme_loader.php'; // Load chosenTheme variable
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Blesk - Reports</title>
    <!-- Base + page-specific styles -->
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/reports.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">

    <!-- External libs / helpers reused across the project -->
    <script src="functions/d3.v7.min.js"></script>
    <script src="functions/fetchHostGroups.js"></script>
    <script src="functions/helperFunctions.js"></script>
    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <!-- Hostlist modal helper -->
    <script src="functions/hostlistPhpFunctions/modalHandlers.js"></script>
    <!-- Bubble hostnames map functionality -->
    <script src="functions/reportsFunctions/bubble-hostnames.js"></script>
    <!-- Page-specific logic -->
    <script defer src="functions/reportsFunctions/reports-core.js"></script>
    <script defer src="functions/reportsFunctions/reports-ui.js"></script>
    <script defer src="functions/reportsFunctions/reports-renderer.js"></script>
    <script defer src="functions/reportsFunctions/reports-filters.js"></script>
    <script defer src="functions/reportsFunctions/reports-filters-modal.js"></script>
    <script defer src="functions/reportsFunctions/reports-save.js"></script>
    <script defer src="functions/reportsFunctions/scheduled-reports-ui.js"></script>
    <script defer src="functions/reportsFunctions/reports.js"></script>
    <script src="functions/setMainView.js"></script>
    <script src="functions/globalAiAssistant.js"></script>
</head>
<body>
    <!-- Page Header -->
    <header>
        <div style="display: flex; align-items: center; gap: 15px;">
            <a href="../../index.php">
                <img style="width: 25px;" src="../../images/blesk-favicon.svg" alt="Home">
            </a>
            <div class="breadcrumbs">
                <a href="infra.php"><i class="fa fa-home"></i> Home</a>
                <span class="separator">/</span>
                <span class="current"><i class="fa fa-file-text"></i> Reports</span>
            </div>
        </div>
        <div class="header-content">
            <div class="hamburger">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </div>
            <div class="header-buttons">
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="viewDropdownBtn"><i class="fa fa-eye"></i> View <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="viewDropdownContent">
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'bubble', 'https://<?php echo $_SERVER['HTTP_HOST']; ?>/bubblemaps/infra.php')"><i class="fa fa-th"></i> Infrastructure Mapping</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'list', 'hostlist.php')"><i class="fa fa-list"></i> Asset Overview</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'table', 'tableview.php')"><i class="fa fa-table"></i> Data Grid</a>
                        <a class="header-button" href="#" onclick="handleViewDropdownClick(event, 'dashboards', 'networkmap.php')"><i class="fa fa-map"></i> Dashboards and Visualization</a>
                    </div>
                </div>
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="menuDropdownBtn"><i class="fa fa-bars"></i> Control Panel <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="menuDropdownContent">
                        <a class="header-button" onclick='showModal("https://<?php echo $_SERVER["HTTP_HOST"]; ?>/bubblemaps/credentials.php")'><i class="fa fa-key"></i> Credentials</a>
                        <a class="header-button" href="notifications.php"><i class="fa fa-bell"></i> Notifications</a>
                    </div>
                </div>
                <a class="header-button" id="openSettingsBtn"><i class="fa fa-cog"></i> Advanced Settings</a>
            </div>
        </div>
    </header>
    <!-- Main container -->
    <div class="reports-container">
        <!-- Controls bar -->
        <div class="reports-controls">
            <div class="controls-row primary-controls">
                <!-- Status filter buttons -->
                <div id="reports-status-filters" class="reports-status-filters"></div>
                <div class="vertical-separator"></div>
                <div class="control-group">
                    <label for="report-search">Search:</label>
                    <div class="search-input-wrapper">
                        <input type="text" id="report-search" placeholder="Search hosts or services...">
                        <div class="search-actions">
                            <button type="button" id="report-search-clear" class="report-clear-search" title="Clear search">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <button id="report-filters" class="generate-btn" title="Open filters">
                    <i class="fa fa-filter"></i> Filters
                </button>

                <!-- Hidden inputs to store filter values -->
                <input type="hidden" id="report-start">
                <input type="hidden" id="report-end">
                <input type="hidden" id="report-type" value="hostgroups">
                <input type="hidden" id="report-hostgroup" value="all">
                <input type="hidden" id="report-service-description" value="all">
                <button id="report-save" class="generate-btn" title="Save PDF report to server"><i class="fa fa-save"></i></button>
                <button id="report-schedule" class="generate-btn" title="Schedule reports">
                    <i class="fa fa-envelope-o"></i>
                    <span id="scheduled-reports-badge" class="scheduled-reports-badge" style="display: none;">0</span>
                </button>
                <button id="view-saved-reports" class="generate-btn" title="View saved reports">
                    <i class="fa fa-folder-open"></i>
                    <span id="saved-reports-badge" class="saved-reports-badge" style="display: none;">0</span>
                </button>
            </div>
        </div>

        <!-- Results / table goes here -->
        <div id="reports-content" class="reports-content">
            <div class="reports-placeholder"></div>
        </div>
    </div>
    <!-- iframe Modal for credentials and other content -->
    <div id="infoModal" class="iframeModal">
        <div class="iframeModal-content" id="iframeModal-content">
            <span class="iframeMclose" title="Back" aria-label="Back">←</span>
            <div class="iframe-loader"></div>
            <iframe id="modal-frame"></iframe>
        </div>
    </div>

    <?php include "settingsModal.php"; ?>

    <div id="scheduleReportModal" class="sr-modal">
        <div class="sr-modal-content" style="min-width: 900px; max-width: 1200px;">
            <span class="sr-close">×</span>
            <div class="schedule-modal-header">
                <h2>Scheduled Reports</h2>
                <button id="add-new-report-btn" class="generate-btn">Add New Report</button>
            </div>
            
            <!-- Search Bar -->
            <div class="scheduled-reports-search">
                <div class="search-input-wrapper">
                    <input type="text" id="scheduled-reports-search-input" placeholder="Search by report name or email...">
                    <div class="search-actions">
                        <button type="button" id="scheduled-reports-search-clear" class="report-clear-search" title="Clear search">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Scheduled Reports List -->
            <div id="scheduled-reports-list" class="scheduled-reports-list">
                <div class="scheduled-reports-loading">Loading scheduled reports...</div>
            </div>
            
            <!-- Add New Report Form (initially hidden) -->
            <div id="add-report-form" class="add-report-form" style="display: none;">
                <h3>Add New Scheduled Report</h3>
                <form id="schedule-form">
                    <div class="form-row">
                        <div class="sr-field">
                            <label for="sr-name">Report Name *</label>
                            <input type="text" id="sr-name" required placeholder="Daily Infrastructure Report">
                        </div>
                        <div class="sr-field">
                            <label for="sr-email">Email address(es) *</label>
                            <input type="text" id="sr-email" required placeholder="<EMAIL>; <EMAIL>">
                            <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                                Separate multiple emails with semicolons (;)
                            </small>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="sr-field">
                            <label for="sr-frequency">Frequency</label>
                            <select id="sr-frequency">
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>
                        <div class="sr-field">
                            <label for="sr-time">Time to send report</label>
                            <input type="time" id="sr-time" value="00:05">
                            <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                                The time when the report will be sent via email.
                            </small>
                        </div>
                        <div class="sr-field" id="sr-weekday-field" style="display: none;">
                            <label for="sr-weekday">Day of Week</label>
                            <select id="sr-weekday">
                                <option value="0">Sunday</option>
                                <option value="1">Monday</option>
                                <option value="2">Tuesday</option>
                                <option value="3">Wednesday</option>
                                <option value="4">Thursday</option>
                                <option value="5">Friday</option>
                                <option value="6">Saturday</option>
                            </select>
                        </div>
                        <div class="sr-field" id="sr-monthday-field" style="display: none;">
                            <label for="sr-monthday">Day of Month</label>
                            <select id="sr-monthday">
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="13">13</option>
                                <option value="14">14</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="19">19</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                                <option value="24">24</option>
                                <option value="25">25</option>
                                <option value="26">26</option>
                                <option value="27">27</option>
                                <option value="28">28</option>
                                <option value="29">29</option>
                                <option value="30">30</option>
                                <option value="31">31</option>
                            </select>
                            <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                                Note: Avoid choosing 30th or 31st as some months don't have these days and the report won't run.
                            </small>
                        </div>
                        <div class="sr-field">
                            <label for="sr-range">Report time period</label>
                            <input type="number" id="sr-range" min="1" max="365" value="1" title="Number of days to include in the report">
                            <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                                The report will show data for the last X days. For example, if set to 7, the report will show the last 7 days of monitoring data.
                            </small>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="sr-field">
                            <label for="sr-type">Report Type</label>
                            <select id="sr-type">
                                <option value="hostgroups,services">Hosts & Services</option>
                                <option value="hostgroups">Hosts Only</option>
                                <option value="services">Services Only</option>
                            </select>
                        </div>
                        <div class="sr-field" id="sr-entity-field">
                            <label for="sr-entity">Entity (optional)</label>
                            <select id="sr-entity">
                                <option value="">All</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row" id="sr-service-description-field" style="display: none;">
                        <div class="sr-field">
                            <label for="sr-service-description">Service Description (optional)</label>
                            <select id="sr-service-description">
                                <option value="">All</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="sr-field">
                            <label>Host Statuses</label>
                            <div class="sr-checkbox-group" id="sr-host-statuses">
                                <label><input type="checkbox" value="up" checked> Up</label>
                                <label><input type="checkbox" value="down" checked> Down</label>
                                <label><input type="checkbox" value="unreachable" checked> Unreach</label>
                            </div>
                        </div>
                        <div class="sr-field">
                            <label>Service Statuses</label>
                            <div class="sr-checkbox-group" id="sr-svc-statuses">
                                <label><input type="checkbox" value="ok" checked> OK</label>
                                <label><input type="checkbox" value="warning" checked> Warn</label>
                                <label><input type="checkbox" value="critical" checked> Crit</label>
                                <label><input type="checkbox" value="unknown" checked> Unk</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="sr-field">
                        <label>
                            <input type="checkbox" id="sr-save-to-server" checked> Save report to server
                        </label>
                        <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                            Reports will be accessible from the Reports page
                        </small>
                    </div>
                    
                    <div id="sr-status" class="sr-status"></div>
                    <div class="sr-actions">
                        <button type="button" id="sr-cancel" class="generate-btn">Cancel</button>
                        <button type="submit" id="sr-save" class="generate-btn">Add Report</button>
                    </div>
                </form>
            </div>

            <!-- Edit Report Form (initially hidden) -->
            <div id="edit-report-form" class="add-report-form" style="display: none;">
                <h3>Edit Scheduled Report</h3>
                <form id="edit-schedule-form">
                    <input type="hidden" id="edit-sr-original-name">
                    <div class="form-row">
                        <div class="sr-field">
                            <label for="edit-sr-name">Report Name *</label>
                            <input type="text" id="edit-sr-name" required placeholder="Daily Infrastructure Report">
                        </div>
                        <div class="sr-field">
                            <label for="edit-sr-email">Email address(es) *</label>
                            <input type="text" id="edit-sr-email" required placeholder="<EMAIL>; <EMAIL>">
                            <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                                Separate multiple emails with semicolons (;)
                            </small>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="sr-field">
                            <label for="edit-sr-frequency">Frequency</label>
                            <select id="edit-sr-frequency">
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>
                        <div class="sr-field">
                            <label for="edit-sr-time">Time to send report</label>
                            <input type="time" id="edit-sr-time" value="00:05">
                            <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                                The time when the report will be sent via email.
                            </small>
                        </div>
                        <div class="sr-field" id="edit-sr-weekday-field" style="display: none;">
                            <label for="edit-sr-weekday">Day of Week</label>
                            <select id="edit-sr-weekday">
                                <option value="0">Sunday</option>
                                <option value="1">Monday</option>
                                <option value="2">Tuesday</option>
                                <option value="3">Wednesday</option>
                                <option value="4">Thursday</option>
                                <option value="5">Friday</option>
                                <option value="6">Saturday</option>
                            </select>
                        </div>
                        <div class="sr-field" id="edit-sr-monthday-field" style="display: none;">
                            <label for="edit-sr-monthday">Day of Month</label>
                            <select id="edit-sr-monthday">
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="13">13</option>
                                <option value="14">14</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="19">19</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                                <option value="24">24</option>
                                <option value="25">25</option>
                                <option value="26">26</option>
                                <option value="27">27</option>
                                <option value="28">28</option>
                                <option value="29">29</option>
                                <option value="30">30</option>
                                <option value="31">31</option>
                            </select>
                            <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                                Note: Avoid choosing 30th or 31st as some months don't have these days and the report won't run.
                            </small>
                        </div>
                        <div class="sr-field">
                            <label for="edit-sr-range">Report time period</label>
                            <input type="number" id="edit-sr-range" min="1" max="365" value="1" title="Number of days to include in the report">
                            <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                                The report will show data for the last X days. For example, if set to 7, the report will show the last 7 days of monitoring data.
                            </small>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="sr-field">
                            <label for="edit-sr-type">Report Type</label>
                            <select id="edit-sr-type">
                                <option value="hostgroups,services">Hosts & Services</option>
                                <option value="hostgroups">Hosts Only</option>
                                <option value="services">Services Only</option>
                            </select>
                        </div>
                        <div class="sr-field" id="edit-sr-entity-field">
                            <label for="edit-sr-entity">Entity (optional)</label>
                            <select id="edit-sr-entity">
                                <option value="">All</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row" id="edit-sr-service-description-field" style="display: none;">
                        <div class="sr-field">
                            <label for="edit-sr-service-description">Service Description (optional)</label>
                            <select id="edit-sr-service-description">
                                <option value="">All</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="sr-field">
                            <label>Host Statuses</label>
                            <div class="sr-checkbox-group" id="edit-sr-host-statuses">
                                <label><input type="checkbox" value="up" checked> Up</label>
                                <label><input type="checkbox" value="down" checked> Down</label>
                                <label><input type="checkbox" value="unreachable" checked> Unreach</label>
                            </div>
                        </div>
                        <div class="sr-field">
                            <label>Service Statuses</label>
                            <div class="sr-checkbox-group" id="edit-sr-svc-statuses">
                                <label><input type="checkbox" value="ok" checked> OK</label>
                                <label><input type="checkbox" value="warning" checked> Warn</label>
                                <label><input type="checkbox" value="critical" checked> Crit</label>
                                <label><input type="checkbox" value="unknown" checked> Unk</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="sr-field">
                        <label>
                            <input type="checkbox" id="edit-sr-save-to-server" checked> Save report to server
                        </label>
                        <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                            Reports will be accessible from the Reports page
                        </small>
                    </div>
                    
                    <div id="edit-sr-status" class="sr-status"></div>
                    <div class="sr-actions">
                        <button type="button" id="edit-sr-cancel" class="generate-btn">Cancel</button>
                        <button type="submit" id="edit-sr-save" class="generate-btn">Update Report</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Saved Reports Modal -->
    <div id="savedReportsModal" class="sr-modal">
        <div class="sr-modal-content" style="min-width: 600px; max-width: 800px;">
            <span class="sr-close">×</span>
            <h2>Saved Reports</h2>
            <div id="saved-reports-container" class="saved-reports-container">
                <div class="saved-reports-loading">Loading saved reports...</div>
            </div>
        </div>
    </div>

    <!-- Save Report Name Modal -->
    <div id="saveReportNameModal" class="sr-modal">
        <div class="sr-modal-content">
            <span class="sr-close">×</span>
            <h2>Save Report</h2>
            <form id="save-report-form">
                <div class="sr-field">
                    <label for="report-name">Report Name (Optional)</label>
                    <input type="text" id="report-name" placeholder="Enter a custom name for this report">
                    <small style="color: #777; font-size: 0.8em; margin-top: 2px; display: block;">
                        Leave blank to use default naming
                    </small>
                </div>
                <div class="sr-actions">
                    <button type="button" id="save-report-cancel" class="generate-btn">Cancel</button>
                    <button type="submit" id="save-report-confirm" class="generate-btn">Save Report</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Filters Modal -->
    <div id="filtersModal" class="sr-modal">
        <div class="sr-modal-content" style="min-width: 600px; max-width: 800px;">
            <span class="sr-close">×</span>
            <h2>Report Filters</h2>
            <form id="filters-form">
                <div class="form-row">
                    <div class="sr-field">
                        <label>Quick Time Range</label>
                        <div class="time-shortcuts">
                            <button type="button" class="time-shortcut-btn" data-days="1">Last 24h</button>
                            <button type="button" class="time-shortcut-btn" data-days="7">Last 7 days</button>
                            <button type="button" class="time-shortcut-btn" data-days="30">Last 30 days</button>
                            <button type="button" class="time-shortcut-btn" data-days="90">Last 90 days</button>
                            <button type="button" class="time-shortcut-btn" data-days="365">Last year</button>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="sr-field">
                        <label for="filter-start">Start Date & Time</label>
                        <input type="datetime-local" id="filter-start">
                    </div>
                    <div class="sr-field">
                        <label for="filter-end">End Date & Time</label>
                        <input type="datetime-local" id="filter-end">
                    </div>
                </div>

                <div class="form-row">
                    <div class="sr-field">
                        <label for="filter-type">Report Type</label>
                        <select id="filter-type">
                            <option value="hostgroups">Hosts</option>
                            <option value="services">Services</option>
                        </select>
                    </div>
                    <div class="sr-field" id="filter-entity-wrapper">
                        <label for="filter-entity" id="filter-entity-label">Hostgroup</label>
                        <select id="filter-entity">
                            <option value="all">All</option>
                        </select>
                    </div>
                </div>

                <div class="form-row" id="filter-service-description-wrapper" style="display: none;">
                    <div class="sr-field">
                        <label for="filter-service-description">Service Description</label>
                        <select id="filter-service-description">
                            <option value="all">All</option>
                        </select>
                    </div>
                </div>

                <div class="sr-actions">
                    <button type="button" id="filters-cancel" class="generate-btn">Cancel</button>
                    <button type="submit" id="filters-apply" class="generate-btn">Apply Filters</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Hamburger toggle script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const hamburger = document.querySelector('.hamburger');
            const headerButtons = document.querySelector('.header-buttons');
            if (hamburger) {
                hamburger.addEventListener('click', function(e) {
                    e.stopPropagation();
                    this.classList.toggle('active');
                    headerButtons.classList.toggle('active');
                });
                document.addEventListener('click', function(event) {
                    if (!event.target.closest('.header-content')) {
                        hamburger.classList.remove('active');
                        headerButtons.classList.remove('active');
                    }
                });
            }
        });

        
        // Initialize the translator
        const dictionaries = <?php echo json_encode($dictionaries); ?>;
        const selectedLang = '<?php echo $selectedLang; ?>';
        const translator = new Translator(dictionaries, selectedLang);
        translator.init();
        initTranslationObserver(translator);
        
        // Handle View dropdown
        const viewDropdownBtn = document.getElementById('viewDropdownBtn');
        const viewDropdownContent = document.getElementById('viewDropdownContent');
        if(viewDropdownBtn && viewDropdownContent){
            viewDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                // Close other dropdowns first
                const menuDropdownContent = document.getElementById('menuDropdownContent');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
                viewDropdownContent.classList.toggle('show');
            });
        }
        
        // Handle Control Panel dropdown
        const menuDropdownBtn = document.getElementById('menuDropdownBtn');
        const menuDropdownContent = document.getElementById('menuDropdownContent');
        if(menuDropdownBtn && menuDropdownContent){
            menuDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                // Close other dropdowns first
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                menuDropdownContent.classList.toggle('show');
            });
        }
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e){
            if(!e.target.closest('.header-dropdown')){
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
            }
        });
    </script>
</body>
</html>
