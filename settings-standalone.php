<?php
require_once 'redirect_to_view.php';
include 'language_selector.php';
include 'theme_loader.php'; // Include the theme loader
require_once 'loadenv.php';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blesk - Settings</title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/settings.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <script src="functions/jquery-3.6.0.min.js"></script>
    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <style>
        /* Override modal styles to make it always visible */
        .settings-modal {
            display: block !important;
            position: relative !important;
            z-index: auto !important;
            background-color: transparent !important;
            padding: 0 !important;
            margin: 0 !important;
            width: 100% !important;
            height: 100vh !important;
            overflow: auto !important;
        }
        
        .settings-modal-content {
            position: relative !important;
            background-color: var(--background) !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            border-radius: 0 !important;
            width: 100% !important;
            max-width: none !important;
            height: 100vh !important;
            max-height: none !important;
            box-shadow: none !important;
            animation: none !important;
        }
        
        body {
            margin: 0;
            padding: 0 0 30px 0;
            overflow: hidden;
        }
        
        /* Adjust header to be part of the page */
        .settings-modal-header {
            background-color: var(--surface);
            border-bottom: 1px solid var(--border);
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        /* Hide the close button since this is a standalone page */
        .settings-modal-close {
            display: none !important;
        }
    </style>
</head>

<body>
    <!-- Create dummy elements that settingsDOMLoaded.js expects to prevent null reference errors -->
    <div id="openSettingsBtn" style="display: none;"></div>
    
    <!-- Include the settings modal content directly -->
    <?php include "settingsModal.php"; ?>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize translation system
            const dictionaries = <?php echo json_encode($dictionaries); ?>;
            const selectedLang = '<?php echo $selectedLang; ?>';
            const translator = new Translator(dictionaries, selectedLang);
            translator.init();
            initTranslationObserver(translator);
            
            // Force the modal to be visible and remove any modal-specific behaviors
            const modal = document.getElementById('settingsModal');
            if (modal) {
                modal.classList.add('show');
                modal.style.display = 'block';
            }
            
            // Remove any click handlers that might close the modal
            const closeBtn = document.getElementById('closeModalBtn');
            if (closeBtn) {
                closeBtn.style.display = 'none';
            }
            
            // Ensure the system information tab is properly activated to trigger dynamic updates
            setTimeout(() => {
                const informationTab = document.querySelector('.sub-tab-link[data-target="general-system-information"]');
                const informationPane = document.getElementById('general-system-information');
                
                if (informationTab && !informationTab.classList.contains('active')) {
                    informationTab.classList.add('active');
                }
                
                if (informationPane && !informationPane.classList.contains('active')) {
                    informationPane.classList.add('active');
                }
                
                // Trigger system stats updates manually if needed
                if (typeof startSystemStatsUpdates === 'function') {
                    startSystemStatsUpdates();
                }
            }, 100);
        });
    </script>
</body>

</html>
