<?php
function loadEnv(string $path): void {
    if (!file_exists($path)) {
        die("Error: .env file not found at $path");
    }

    // Read the file line by line
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

    foreach ($lines as $line) {
        // Skip comments (lines starting with #)
        if (strpos(trim($line), '#') === 0) {
            continue;
        }

        // Match key-value pairs
        if (preg_match('/^\s*([\w.-]+)\s*=\s*(.*)\s*$/', $line, $matches)) {
            $key = $matches[1];
            $value = trim($matches[2]);

            // Remove surrounding quotes if present
            if (preg_match('/^"(.*)"$|^\'(.*)\'$/', $value, $quoteMatches)) {
                $value = $quoteMatches[1] ?? $quoteMatches[2];
            }

            // Set the environment variable
            $_ENV[$key] = $value;
        }
    }
}

// Load the .env file
loadEnv('/etc/bubblemaps/.env');
?>