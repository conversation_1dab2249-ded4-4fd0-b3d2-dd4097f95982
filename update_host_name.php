<?php
header('Content-Type: application/json');
include "loadenv.php";

// Function to get database connection for bubblemaps
function getBubblemapsConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        throw new Exception("Bubblemaps connection failed: " . $conn->connect_error);
    }
    return $conn;
}

// Function to get database connection for NagiosQL
function getNagiosqlConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "db_nagiosql_v3";

    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        throw new Exception("NagiosQL connection failed: " . $conn->connect_error);
    }
    return $conn;
}

// Function to get database connection for admin user (blesk database)
function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "blesk";

    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        throw new Exception("Admin DB connection failed: " . $conn->connect_error);
    }
    return $conn;
}

// Function to get user credentials from blesk database
function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();
    
    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "tfUsername" => $row["username"],
            "tfPassword" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }
    
    $conn->close();
    return $userCredentials;
}

// Function to get self IP address
function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        throw new Exception("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

// Function to get real hostname from Nagios by IP
function getHostnameByIP($ip) {
    try {
        // Get credentials from the database
        $credentials = getUserCredentials();
        $hostname = getSelfIp();
        
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials");
        }

        $nagiosUrl = "https://{$hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true";
        
        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $nagiosUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // Disable SSL verification
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        // Set HTTP Basic Authentication credentials
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        
        // Execute request
        $response = curl_exec($ch);
        
        // Handle cURL errors
        if ($response === false) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            throw new Exception("cURL error: " . $errorMsg);
        }
        
        // Check HTTP status code
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode != 200) {
            throw new Exception("HTTP error! Status: {$httpCode}");
        }
        
        // Parse JSON response
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON parse error: " . json_last_error_msg());
        }
        
        // Search for matching IP in hostlist
        if (isset($data['data']['hostlist'])) {
            foreach ($data['data']['hostlist'] as $host) {
                if (isset($host['address']) && $host['address'] === $ip) {
                    return $host['name'];
                }
            }
        }
        
        return null;
        
    } catch (Exception $e) {
        error_log('Error fetching host data from Nagios: ' . $e->getMessage());
        throw $e;
    }
}

// Function to validate hostname (letters, numbers, hyphens, underscores, and dots)
function validateHostname($hostname) {
    // Check if hostname is empty
    if (empty(trim($hostname))) {
        return false;
    }
    
    // Check length (reasonable limits)
    if (strlen($hostname) < 1 || strlen($hostname) > 63) {
        return false;
    }
    
    // Only allow letters, numbers, hyphens, underscores, and dots
    if (!preg_match('/^[a-zA-Z0-9_.-]+$/', $hostname)) {
        return false;
    }
    
    // Cannot start or end with hyphen or dot
    if (substr($hostname, 0, 1) === '-' || substr($hostname, -1) === '-' ||
        substr($hostname, 0, 1) === '.' || substr($hostname, -1) === '.') {
        return false;
    }
    
    // Cannot have consecutive dots
    if (strpos($hostname, '..') !== false) {
        return false;
    }
    
    return true;
}

// Function to check if hostname exists in bubblemaps database
function checkHostnameInBubblemaps($hostname, $excludeId = null) {
    $conn = getBubblemapsConnection();
    
    $sql = "SELECT id FROM hosts WHERE hostname = ?";
    $params = [$hostname];
    $types = "s";
    
    if ($excludeId !== null) {
        $sql .= " AND id != ?";
        $params[] = $excludeId;
        $types .= "i";
    }
    
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        $conn->close();
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    $exists = $result->num_rows > 0;
    
    $stmt->close();
    $conn->close();
    
    return $exists;
}

// Function to check if hostname exists in NagiosQL database
function checkHostnameInNagiosql($hostname, $excludeId = null) {
    $conn = getNagiosqlConnection();
    
    $sql = "SELECT id FROM tbl_host WHERE host_name = ?";
    $params = [$hostname];
    $types = "s";
    
    if ($excludeId !== null) {
        $sql .= " AND id != ?";
        $params[] = $excludeId;
        $types .= "i";
    }
    
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        $conn->close();
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    $exists = $result->num_rows > 0;
    
    $stmt->close();
    $conn->close();
    
    return $exists;
}

// Function to get current host data from bubblemaps
function getCurrentHostData($id) {
    $conn = getBubblemapsConnection();
    
    $sql = "SELECT hostname, ip FROM hosts WHERE id = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        $conn->close();
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $stmt->close();
        $conn->close();
        throw new Exception("No record found with the given ID.");
    }
    
    $data = $result->fetch_assoc();
    $stmt->close();
    $conn->close();
    
    return $data;
}

// Function to update hostname in bubblemaps database
function updateHostnameInBubblemaps($id, $newName) {
    $conn = getBubblemapsConnection();
    
    $sql = "UPDATE hosts SET hostname = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        $conn->close();
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param('si', $newName, $id);
    $success = $stmt->execute();
    
    if (!$success) {
        $stmt->close();
        $conn->close();
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
    $stmt->close();
    $conn->close();
    
    return true;
}

// Function to update hostname in NagiosQL database
function updateHostnameInNagiosql($oldName, $newName) {
    $conn = getNagiosqlConnection();
    
    // Update both host_name and display_name fields
    $sql = "UPDATE tbl_host SET host_name = ?, display_name = ? WHERE host_name = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        $conn->close();
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param('sss', $newName, $newName, $oldName);
    $success = $stmt->execute();
    
    if (!$success) {
        $stmt->close();
        $conn->close();
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
    $affectedRows = $stmt->affected_rows;
    $stmt->close();
    $conn->close();
    
    return $affectedRows;
}

// Function to delete old host configuration file
function deleteOldHostConfigFile($oldHostname) {
    try {
        $configFilePath = "/etc/nagiosql/hosts/" . $oldHostname . ".cfg";
        
        // Check if file exists
        if (!file_exists($configFilePath)) {
            error_log("Old host config file does not exist: " . $configFilePath);
            return false; // Not an error, just no file to delete
        }
        
        // Check if file is writable (or if we have permission to delete)
        if (!is_writable($configFilePath)) {
            error_log("Old host config file is not writable: " . $configFilePath);
            throw new Exception("Cannot delete old host config file - insufficient permissions: " . $configFilePath);
        }
        
        // Delete the file
        if (unlink($configFilePath)) {
            error_log("Successfully deleted old host config file: " . $configFilePath);
            return true;
        } else {
            error_log("Failed to delete old host config file: " . $configFilePath);
            throw new Exception("Failed to delete old host config file: " . $configFilePath);
        }
        
    } catch (Exception $e) {
        error_log("Error deleting old host config file: " . $e->getMessage());
        throw new Exception("Error deleting old host config file: " . $e->getMessage());
    }
}

// Function to execute doit.php script
function executeDoitScript() {
    try {
        // Get the current directory and construct the path to doit.php
        $doitPath = __DIR__ . '/doit.php';
        
        if (!file_exists($doitPath)) {
            error_log("doit.php script not found at: " . $doitPath);
            throw new Exception("doit.php script not found at: " . $doitPath);
        }
        
        // Log the execution attempt
        error_log("Executing doit.php script: " . $doitPath);
        
        // Execute the script and capture output
        $output = shell_exec("php " . escapeshellarg($doitPath) . " 2>&1");
        
        if ($output === null) {
            error_log("Failed to execute doit.php script - shell_exec returned null");
            throw new Exception("Failed to execute doit.php script");
        }
        
        // Log the output for debugging
        error_log("doit.php output: " . $output);
        
        return $output;
    } catch (Exception $e) {
        error_log("Error executing doit.php: " . $e->getMessage());
        throw new Exception("Error executing doit.php: " . $e->getMessage());
    }
}

try {
    // Log the start of the operation
    error_log("Hostname update request started - ID: " . ($_POST['id'] ?? 'not set') . ", New Name: " . ($_POST['newName'] ?? 'not set'));
    
    // Validate input parameters
    if (!isset($_POST['id']) || !isset($_POST['newName'])) {
        throw new Exception("Missing required parameters: id and newName");
    }
    
    $id = intval($_POST['id']);
    $newName = trim($_POST['newName']);
    
    if ($id <= 0) {
        throw new Exception("Invalid host ID");
    }
    
    // Validate hostname format
    if (!validateHostname($newName)) {
        throw new Exception("Invalid hostname format. Only letters, numbers, hyphens (-), underscores (_), and dots (.) are allowed. Cannot start or end with hyphen or dot, and cannot have consecutive dots.");
    }
    
    // Get current host data
    $currentData = getCurrentHostData($id);
    $oldName = $currentData['hostname'];
    $ip = $currentData['ip'];
    
    error_log("Current host data - ID: $id, Old Name: $oldName, New Name: $newName, IP: $ip");
    
    // Get the real hostname from Nagios using the IP
    $realHostnameFromNagios = null;
    try {
        $realHostnameFromNagios = getHostnameByIP($ip);
        error_log("Real hostname from Nagios for IP $ip: " . ($realHostnameFromNagios ?? 'null'));
    } catch (Exception $e) {
        error_log("Warning: Could not get real hostname from Nagios: " . $e->getMessage());
        // Continue without the real hostname - we'll use the stored hostname for comparison
    }
    
    // If the new name is the same as the old name, no need to update
    if ($newName === $oldName) {
        error_log("Hostname unchanged - no update needed");
        echo json_encode([
            'success' => true,
            'message' => 'Hostname unchanged',
            'data' => [
                'oldName' => $oldName,
                'newName' => $newName,
                'ip' => $ip,
                'realHostnameFromNagios' => $realHostnameFromNagios
            ]
        ]);
        exit;
    }
    
    // Check for duplicates in bubblemaps database
    if (checkHostnameInBubblemaps($newName, $id)) {
        error_log("Duplicate hostname found in bubblemaps database: $newName");
        throw new Exception("Hostname '$newName' already exists in bubblemaps database");
    }
    
    // Check for duplicates in NagiosQL database using the real hostname from Nagios
    // If we have a real hostname from Nagios, use it for comparison
    $hostnameToCheckInNagiosql = $realHostnameFromNagios ?? $oldName;
    
    if (checkHostnameInNagiosql($newName)) {
        error_log("Duplicate hostname found in NagiosQL database: $newName");
        throw new Exception("Hostname '$newName' already exists in NagiosQL database");
    }
    
    // Also check if the new name conflicts with the real hostname from Nagios
    if ($realHostnameFromNagios && $newName === $realHostnameFromNagios) {
        error_log("New hostname matches real hostname from Nagios: $newName");
        throw new Exception("Hostname '$newName' matches the real hostname from Nagios. No update needed.");
    }
    
    error_log("No duplicates found - proceeding with update");
    
    // Start transaction-like operations
    $bubblemapsUpdated = false;
    $nagiosqlUpdated = false;
    
    try {
        // Update bubblemaps database
        error_log("Updating bubblemaps database...");
        updateHostnameInBubblemaps($id, $newName);
        $bubblemapsUpdated = true;
        error_log("Bubblemaps database updated successfully");
        
        // Update NagiosQL database using the real hostname from Nagios
        error_log("Updating NagiosQL database...");
        $hostnameToUpdateInNagiosql = $realHostnameFromNagios ?? $oldName;
        $affectedRows = updateHostnameInNagiosql($hostnameToUpdateInNagiosql, $newName);
        $nagiosqlUpdated = true;
        error_log("NagiosQL database updated successfully - affected rows: $affectedRows (updated from: $hostnameToUpdateInNagiosql)");
        
        // Delete old host configuration file
        error_log("Deleting old host configuration file...");
        $configFileDeleted = deleteOldHostConfigFile($hostnameToUpdateInNagiosql);
        error_log("Old host config file deletion result: " . ($configFileDeleted ? 'success' : 'no file to delete'));
        
        // Execute doit.php script
        error_log("Executing doit.php script...");
        $doitOutput = executeDoitScript();
        error_log("doit.php script executed successfully");
        
        error_log("Hostname update completed successfully - $oldName -> $newName");
        
        echo json_encode([
            'success' => true,
            'message' => 'Hostname updated successfully',
            'data' => [
                'oldName' => $oldName,
                'newName' => $newName,
                'ip' => $ip,
                'realHostnameFromNagios' => $realHostnameFromNagios,
                'hostnameUsedForNagiosqlUpdate' => $hostnameToUpdateInNagiosql,
                'bubblemapsUpdated' => $bubblemapsUpdated,
                'nagiosqlUpdated' => $nagiosqlUpdated,
                'nagiosqlAffectedRows' => $affectedRows,
                'configFileDeleted' => $configFileDeleted,
                'doitOutput' => $doitOutput
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Update operation failed: " . $e->getMessage());
        // If we updated bubblemaps but failed elsewhere, we could rollback here
        // For now, we'll just report the error
        throw new Exception("Update failed: " . $e->getMessage());
    }
    
} catch (Exception $e) {
    error_log("Hostname update error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
