<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Subnet Mode Detection</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { margin-top: 10px; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Subnet Mode Detection Test</h1>
    
    <div class="test-case">
        <h3>Test Case 1: Regular All Hosts View</h3>
        <p>URL: <code>hosts.php?subnet=all&subnetNickname=All Hosts&infra=PRIVAL</code></p>
        <button onclick="testUrl('?subnet=all&subnetNickname=All Hosts&infra=PRIVAL', 1)">Test</button>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 2: Subnet Mode</h3>
        <p>URL: <code>hosts.php?subnet=192.168.110&subnetNickname=Test Subnet&infra=PRIVAL&subnet=true</code></p>
        <button onclick="testUrl('?subnet=192.168.110&subnetNickname=Test Subnet&infra=PRIVAL&subnet=true', 2)">Test</button>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 3: Hostgroup View</h3>
        <p>URL: <code>hosts.php?hostgroup=Windows Servers&subnet=192.168.110&subnetNickname=Test Subnet&infra=PRIVAL&subnet=true</code></p>
        <button onclick="testUrl('?hostgroup=Windows Servers&subnet=192.168.110&subnetNickname=Test Subnet&infra=PRIVAL&subnet=true', 3)">Test</button>
        <div id="result3" class="result"></div>
    </div>

    <div class="test-case">
        <h3>Test JSON Structure</h3>
        <button onclick="testJsonStructure()">Test JSON Structure Fix</button>
        <div id="result4" class="result"></div>
    </div>

    <script>
        function testUrl(queryString, testNumber) {
            // Simulate the URL parameters
            const urlParams = new URLSearchParams(queryString);
            const hostgroup = urlParams.get('hostgroup');
            const subnet = urlParams.get('subnet');
            
            // Apply the same logic as in fetchHostsBubbles.js
            const urlString = queryString;
            const isSubnetMode = urlString.includes('subnet=true');
            const subnetName = isSubnetMode && subnet && subnet !== 'all' && subnet !== 'true' ? subnet : null;
            
            const resultDiv = document.getElementById(`result${testNumber}`);
            
            let result = `
                <strong>Detection Results:</strong><br>
                - hostgroup: ${hostgroup || 'null'}<br>
                - subnet: ${subnet || 'null'}<br>
                - isSubnetMode: ${isSubnetMode}<br>
                - subnetName: ${subnetName || 'null'}<br>
            `;
            
            // Determine expected behavior
            let expected = '';
            let isCorrect = false;
            
            switch(testNumber) {
                case 1:
                    expected = 'Should NOT be in subnet mode (isSubnetMode: false, subnetName: null)';
                    isCorrect = !isSubnetMode && !subnetName;
                    break;
                case 2:
                    expected = 'Should be in subnet mode (isSubnetMode: true, subnetName: 192.168.110)';
                    isCorrect = isSubnetMode && subnetName === '192.168.110';
                    break;
                case 3:
                    expected = 'Should be in subnet mode with hostgroup (isSubnetMode: true, subnetName: 192.168.110)';
                    isCorrect = isSubnetMode && subnetName === '192.168.110' && hostgroup === 'Windows Servers';
                    break;
            }
            
            result += `<br><strong>Expected:</strong> ${expected}<br>`;
            result += `<strong>Status:</strong> ${isCorrect ? 'PASS' : 'FAIL'}`;
            
            resultDiv.innerHTML = result;
            resultDiv.className = `result ${isCorrect ? 'success' : 'error'}`;
        }

        async function testJsonStructure() {
            const resultDiv = document.getElementById('result4');
            resultDiv.innerHTML = 'Testing JSON structure...';
            resultDiv.className = 'result info';
            
            try {
                // Test the get endpoint
                const response = await fetch('functions/hostlistPhpFunctions/get_hostgroup_positions.php?infra=TEST_INFRA');
                const data = await response.json();
                
                let result = '<strong>JSON Structure Test:</strong><br>';
                result += `Response status: ${response.status}<br>`;
                result += `Data structure: ${JSON.stringify(data, null, 2)}<br>`;
                
                // Test with subnet parameter
                const subnetResponse = await fetch('functions/hostlistPhpFunctions/get_hostgroup_positions.php?infra=TEST_INFRA&subnet=192.168.1');
                const subnetData = await subnetResponse.json();
                
                result += `<br><strong>Subnet-specific test:</strong><br>`;
                result += `Response status: ${subnetResponse.status}<br>`;
                result += `Data structure: ${JSON.stringify(subnetData, null, 2)}`;
                
                resultDiv.innerHTML = result;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
