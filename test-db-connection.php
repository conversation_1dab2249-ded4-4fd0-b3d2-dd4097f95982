<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include the NDD classes
$ndd_path = '/var/www/html/ndd';
include_once $ndd_path . '/include/init.php';

// Autoload the required classes
spl_autoload_register(function ($class) use ($ndd_path) {
    $class_file = $ndd_path . '/include/class.' . $class . '.php';
    if (file_exists($class_file)) {
        include $class_file;
    }
});

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit;
}

$dbtype = $input['dbtype'] ?? '';
$host = $input['host'] ?? '';
$port = $input['port'] ?? '';
$dbname = $input['dbname'] ?? '';
$username = $input['username'] ?? '';
$password = $input['password'] ?? '';
$domain = $input['domain'] ?? '';

if (empty($dbtype) || empty($host) || empty($username)) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

function testConnection($dbtype, $host, $port, $dbname, $username, $password, $domain = '') {
    try {
        // Prepare the info array for the Auth classes
        $info = [
            'dbtype' => $dbtype,
            'host' => $host,
            'port' => $port,
            'dbname' => $dbname,
            'username' => $username,
            'password' => $password,
            'domain' => $domain
        ];
        
        switch (strtolower($dbtype)) {
            case 'mysql':
                $info['port'] = $port ?: '3306';
                $info['dbname'] = $dbname ?: 'information_schema';
                
                $ApmAuthMysql = new ApmAuthMysql();
                $result = $ApmAuthMysql->testMysqlPlugin($info);
                
                if ($result === true) {
                    return ['success' => true, 'message' => 'MySQL connection successful'];
                } else {
                    return ['success' => false, 'message' => 'MySQL connection failed - check credentials and connectivity'];
                }
                
            case 'mssql':
            case 'sybase':
                $info['port'] = $port ?: '1433';
                if (empty($dbname)) {
                    return ['success' => false, 'message' => 'Database/Instance name is required for MSSQL/Sybase'];
                }
                
                $ApmAuthFreetds = new ApmAuthFreetds();
                
                // Try without domain first
                $result = $ApmAuthFreetds->testMssqlPluginNoDomainWithResults($info);
                if (preg_match('/^Success/', $result)) {
                    return ['success' => true, 'message' => 'MSSQL/Sybase connection successful (no domain)'];
                }
                
                // If domain is provided and no-domain test failed, try with domain
                if (!empty($domain)) {
                    $result = $ApmAuthFreetds->testMssqlPluginWithResults($info);
                    if (preg_match('/^Success/', $result)) {
                        return ['success' => true, 'message' => 'MSSQL/Sybase connection successful (with domain)'];
                    }
                }
                
                // Clean up the error message for user display
                $errorMsg = $result;
                if (preg_match('/^Error: (.+)/', $result, $matches)) {
                    $errorMsg = $matches[1];
                }
                
                return ['success' => false, 'message' => 'MSSQL/Sybase connection failed: ' . $errorMsg];
                
            case 'oracle':
                $info['port'] = $port ?: '1521';
                if (empty($dbname)) {
                    return ['success' => false, 'message' => 'Database/Instance name is required for Oracle'];
                }
                
                $ApmAuthOracle = new ApmAuthOracle();
                $result = $ApmAuthOracle->testOraclePluginWithResults($info);
                
                if (preg_match('/^Success/', $result)) {
                    return ['success' => true, 'message' => 'Oracle connection successful'];
                }
                
                // Clean up the error message for user display
                $errorMsg = $result;
                if (preg_match('/^Error: (.+)/', $result, $matches)) {
                    $errorMsg = $matches[1];
                }
                
                return ['success' => false, 'message' => 'Oracle connection failed: ' . $errorMsg];
                
            default:
                return ['success' => false, 'message' => 'Unsupported database type'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Connection test failed: ' . $e->getMessage()];
    }
}

$result = testConnection($dbtype, $host, $port, $dbname, $username, $password, $domain);
echo json_encode($result);
?>
